import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { useLikesStore } from '@/stores/likesStore';
import { useSafeAsync } from '@/hooks/useSafeAsync';

/**
 * Debug component to test that state updates are properly handled
 * and don't cause React lifecycle errors
 */
export default function StateUpdateTest() {
  const [testResults, setTestResults] = useState<string[]>([]);
  const { safeAsync, safeSetState } = useSafeAsync();
  const { fetchReceivedLikes, isLoading, error } = useLikesStore();

  const addResult = (result: string) => {
    safeSetState(setTestResults, prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const testAsyncOperation = async () => {
    addResult('Starting async test...');
    
    await safeAsync(
      async () => {
        // Simulate async operation
        await new Promise(resolve => setTimeout(resolve, 1000));
        return 'success';
      },
      (result) => {
        addResult(`Async operation completed: ${result}`);
      },
      (error) => {
        addResult(`Async operation failed: ${error.message}`);
      }
    );
  };

  const testStoreOperation = async () => {
    addResult('Testing store operation...');
    
    try {
      await fetchReceivedLikes(true);
      addResult('Store operation completed successfully');
    } catch (error) {
      addResult(`Store operation failed: ${error}`);
    }
  };

  const testRapidStateUpdates = () => {
    addResult('Testing rapid state updates...');
    
    // Test multiple rapid state updates
    for (let i = 0; i < 5; i++) {
      setTimeout(() => {
        safeSetState(setTestResults, prev => [...prev, `Rapid update ${i + 1}`]);
      }, i * 100);
    }
  };

  const clearResults = () => {
    safeSetState(setTestResults, []);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>State Update Test</Text>
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.button} onPress={testAsyncOperation}>
          <Text style={styles.buttonText}>Test Async</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.button} onPress={testStoreOperation}>
          <Text style={styles.buttonText}>Test Store</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.button} onPress={testRapidStateUpdates}>
          <Text style={styles.buttonText}>Test Rapid Updates</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={[styles.button, styles.clearButton]} onPress={clearResults}>
          <Text style={styles.buttonText}>Clear</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.statusContainer}>
        <Text style={styles.statusText}>
          Loading: {isLoading ? 'Yes' : 'No'}
        </Text>
        {error && (
          <Text style={styles.errorText}>
            Error: {error}
          </Text>
        )}
      </View>

      <View style={styles.resultsContainer}>
        <Text style={styles.resultsTitle}>Test Results:</Text>
        {testResults.map((result, index) => (
          <Text key={index} style={styles.resultText}>
            {result}
          </Text>
        ))}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: 'white',
    margin: 10,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
    marginBottom: 15,
  },
  button: {
    backgroundColor: '#8B5CF6',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 5,
    marginBottom: 5,
  },
  clearButton: {
    backgroundColor: '#EF4444',
  },
  buttonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  statusContainer: {
    marginBottom: 15,
    padding: 10,
    backgroundColor: '#F3F4F6',
    borderRadius: 5,
  },
  statusText: {
    fontSize: 14,
    marginBottom: 5,
  },
  errorText: {
    fontSize: 14,
    color: '#EF4444',
  },
  resultsContainer: {
    maxHeight: 200,
  },
  resultsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
  },
  resultText: {
    fontSize: 12,
    marginBottom: 3,
    color: '#374151',
  },
});
