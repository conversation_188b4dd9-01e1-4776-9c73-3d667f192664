import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import * as Haptics from 'expo-haptics';
import {
  ArrowLeft,
  Play,
  CheckCircle,
  XCircle,
  Clock,
  User,
  Settings,
  Shield,
  Bell,
  Camera,
  Award,
  Info,
} from 'lucide-react-native';

import { theme } from '../../constants/theme';
import { useProfileStore } from '../../stores/profileStore';

export default function ProfileTestSuite() {
  const router = useRouter();
  const { 
    profile, 
    settings, 
    loadProfile, 
    updateProfile, 
    updateSettings,
    uploadPhoto,
    deletePhoto,
    isLoading,
    error 
  } = useProfileStore();
  
  const [testResults, setTestResults] = useState<Record<string, 'pending' | 'success' | 'error'>>({});
  const [isRunning, setIsRunning] = useState(false);

  const handleBack = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    router.back();
  };

  const runTest = async (testName: string, testFunction: () => Promise<void>) => {
    setTestResults(prev => ({ ...prev, [testName]: 'pending' }));
    
    try {
      await testFunction();
      setTestResults(prev => ({ ...prev, [testName]: 'success' }));
    } catch (error) {
      console.error(`Test ${testName} failed:`, error);
      setTestResults(prev => ({ ...prev, [testName]: 'error' }));
    }
  };

  const testProfileLoad = async () => {
    await loadProfile();
    if (!profile) throw new Error('Profile not loaded');
  };

  const testProfileUpdate = async () => {
    if (!profile) throw new Error('Profile not available');
    
    const originalBio = profile.bio;
    const testBio = 'Test bio update';
    
    await updateProfile({ bio: testBio });
    
    // Verify update
    if (profile.bio !== testBio) {
      throw new Error('Profile update failed');
    }
    
    // Restore original
    await updateProfile({ bio: originalBio });
  };

  const testSettingsUpdate = async () => {
    if (!settings) throw new Error('Settings not available');
    
    const originalValue = settings.notifications.pushNotifications;
    const testValue = !originalValue;
    
    await updateSettings({
      notifications: {
        ...settings.notifications,
        pushNotifications: testValue,
      },
    });
    
    // Verify update
    if (settings.notifications.pushNotifications !== testValue) {
      throw new Error('Settings update failed');
    }
    
    // Restore original
    await updateSettings({
      notifications: {
        ...settings.notifications,
        pushNotifications: originalValue,
      },
    });
  };

  const testPhotoUpload = async () => {
    // Mock photo upload
    const mockPhoto = {
      uri: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      type: 'image/jpeg',
      name: 'test-photo.jpg',
      isMain: false,
      order: profile?.photos.length || 0,
    };
    
    const photoId = await uploadPhoto(mockPhoto);
    if (!photoId) throw new Error('Photo upload failed');
    
    // Clean up - delete the test photo
    await deletePhoto(photoId);
  };

  const testNavigationFlow = async () => {
    // Test navigation to different profile screens
    const screens = [
      '/profile/edit',
      '/profile/settings',
      '/profile/notifications',
      '/profile/photos',
      '/profile/account',
      '/profile/privacy',
      '/profile/preferences',
      '/profile/verification',
      '/profile/about',
    ];
    
    for (const screen of screens) {
      try {
        router.push(screen as any);
        // Small delay to simulate navigation
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        throw new Error(`Navigation to ${screen} failed`);
      }
    }
    
    // Return to profile
    router.push('/(tabs)/profile');
  };

  const testDataPersistence = async () => {
    if (!profile) throw new Error('Profile not available');
    
    // Test that profile data persists across app sessions
    const testData = {
      bio: 'Persistence test bio',
      interests: ['Test Interest 1', 'Test Interest 2'],
    };
    
    await updateProfile(testData);
    
    // Reload profile to simulate app restart
    await loadProfile();
    
    // Verify data persisted
    if (profile.bio !== testData.bio) {
      throw new Error('Profile data persistence failed');
    }
  };

  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults({});
    
    const tests = [
      { name: 'Profile Load', test: testProfileLoad },
      { name: 'Profile Update', test: testProfileUpdate },
      { name: 'Settings Update', test: testSettingsUpdate },
      { name: 'Photo Upload', test: testPhotoUpload },
      { name: 'Navigation Flow', test: testNavigationFlow },
      { name: 'Data Persistence', test: testDataPersistence },
    ];
    
    for (const { name, test } of tests) {
      await runTest(name, test);
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    setIsRunning(false);
    
    const results = Object.values(testResults);
    const successCount = results.filter(r => r === 'success').length;
    const totalCount = results.length;
    
    Alert.alert(
      'Test Results',
      `${successCount}/${totalCount} tests passed`,
      [{ text: 'OK' }]
    );
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle size={20} color={theme.colors.success} />;
      case 'error':
        return <XCircle size={20} color={theme.colors.error} />;
      case 'pending':
        return <Clock size={20} color={theme.colors.warning} />;
      default:
        return <Clock size={20} color={theme.colors.gray400} />;
    }
  };

  const TestItem = ({ 
    icon: Icon, 
    title, 
    description, 
    testName, 
    onPress 
  }: {
    icon: any;
    title: string;
    description: string;
    testName: string;
    onPress: () => void;
  }) => (
    <TouchableOpacity style={styles.testItem} onPress={onPress}>
      <View style={styles.testLeft}>
        <View style={styles.testIcon}>
          <Icon size={20} color={theme.colors.primary} />
        </View>
        <View style={styles.testContent}>
          <Text style={styles.testTitle}>{title}</Text>
          <Text style={styles.testDescription}>{description}</Text>
        </View>
      </View>
      {getStatusIcon(testResults[testName])}
    </TouchableOpacity>
  );

  return (
    <LinearGradient colors={['#8B5CF6', '#EC4899']} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <ArrowLeft size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Profile Test Suite</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Run All Tests */}
          <View style={styles.section}>
            <TouchableOpacity 
              style={[styles.runAllButton, isRunning && styles.runAllButtonDisabled]} 
              onPress={runAllTests}
              disabled={isRunning}
            >
              <Play size={20} color="white" />
              <Text style={styles.runAllButtonText}>
                {isRunning ? 'Running Tests...' : 'Run All Tests'}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Individual Tests */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Individual Tests</Text>
            
            <TestItem
              icon={User}
              title="Profile Load"
              description="Test profile data loading"
              testName="Profile Load"
              onPress={() => runTest('Profile Load', testProfileLoad)}
            />
            
            <TestItem
              icon={User}
              title="Profile Update"
              description="Test profile data updates"
              testName="Profile Update"
              onPress={() => runTest('Profile Update', testProfileUpdate)}
            />
            
            <TestItem
              icon={Settings}
              title="Settings Update"
              description="Test settings modifications"
              testName="Settings Update"
              onPress={() => runTest('Settings Update', testSettingsUpdate)}
            />
            
            <TestItem
              icon={Camera}
              title="Photo Upload"
              description="Test photo upload functionality"
              testName="Photo Upload"
              onPress={() => runTest('Photo Upload', testPhotoUpload)}
            />
            
            <TestItem
              icon={Shield}
              title="Navigation Flow"
              description="Test navigation between screens"
              testName="Navigation Flow"
              onPress={() => runTest('Navigation Flow', testNavigationFlow)}
            />
            
            <TestItem
              icon={Award}
              title="Data Persistence"
              description="Test data persistence across sessions"
              testName="Data Persistence"
              onPress={() => runTest('Data Persistence', testDataPersistence)}
            />
          </View>

          {/* Current State */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Current State</Text>
            <View style={styles.stateContainer}>
              <Text style={styles.stateText}>Profile Loaded: {profile ? '✅' : '❌'}</Text>
              <Text style={styles.stateText}>Settings Loaded: {settings ? '✅' : '❌'}</Text>
              <Text style={styles.stateText}>Loading: {isLoading ? '⏳' : '✅'}</Text>
              <Text style={styles.stateText}>Error: {error ? '❌' : '✅'}</Text>
              {profile && (
                <>
                  <Text style={styles.stateText}>Photos: {profile.photos.length}</Text>
                  <Text style={styles.stateText}>Completion: {profile.profileCompletion}%</Text>
                </>
              )}
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  placeholder: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    marginVertical: 10,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 12,
  },
  runAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.primary,
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    margin: 20,
  },
  runAllButtonDisabled: {
    backgroundColor: theme.colors.gray400,
  },
  runAllButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
    marginLeft: 8,
  },
  testItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray100,
  },
  testLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  testIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  testContent: {
    flex: 1,
  },
  testTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 2,
  },
  testDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
  },
  stateContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  stateText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text,
    marginBottom: 4,
  },
});
