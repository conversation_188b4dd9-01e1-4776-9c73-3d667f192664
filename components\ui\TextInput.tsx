import React, { useState, useRef } from 'react';
import {
  View,
  TextInput as RNTextInput,
  Text,
  StyleSheet,
  Animated,
  TextInputProps,
  ViewStyle,
} from 'react-native';
import { theme } from '@/constants/theme';

interface CustomTextInputProps extends TextInputProps {
  label?: string;
  error?: string;
  success?: boolean;
  helperText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  containerStyle?: ViewStyle;
  showCharacterCount?: boolean;
  maxLength?: number;
  variant?: 'default' | 'filled' | 'outlined';
}

export default function TextInput({
  label,
  error,
  success,
  helperText,
  leftIcon,
  rightIcon,
  containerStyle,
  showCharacterCount,
  maxLength,
  variant = 'outlined',
  value = '',
  onFocus,
  onBlur,
  ...props
}: CustomTextInputProps) {
  const [isFocused, setIsFocused] = useState(false);
  const animatedLabel = useRef(new Animated.Value(value ? 1 : 0)).current;
  const animatedBorder = useRef(new Animated.Value(0)).current;

  const handleFocus = (e: any) => {
    setIsFocused(true);
    
    Animated.parallel([
      Animated.timing(animatedLabel, {
        toValue: 1,
        duration: theme.animation.duration.fast,
        useNativeDriver: false,
      }),
      Animated.timing(animatedBorder, {
        toValue: 1,
        duration: theme.animation.duration.fast,
        useNativeDriver: false,
      }),
    ]).start();
    
    onFocus?.(e);
  };

  const handleBlur = (e: any) => {
    setIsFocused(false);
    
    if (!value) {
      Animated.timing(animatedLabel, {
        toValue: 0,
        duration: theme.animation.duration.fast,
        useNativeDriver: false,
      }).start();
    }
    
    Animated.timing(animatedBorder, {
      toValue: 0,
      duration: theme.animation.duration.fast,
      useNativeDriver: false,
    }).start();
    
    onBlur?.(e);
  };

  const labelStyle = {
    position: 'absolute' as const,
    left: leftIcon ? 48 : theme.spacing.md,
    top: animatedLabel.interpolate({
      inputRange: [0, 1],
      outputRange: [20, -8],
    }),
    fontSize: animatedLabel.interpolate({
      inputRange: [0, 1],
      outputRange: [theme.fontSize.base, theme.fontSize.sm],
    }),
    color: animatedLabel.interpolate({
      inputRange: [0, 1],
      outputRange: [theme.colors.gray500, isFocused ? theme.colors.primary : theme.colors.gray600],
    }),
    backgroundColor: theme.colors.white,
    paddingHorizontal: 4,
    zIndex: 1,
  };

  const borderColor = animatedBorder.interpolate({
    inputRange: [0, 1],
    outputRange: [
      error ? theme.colors.error : theme.colors.gray300,
      error ? theme.colors.error : success ? theme.colors.success : theme.colors.primary,
    ],
  });

  const containerStyles = [
    styles.container,
    styles[variant],
    { borderColor },
    error && styles.errorContainer,
    success && styles.successContainer,
    containerStyle,
  ];

  return (
    <View style={styles.wrapper}>
      <View style={containerStyles as any}>
        {label && variant === 'outlined' && (
          <Animated.Text style={labelStyle}>{label}</Animated.Text>
        )}
        
        {leftIcon && <View style={styles.leftIcon}>{leftIcon}</View>}
        
        <RNTextInput
          {...props}
          value={value}
          onFocus={handleFocus}
          onBlur={handleBlur}
          style={[
            styles.input,
            leftIcon && styles.inputWithLeftIcon,
            rightIcon && styles.inputWithRightIcon,
          ] as any}
          placeholderTextColor={theme.colors.gray400}
        />
        
        {rightIcon && <View style={styles.rightIcon}>{rightIcon}</View>}
      </View>
      
      <View style={styles.footer}>
        <View style={styles.helperTextContainer}>
          {error && <Text style={styles.errorText}>{error}</Text>}
          {!error && helperText && (
            <Text style={styles.helperText}>{helperText}</Text>
          )}
        </View>
        
        {showCharacterCount && maxLength && (
          <Text style={styles.characterCount}>
            {value.length}/{maxLength}
          </Text>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  wrapper: {
    marginBottom: theme.spacing.md,
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: theme.borderRadius.lg,
    backgroundColor: theme.colors.white,
  },
  default: {
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray300,
  },
  filled: {
    backgroundColor: theme.colors.gray100,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
  },
  outlined: {
    borderWidth: 1,
    borderColor: theme.colors.gray300,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.md,
  },
  input: {
    flex: 1,
    fontSize: theme.fontSize.base,
    color: theme.colors.gray900,
    paddingVertical: theme.spacing.sm,
  },
  inputWithLeftIcon: {
    marginLeft: theme.spacing.sm,
  },
  inputWithRightIcon: {
    marginRight: theme.spacing.sm,
  },
  leftIcon: {
    marginRight: theme.spacing.sm,
  },
  rightIcon: {
    marginLeft: theme.spacing.sm,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: theme.spacing.xs,
    paddingHorizontal: theme.spacing.xs,
  },
  helperTextContainer: {
    flex: 1,
  },
  helperText: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.gray500,
  },
  errorText: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.error,
  },
  characterCount: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.gray400,
  },
  errorContainer: {
    borderColor: theme.colors.error,
  },
  successContainer: {
    borderColor: theme.colors.success,
  },
});
