{"compilerOptions": {"target": "esnext", "lib": ["dom", "esnext"], "allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "strict": true, "moduleResolution": "node", "resolveJsonModule": true, "noEmit": true, "jsx": "react-jsx", "isolatedModules": true, "incremental": true, "paths": {"@/*": ["./*"]}}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", ".expo/types/**/*.ts", "expo-env.d.ts", "nativewind-env.d.ts"], "exclude": ["node_modules", ".expo", "dist", "web-build"], "extends": "expo/tsconfig.base"}