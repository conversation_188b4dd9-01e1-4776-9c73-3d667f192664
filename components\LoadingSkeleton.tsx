import React from 'react';
import { View, StyleSheet } from 'react-native';
import { SkeletonProfile, SkeletonCard } from './ui/Skeleton';
import { theme } from '../constants/theme';

interface LoadingSkeletonProps {
  variant?: 'profile' | 'settings' | 'list' | 'card';
  count?: number;
}

export default function LoadingSkeleton({ 
  variant = 'settings', 
  count = 3 
}: LoadingSkeletonProps) {
  const renderSkeleton = () => {
    switch (variant) {
      case 'profile':
        return (
          <View style={styles.container}>
            <SkeletonProfile />
            <SkeletonCard />
            <SkeletonCard />
          </View>
        );
      
      case 'list':
        return (
          <View style={styles.container}>
            {Array.from({ length: count }, (_, index) => (
              <SkeletonProfile key={index} style={styles.listItem} />
            ))}
          </View>
        );
      
      case 'card':
        return (
          <View style={styles.container}>
            {Array.from({ length: count }, (_, index) => (
              <SkeletonCard key={index} />
            ))}
          </View>
        );
      
      case 'settings':
      default:
        return (
          <View style={styles.container}>
            <SkeletonProfile style={styles.headerSkeleton} />
            <View style={styles.section}>
              {Array.from({ length: 4 }, (_, index) => (
                <View key={index} style={styles.settingItem}>
                  <View style={styles.settingIcon} />
                  <View style={styles.settingContent}>
                    <View style={styles.settingTitle} />
                    <View style={styles.settingSubtitle} />
                  </View>
                </View>
              ))}
            </View>
            <View style={styles.section}>
              {Array.from({ length: 3 }, (_, index) => (
                <View key={index} style={styles.settingItem}>
                  <View style={styles.settingIcon} />
                  <View style={styles.settingContent}>
                    <View style={styles.settingTitle} />
                    <View style={styles.settingSubtitle} />
                  </View>
                </View>
              ))}
            </View>
          </View>
        );
    }
  };

  return renderSkeleton();
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  headerSkeleton: {
    marginBottom: 20,
  },
  section: {
    backgroundColor: 'white',
    borderRadius: 16,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray100,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.gray200,
    marginRight: 12,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    height: 16,
    backgroundColor: theme.colors.gray200,
    borderRadius: 4,
    marginBottom: 6,
    width: '60%',
  },
  settingSubtitle: {
    height: 12,
    backgroundColor: theme.colors.gray200,
    borderRadius: 4,
    width: '40%',
  },
  listItem: {
    marginBottom: 12,
  },
});
