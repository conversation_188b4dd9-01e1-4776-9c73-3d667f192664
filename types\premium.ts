// Premium subscription and feature types
export interface PremiumSubscription {
  id: string;
  userId: string;
  planId: string;
  status: SubscriptionStatus;
  startDate: Date;
  endDate: Date;
  autoRenew: boolean;
  paymentMethod: PaymentMethod;
  price: number;
  currency: string;
  billingCycle: BillingCycle;
  trialEndDate?: Date;
  cancelledAt?: Date;
  pausedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export type SubscriptionStatus = 
  | 'active' 
  | 'cancelled' 
  | 'expired' 
  | 'paused' 
  | 'trial' 
  | 'pending' 
  | 'failed';

export type BillingCycle = '1_month' | '3_months' | '6_months' | '12_months';

export interface SubscriptionPlan {
  id: string;
  name: string;
  duration: string;
  billingCycle: BillingCycle;
  price: number;
  originalPrice?: number;
  currency: string;
  pricePerMonth: string;
  savings?: string;
  savingsPercentage?: number;
  popular: boolean;
  features: PremiumFeatureType[];
  trialDays?: number;
  description?: string;
  isActive: boolean;
}

export type PremiumFeatureType = 
  | 'unlimited_likes'
  | 'see_who_likes_you'
  | 'super_likes'
  | 'priority_messages'
  | 'advanced_filters'
  | 'profile_boost'
  | 'incognito_mode'
  | 'read_receipts'
  | 'rewind_swipes'
  | 'passport_mode'
  | 'premium_support'
  | 'ad_free_experience';

export interface PremiumFeature {
  id: PremiumFeatureType;
  name: string;
  description: string;
  icon: string;
  highlight: boolean;
  category: FeatureCategory;
  isNew?: boolean;
  comingSoon?: boolean;
}

export type FeatureCategory = 'discovery' | 'messaging' | 'privacy' | 'boost' | 'support';

export interface PaymentMethod {
  id: string;
  type: PaymentType;
  last4?: string;
  brand?: string;
  expiryMonth?: number;
  expiryYear?: number;
  isDefault: boolean;
}

export type PaymentType = 'card' | 'paypal' | 'apple_pay' | 'google_pay' | 'bank_transfer';

export interface PaymentTransaction {
  id: string;
  subscriptionId: string;
  amount: number;
  currency: string;
  status: TransactionStatus;
  paymentMethod: PaymentMethod;
  timestamp: Date;
  receiptUrl?: string;
  failureReason?: string;
}

export type TransactionStatus = 'pending' | 'completed' | 'failed' | 'refunded' | 'cancelled';

export interface PremiumUsage {
  userId: string;
  period: Date;
  likesUsed: number;
  superLikesUsed: number;
  boostsUsed: number;
  rewindsUsed: number;
  features: {
    [key in PremiumFeatureType]?: {
      used: number;
      limit?: number;
      lastUsed?: Date;
    };
  };
}

export interface PremiumAnalytics {
  totalRevenue: number;
  activeSubscriptions: number;
  churnRate: number;
  conversionRate: number;
  averageRevenuePerUser: number;
  popularPlans: {
    planId: string;
    subscriptions: number;
    percentage: number;
  }[];
  featureUsage: {
    [key in PremiumFeatureType]?: {
      users: number;
      usage: number;
      engagement: number;
    };
  };
}

// Premium feature access control
export interface FeatureAccess {
  canAccess: boolean;
  reason?: string;
  upgradeRequired?: boolean;
  featureLimit?: {
    current: number;
    limit: number;
    resetDate?: Date;
  };
}

// Premium onboarding and promotions
export interface PremiumPromotion {
  id: string;
  name: string;
  description: string;
  discountPercentage?: number;
  discountAmount?: number;
  validFrom: Date;
  validTo: Date;
  targetPlans: string[];
  isActive: boolean;
  conditions?: {
    newUsersOnly?: boolean;
    minSubscriptionLength?: BillingCycle;
    maxUsageCount?: number;
  };
}

// Premium feature validation
export interface PremiumValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Constants for premium features
export const PREMIUM_FEATURES: PremiumFeature[] = [
  {
    id: 'see_who_likes_you',
    name: 'See Who Likes You',
    description: 'View all your likes instantly without swiping',
    icon: 'eye',
    highlight: true,
    category: 'discovery',
  },
  {
    id: 'unlimited_likes',
    name: 'Unlimited Likes',
    description: 'Like as many profiles as you want',
    icon: 'heart',
    highlight: true,
    category: 'discovery',
  },
  {
    id: 'super_likes',
    name: '5 Super Likes Daily',
    description: 'Stand out with Super Likes to get noticed',
    icon: 'zap',
    highlight: false,
    category: 'discovery',
  },
  {
    id: 'priority_messages',
    name: 'Priority Messages',
    description: 'Your messages appear first in their inbox',
    icon: 'message-circle',
    highlight: false,
    category: 'messaging',
  },
  {
    id: 'advanced_filters',
    name: 'Advanced Filters',
    description: 'Filter by education, lifestyle, and more',
    icon: 'filter',
    highlight: false,
    category: 'discovery',
  },
  {
    id: 'profile_boost',
    name: 'Profile Boost',
    description: 'Be seen by 10x more people for 30 minutes',
    icon: 'trending-up',
    highlight: false,
    category: 'boost',
  },
  {
    id: 'incognito_mode',
    name: 'Incognito Mode',
    description: 'Browse profiles privately',
    icon: 'shield',
    highlight: false,
    category: 'privacy',
  },
  {
    id: 'read_receipts',
    name: 'Read Receipts',
    description: 'See when your messages are read',
    icon: 'star',
    highlight: false,
    category: 'messaging',
  },
];

export const DEFAULT_SUBSCRIPTION_PLANS: SubscriptionPlan[] = [
  {
    id: '1_month',
    name: 'Monthly',
    duration: '1 Month',
    billingCycle: '1_month',
    price: 19.99,
    currency: 'USD',
    pricePerMonth: '$19.99/month',
    savings: undefined,
    popular: false,
    features: ['unlimited_likes', 'see_who_likes_you', 'super_likes', 'priority_messages'],
    isActive: true,
  },
  {
    id: '3_months',
    name: 'Quarterly',
    duration: '3 Months',
    billingCycle: '3_months',
    price: 39.99,
    originalPrice: 59.97,
    currency: 'USD',
    pricePerMonth: '$13.33/month',
    savings: 'Save 33%',
    savingsPercentage: 33,
    popular: true,
    features: ['unlimited_likes', 'see_who_likes_you', 'super_likes', 'priority_messages', 'advanced_filters'],
    isActive: true,
  },
  {
    id: '6_months',
    name: 'Semi-Annual',
    duration: '6 Months',
    billingCycle: '6_months',
    price: 59.99,
    originalPrice: 119.94,
    currency: 'USD',
    pricePerMonth: '$9.99/month',
    savings: 'Save 50%',
    savingsPercentage: 50,
    popular: false,
    features: ['unlimited_likes', 'see_who_likes_you', 'super_likes', 'priority_messages', 'advanced_filters', 'profile_boost', 'incognito_mode', 'read_receipts'],
    isActive: true,
  },
];

// Feature limits for free users
export const FREE_USER_LIMITS = {
  daily_likes: 10,
  super_likes: 1,
  rewinds: 0,
  boosts: 0,
  advanced_filters: false,
  see_who_likes_you: false,
  priority_messages: false,
  incognito_mode: false,
  read_receipts: false,
} as const;
