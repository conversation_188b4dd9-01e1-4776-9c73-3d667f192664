import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { Check, CheckCheck, Clock, AlertCircle, Languages, Eye, EyeOff } from 'lucide-react-native';
import { Message } from '@/types/messaging';
import { useTranslation } from '@/hooks/useTranslation';
import { useMessagesStore } from '@/stores/messagesStore';
import MessageReactions from './MessageReactions';
import { theme } from '@/constants/theme';
import * as Haptics from 'expo-haptics';
import { Platform } from 'react-native';

interface EnhancedMessageBubbleProps {
  message: Message;
  isOwn: boolean;
  showAvatar: boolean;
  isConsecutive: boolean;
  senderAvatar: string;
  onLongPress?: () => void;
}

const { width } = Dimensions.get('window');
const MAX_BUBBLE_WIDTH = width * 0.75;

export default function EnhancedMessageBubble({
  message,
  isOwn,
  showAvatar,
  isConsecutive,
  senderAvatar,
  onLongPress
}: EnhancedMessageBubbleProps) {
  const [showOriginal, setShowOriginal] = useState(false);
  const [isTranslating, setIsTranslating] = useState(false);

  const {
    settings,
    translateMessage,
    getTranslation,
    isTranslating: globalTranslating,
  } = useTranslation();

  const {
    messageReactions,
    currentUser,
    addReaction,
    removeReaction
  } = useMessagesStore();

  const translation = getTranslation(message.id);
  const shouldShowTranslation = !isOwn && settings.autoTranslate && translation;
  const canTranslate = !isOwn && message.type === 'text';

  // Auto-translate if enabled and message is not from current user
  useEffect(() => {
    if (settings.autoTranslate && canTranslate && !translation && !isTranslating) {
      handleTranslate();
    }
  }, [settings.autoTranslate, canTranslate, translation, isTranslating]);

  const handleTranslate = async () => {
    if (isTranslating || translation) return;

    setIsTranslating(true);

    try {
      await translateMessage(message.id, message.content);

      if (Platform.OS !== 'web') {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
    } catch (error) {
      console.error('Translation failed:', error);
    } finally {
      setIsTranslating(false);
    }
  };

  const handleLongPress = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    onLongPress?.();
  };

  const handleAddReaction = (emoji: string) => {
    if (currentUser) {
      addReaction(message.id, emoji);
    }
  };

  const handleRemoveReaction = (emoji: string) => {
    if (currentUser) {
      removeReaction(message.id, emoji);
    }
  };

  // Get reactions for this message
  const reactions = messageReactions[message.id] || [];

  const toggleOriginal = () => {
    setShowOriginal(!showOriginal);
    
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  const getStatusIcon = () => {
    switch (message.status) {
      case 'sending':
        return <Clock size={12} color={theme.colors.gray400} />;
      case 'sent':
        return <Check size={12} color={theme.colors.gray400} />;
      case 'delivered':
        return <CheckCheck size={12} color={theme.colors.gray400} />;
      case 'read':
        return <CheckCheck size={12} color={theme.colors.primary} />;
      default:
        return <AlertCircle size={12} color={theme.colors.error} />;
    }
  };

  const renderTextContent = () => {
    const displayText = shouldShowTranslation && !showOriginal 
      ? translation?.translatedText 
      : message.content;

    return (
      <View>
        <Text style={[
          styles.messageText,
          isOwn ? styles.ownMessageText : styles.otherMessageText,
        ]}>
          {displayText}
        </Text>

        {/* Translation indicator */}
        {shouldShowTranslation && (
          <View style={styles.translationIndicator}>
            <Languages size={10} color={theme.colors.gray500} />
            <Text style={styles.translationLabel}>
              {showOriginal ? 'Original' : `Translated from ${translation?.sourceLanguage?.toUpperCase()}`}
            </Text>
            
            {settings.showOriginal && (
              <TouchableOpacity onPress={toggleOriginal} style={styles.toggleButton}>
                {showOriginal ? (
                  <EyeOff size={12} color={theme.colors.gray500} />
                ) : (
                  <Eye size={12} color={theme.colors.gray500} />
                )}
              </TouchableOpacity>
            )}
          </View>
        )}

        {/* Show both original and translated if enabled */}
        {shouldShowTranslation && settings.showOriginal && !showOriginal && (
          <View style={styles.originalTextContainer}>
            <Text style={styles.originalText}>
              {message.content}
            </Text>
          </View>
        )}
      </View>
    );
  };

  const renderImageContent = () => (
    <View style={styles.imageContainer}>
      <Image source={{ uri: message.fileUrl }} style={styles.messageImage} />
      {message.content && (
        <Text style={[
          styles.imageCaption,
          isOwn ? styles.ownMessageText : styles.otherMessageText,
        ]}>
          {message.content}
        </Text>
      )}
    </View>
  );

  const renderFileContent = () => (
    <View style={styles.fileContainer}>
      <View style={styles.fileIcon}>
        <Text style={styles.fileExtension}>
          {message.fileName?.split('.').pop()?.toUpperCase() || 'FILE'}
        </Text>
      </View>
      <View style={styles.fileInfo}>
        <Text style={[
          styles.fileName,
          isOwn ? styles.ownMessageText : styles.otherMessageText,
        ]}>
          {message.fileName}
        </Text>
        <Text style={styles.fileSize}>
          {message.fileSize ? `${(message.fileSize / 1024).toFixed(1)} KB` : 'Unknown size'}
        </Text>
      </View>
    </View>
  );

  const renderContent = () => {
    switch (message.type) {
      case 'text':
        return renderTextContent();
      case 'image':
        return renderImageContent();
      case 'file':
        return renderFileContent();
      default:
        return renderTextContent();
    }
  };

  return (
    <View style={[
      styles.container,
      isOwn ? styles.ownContainer : styles.otherContainer,
      isConsecutive && styles.consecutiveContainer,
    ]}>
      {/* Avatar */}
      {showAvatar && !isOwn && (
        <Image source={{ uri: senderAvatar }} style={styles.avatar} />
      )}

      {/* Message bubble */}
      <View style={[
        styles.bubble,
        isOwn ? styles.ownBubble : styles.otherBubble,
        isConsecutive && (isOwn ? styles.consecutiveOwnBubble : styles.consecutiveOtherBubble),
      ]}>
        {/* Translation button for non-own messages */}
        {canTranslate && !settings.autoTranslate && (
          <TouchableOpacity
            onPress={handleTranslate}
            style={styles.translateButton}
            disabled={isTranslating || globalTranslating}
          >
            {isTranslating || globalTranslating ? (
              <ActivityIndicator size="small" color={theme.colors.gray500} />
            ) : (
              <Languages size={14} color={theme.colors.gray500} />
            )}
          </TouchableOpacity>
        )}

        {renderContent()}

        {/* Message status and timestamp */}
        <View style={[
          styles.messageFooter,
          isOwn ? styles.ownMessageFooter : styles.otherMessageFooter,
        ]}>
          <Text style={styles.timestamp}>
            {message.timestamp.toLocaleTimeString([], { 
              hour: '2-digit', 
              minute: '2-digit' 
            })}
          </Text>
          {isOwn && (
            <View style={styles.statusIcon}>
              {getStatusIcon()}
            </View>
          )}
        </View>
      </View>

      {/* Message Reactions */}
      {reactions.length > 0 && currentUser && (
        <MessageReactions
          messageId={message.id}
          reactions={reactions}
          currentUserId={currentUser.id}
          onAddReaction={handleAddReaction}
          onRemoveReaction={handleRemoveReaction}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    marginVertical: 2,
    paddingHorizontal: 16,
  },
  ownContainer: {
    justifyContent: 'flex-end',
  },
  otherContainer: {
    justifyContent: 'flex-start',
  },
  consecutiveContainer: {
    marginVertical: 1,
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 8,
    marginTop: 4,
  },
  bubble: {
    maxWidth: MAX_BUBBLE_WIDTH,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 18,
    position: 'relative',
  },
  ownBubble: {
    backgroundColor: theme.colors.primary,
    borderBottomRightRadius: 4,
  },
  otherBubble: {
    backgroundColor: theme.colors.gray100,
    borderBottomLeftRadius: 4,
  },
  consecutiveOwnBubble: {
    borderBottomRightRadius: 18,
  },
  consecutiveOtherBubble: {
    borderBottomLeftRadius: 18,
  },
  translateButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    zIndex: 1,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 20,
  },
  ownMessageText: {
    color: 'white',
  },
  otherMessageText: {
    color: theme.colors.text,
  },
  translationIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    gap: 4,
  },
  translationLabel: {
    fontSize: 10,
    color: theme.colors.gray500,
    flex: 1,
  },
  toggleButton: {
    padding: 2,
  },
  originalTextContainer: {
    marginTop: 6,
    paddingTop: 6,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.2)',
  },
  originalText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    fontStyle: 'italic',
  },
  imageContainer: {
    overflow: 'hidden',
  },
  messageImage: {
    width: 200,
    height: 200,
    borderRadius: 12,
  },
  imageCaption: {
    marginTop: 8,
    fontSize: 14,
  },
  fileContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  fileIcon: {
    width: 40,
    height: 40,
    borderRadius: 8,
    backgroundColor: theme.colors.gray300,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fileExtension: {
    fontSize: 10,
    fontWeight: 'bold',
    color: theme.colors.gray600,
  },
  fileInfo: {
    flex: 1,
  },
  fileName: {
    fontSize: 14,
    fontWeight: '500',
  },
  fileSize: {
    fontSize: 12,
    color: theme.colors.gray500,
    marginTop: 2,
  },
  messageFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    gap: 4,
  },
  ownMessageFooter: {
    justifyContent: 'flex-end',
  },
  otherMessageFooter: {
    justifyContent: 'flex-start',
  },
  timestamp: {
    fontSize: 11,
    color: theme.colors.gray500,
  },
  statusIcon: {
    marginLeft: 4,
  },
});
