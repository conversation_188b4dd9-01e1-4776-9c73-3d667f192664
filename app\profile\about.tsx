import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Platform,
  Linking,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import * as Haptics from 'expo-haptics';
import {
  ArrowLeft,
  FileText,
  Shield,
  Info,
  Heart,
  Star,
  MessageSquare,
  ExternalLink,
  Mail,
  Globe,
  Users,
  Award,
} from 'lucide-react-native';

import { theme } from '../../constants/theme';

export default function AboutScreen() {
  const router = useRouter();
  const appVersion = '1.0.0';
  const buildNumber = '100';

  const handleBack = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    router.back();
  };

  const handleOpenLink = async (url: string, title: string) => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        Alert.alert('Error', `Cannot open ${title}`);
      }
    } catch (error) {
      Alert.alert('Error', `Failed to open ${title}`);
    }
  };

  const handleTermsOfService = () => {
    handleOpenLink('https://example.com/terms', 'Terms of Service');
  };

  const handlePrivacyPolicy = () => {
    handleOpenLink('https://example.com/privacy', 'Privacy Policy');
  };

  const handleCommunityGuidelines = () => {
    handleOpenLink('https://example.com/guidelines', 'Community Guidelines');
  };

  const handleSafetyTips = () => {
    handleOpenLink('https://example.com/safety', 'Safety Tips');
  };

  const handleContactUs = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    Alert.alert(
      'Contact Us',
      'How would you like to contact us?',
      [
        { text: 'Email', onPress: () => handleOpenLink('mailto:<EMAIL>', 'Email') },
        { text: 'Website', onPress: () => handleOpenLink('https://example.com/contact', 'Website') },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const handleRateApp = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    const storeUrl = Platform.OS === 'ios' 
      ? 'https://apps.apple.com/app/id123456789'
      : 'https://play.google.com/store/apps/details?id=com.datingapp';
    
    handleOpenLink(storeUrl, 'App Store');
  };

  const handleFeedback = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    Alert.alert(
      'Send Feedback',
      'We value your feedback! How would you like to share it?',
      [
        { text: 'Email Feedback', onPress: () => handleOpenLink('mailto:<EMAIL>', 'Email') },
        { text: 'In-App Survey', onPress: () => Alert.alert('Survey', 'In-app survey would open here') },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const handleAcknowledgments = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    Alert.alert(
      'Acknowledgments',
      'This app is built with love using React Native, Expo, and many other amazing open-source libraries. Thank you to all the contributors!',
      [{ text: 'OK' }]
    );
  };

  const MenuItem = ({ 
    icon: Icon, 
    title, 
    subtitle, 
    onPress,
    showArrow = true,
  }: {
    icon: any;
    title: string;
    subtitle?: string;
    onPress: () => void;
    showArrow?: boolean;
  }) => (
    <TouchableOpacity style={styles.menuItem} onPress={onPress}>
      <View style={styles.menuLeft}>
        <View style={styles.menuIcon}>
          <Icon size={20} color={theme.colors.primary} />
        </View>
        <View style={styles.menuContent}>
          <Text style={styles.menuTitle}>{title}</Text>
          {subtitle && <Text style={styles.menuSubtitle}>{subtitle}</Text>}
        </View>
      </View>
      {showArrow && <ExternalLink size={16} color={theme.colors.gray400} />}
    </TouchableOpacity>
  );

  return (
    <LinearGradient colors={['#8B5CF6', '#EC4899']} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <ArrowLeft size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>About</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* App Info */}
          <View style={styles.section}>
            <View style={styles.appInfo}>
              <View style={styles.appIcon}>
                <Heart size={32} color="white" />
              </View>
              <Text style={styles.appName}>Dating App</Text>
              <Text style={styles.appVersion}>Version {appVersion} ({buildNumber})</Text>
              <Text style={styles.appDescription}>
                Connect with amazing people and find meaningful relationships
              </Text>
            </View>
          </View>

          {/* Legal */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Legal</Text>
            
            <MenuItem
              icon={FileText}
              title="Terms of Service"
              subtitle="Our terms and conditions"
              onPress={handleTermsOfService}
            />
            
            <MenuItem
              icon={Shield}
              title="Privacy Policy"
              subtitle="How we protect your data"
              onPress={handlePrivacyPolicy}
            />
            
            <MenuItem
              icon={Users}
              title="Community Guidelines"
              subtitle="Rules for a safe community"
              onPress={handleCommunityGuidelines}
            />
            
            <MenuItem
              icon={Award}
              title="Safety Tips"
              subtitle="Stay safe while dating"
              onPress={handleSafetyTips}
            />
          </View>

          {/* Support */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Support</Text>
            
            <MenuItem
              icon={Mail}
              title="Contact Us"
              subtitle="Get help from our support team"
              onPress={handleContactUs}
            />
            
            <MenuItem
              icon={MessageSquare}
              title="Send Feedback"
              subtitle="Help us improve the app"
              onPress={handleFeedback}
            />
            
            <MenuItem
              icon={Star}
              title="Rate the App"
              subtitle="Share your experience"
              onPress={handleRateApp}
            />
          </View>

          {/* About */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>More</Text>
            
            <MenuItem
              icon={Globe}
              title="Website"
              subtitle="Visit our website"
              onPress={() => handleOpenLink('https://example.com', 'Website')}
            />
            
            <MenuItem
              icon={Info}
              title="Acknowledgments"
              subtitle="Credits and thanks"
              onPress={handleAcknowledgments}
              showArrow={false}
            />
          </View>

          {/* Footer */}
          <View style={styles.footer}>
            <Text style={styles.footerText}>
              Made with ❤️ for connecting hearts
            </Text>
            <Text style={styles.footerCopyright}>
              © 2024 Dating App. All rights reserved.
            </Text>
          </View>
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  placeholder: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    marginVertical: 10,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 12,
  },
  appInfo: {
    alignItems: 'center',
    paddingVertical: 30,
    paddingHorizontal: 20,
  },
  appIcon: {
    width: 80,
    height: 80,
    borderRadius: 20,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  appName: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: theme.colors.text,
    marginBottom: 4,
  },
  appVersion: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: theme.colors.gray600,
    marginBottom: 12,
  },
  appDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
    textAlign: 'center',
    lineHeight: 20,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray100,
  },
  menuLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  menuContent: {
    flex: 1,
  },
  menuTitle: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: theme.colors.text,
    marginBottom: 2,
  },
  menuSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 30,
    paddingHorizontal: 20,
  },
  footerText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: theme.colors.gray600,
    marginBottom: 8,
  },
  footerCopyright: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray500,
  },
});
