import React, { useMemo, useCallback } from 'react';
import {
  View,
  FlatList,
  Dimensions,
  StyleSheet,
  RefreshControl,
  ListRenderItem,
} from 'react-native';
import { LikeProfile } from '@/types/messaging';
import AnimatedLikeCard from './AnimatedLikeCard';
import { LikesGridSkeleton } from '@/components/ui/LikesSkeleton';
import { theme } from '@/constants/theme';

const { width } = Dimensions.get('window');
const CARD_WIDTH = (width - 48) / 2;
const ITEM_HEIGHT = CARD_WIDTH * 1.5; // Approximate height including content

interface OptimizedLikesListProps {
  likes: LikeProfile[];
  isLoading: boolean;
  isRefreshing: boolean;
  hasMore: boolean;
  onRefresh: () => void;
  onLoadMore: () => void;
  onLikeBack: (userId: string) => Promise<boolean>;
  onMessage: (userId: string) => void;
  onProfilePress: (profile: LikeProfile) => void;
}

export default function OptimizedLikesList({
  likes,
  isLoading,
  isRefreshing,
  hasMore,
  onRefresh,
  onLoadMore,
  onLikeBack,
  onMessage,
  onProfilePress,
}: OptimizedLikesListProps) {
  // Memoize the data to prevent unnecessary re-renders
  const memoizedLikes = useMemo(() => likes, [likes]);

  // Create pairs for grid layout
  const pairedData = useMemo(() => {
    const pairs: (LikeProfile | null)[][] = [];
    for (let i = 0; i < memoizedLikes.length; i += 2) {
      pairs.push([
        memoizedLikes[i],
        memoizedLikes[i + 1] || null, // null for odd number of items
      ]);
    }
    return pairs;
  }, [memoizedLikes]);

  // Memoized render item function
  const renderItem: ListRenderItem<(LikeProfile | null)[]> = useCallback(
    ({ item: pair, index }) => (
      <View style={styles.row}>
        {pair[0] && (
          <View style={styles.cardContainer}>
            <AnimatedLikeCard
              profile={pair[0]}
              onLikeBack={onLikeBack}
              onMessage={onMessage}
              onPress={onProfilePress}
            />
          </View>
        )}
        {pair[1] && (
          <View style={styles.cardContainer}>
            <AnimatedLikeCard
              profile={pair[1]}
              onLikeBack={onLikeBack}
              onMessage={onMessage}
              onPress={onProfilePress}
            />
          </View>
        )}
        {!pair[1] && <View style={styles.cardContainer} />}
      </View>
    ),
    [onLikeBack, onMessage, onProfilePress]
  );

  // Memoized key extractor
  const keyExtractor = useCallback(
    (item: (LikeProfile | null)[], index: number) => {
      const key1 = item[0]?.id || '';
      const key2 = item[1]?.id || '';
      return `${key1}-${key2}-${index}`;
    },
    []
  );

  // Memoized get item layout for better performance
  const getItemLayout = useCallback(
    (data: any, index: number) => ({
      length: ITEM_HEIGHT,
      offset: ITEM_HEIGHT * index,
      index,
    }),
    []
  );

  // Handle end reached
  const handleEndReached = useCallback(() => {
    if (hasMore && !isLoading) {
      onLoadMore();
    }
  }, [hasMore, isLoading, onLoadMore]);

  // Render footer
  const renderFooter = useCallback(() => {
    if (!isLoading || isRefreshing) return null;
    return (
      <View style={styles.footer}>
        <LikesGridSkeleton count={4} />
      </View>
    );
  }, [isLoading, isRefreshing]);

  // Render empty component
  const renderEmpty = useCallback(() => {
    if (isLoading) {
      return (
        <View style={styles.emptyContainer}>
          <LikesGridSkeleton count={6} />
        </View>
      );
    }
    return null;
  }, [isLoading]);

  return (
    <FlatList
      data={pairedData}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      getItemLayout={getItemLayout}
      numColumns={1}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.container}
      refreshControl={
        <RefreshControl
          refreshing={isRefreshing}
          onRefresh={onRefresh}
          tintColor="white"
          colors={['white']}
        />
      }
      onEndReached={handleEndReached}
      onEndReachedThreshold={0.5}
      ListFooterComponent={renderFooter}
      ListEmptyComponent={renderEmpty}
      removeClippedSubviews={true}
      maxToRenderPerBatch={10}
      updateCellsBatchingPeriod={50}
      initialNumToRender={6}
      windowSize={10}
      legacyImplementation={false}
    />
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  cardContainer: {
    width: CARD_WIDTH,
  },
  footer: {
    paddingVertical: 20,
  },
  emptyContainer: {
    paddingTop: 20,
  },
});
