export interface User {
  id: string;
  name: string;
  age: number;
  bio: string;
  photos: string[];
  location: string;
  distance: number;
  interests: string[];
  occupation: string;
  education: string;
  isOnline: boolean;
  lastSeen?: string;
  verified: boolean;
}

export interface Match {
  id: string;
  user: User;
  matchedAt: string;
  lastMessage?: Message;
}

export interface Message {
  id: string;
  senderId: string;
  receiverId: string;
  content: string;
  timestamp: string;
  status: 'sent' | 'delivered' | 'read';
  type: 'text' | 'image' | 'gif';
}

export interface Conversation {
  id: string;
  participants: User[];
  messages: Message[];
  lastMessage: Message;
  unreadCount: number;
}

// Mock users for discovery
export const mockUsers: User[] = [
  {
    id: '1',
    name: '<PERSON>',
    age: 25,
    bio: 'Adventure seeker 🌟 Love hiking, photography, and good coffee ☕️',
    photos: [
      'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400',
      'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=400',
      'https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?w=400',
    ],
    location: 'New York, NY',
    distance: 2,
    interests: ['Photography', 'Hiking', 'Coffee', 'Travel'],
    occupation: 'Graphic Designer',
    education: 'NYU',
    isOnline: true,
    verified: true,
  },
  {
    id: '2',
    name: 'Sophia',
    age: 28,
    bio: 'Yoga instructor & wellness enthusiast 🧘‍♀️ Spreading good vibes ✨',
    photos: [
      'https://images.unsplash.com/photo-1524504388940-b1c1722653e1?w=400',
      'https://images.unsplash.com/photo-1488426862026-3ee34a7d66df?w=400',
    ],
    location: 'Brooklyn, NY',
    distance: 5,
    interests: ['Yoga', 'Meditation', 'Healthy Living', 'Music'],
    occupation: 'Yoga Instructor',
    education: 'Columbia University',
    isOnline: false,
    lastSeen: '2 hours ago',
    verified: true,
  },
  {
    id: '3',
    name: 'Isabella',
    age: 24,
    bio: 'Artist by day, foodie by night 🎨🍕 Always looking for new experiences!',
    photos: [
      'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=400',
      'https://images.unsplash.com/photo-1506863530036-1efeddceb993?w=400',
      'https://images.unsplash.com/photo-1525134479668-1bee5c7c6845?w=400',
    ],
    location: 'Manhattan, NY',
    distance: 3,
    interests: ['Art', 'Food', 'Museums', 'Dancing'],
    occupation: 'Artist',
    education: 'Parsons School of Design',
    isOnline: true,
    verified: false,
  },
  {
    id: '4',
    name: 'Olivia',
    age: 26,
    bio: 'Tech enthusiast & dog lover 🐕 Building the future one app at a time 💻',
    photos: [
      'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400',
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400',
    ],
    location: 'Queens, NY',
    distance: 8,
    interests: ['Technology', 'Dogs', 'Gaming', 'Startups'],
    occupation: 'Software Engineer',
    education: 'MIT',
    isOnline: false,
    lastSeen: '1 day ago',
    verified: true,
  },
  {
    id: '5',
    name: 'Ava',
    age: 27,
    bio: 'Fitness coach & nutrition expert 💪 Let\'s sweat together and grab smoothies after!',
    photos: [
      'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400',
      'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=400',
      'https://images.unsplash.com/photo-1521146764736-56c929d59c83?w=400',
    ],
    location: 'Bronx, NY',
    distance: 12,
    interests: ['Fitness', 'Nutrition', 'Running', 'Cooking'],
    occupation: 'Fitness Coach',
    education: 'Hunter College',
    isOnline: true,
    verified: true,
  },
];

// Mock matches
export const mockMatches: Match[] = [
  {
    id: '1',
    user: mockUsers[0],
    matchedAt: '2024-01-15T10:30:00Z',
    lastMessage: {
      id: '1',
      senderId: '1',
      receiverId: 'current-user',
      content: 'Hey! Thanks for the match 😊',
      timestamp: '2024-01-15T11:00:00Z',
      status: 'read',
      type: 'text',
    },
  },
  {
    id: '2',
    user: mockUsers[1],
    matchedAt: '2024-01-14T15:45:00Z',
    lastMessage: {
      id: '2',
      senderId: 'current-user',
      receiverId: '2',
      content: 'Would love to try that yoga class you mentioned!',
      timestamp: '2024-01-14T16:20:00Z',
      status: 'delivered',
      type: 'text',
    },
  },
];

// Mock conversations
export const mockConversations: Conversation[] = [
  {
    id: '1',
    participants: [mockUsers[0]],
    messages: [
      {
        id: '1',
        senderId: '1',
        receiverId: 'current-user',
        content: 'Hey! Thanks for the match 😊',
        timestamp: '2024-01-15T11:00:00Z',
        status: 'read',
        type: 'text',
      },
      {
        id: '2',
        senderId: 'current-user',
        receiverId: '1',
        content: 'Hi Emma! Love your photography work 📸',
        timestamp: '2024-01-15T11:05:00Z',
        status: 'read',
        type: 'text',
      },
      {
        id: '3',
        senderId: '1',
        receiverId: 'current-user',
        content: 'Thank you! Do you enjoy photography too?',
        timestamp: '2024-01-15T11:10:00Z',
        status: 'read',
        type: 'text',
      },
    ],
    lastMessage: {
      id: '3',
      senderId: '1',
      receiverId: 'current-user',
      content: 'Thank you! Do you enjoy photography too?',
      timestamp: '2024-01-15T11:10:00Z',
      status: 'read',
      type: 'text',
    },
    unreadCount: 0,
  },
  {
    id: '2',
    participants: [mockUsers[1]],
    messages: [
      {
        id: '4',
        senderId: 'current-user',
        receiverId: '2',
        content: 'Would love to try that yoga class you mentioned!',
        timestamp: '2024-01-14T16:20:00Z',
        status: 'delivered',
        type: 'text',
      },
    ],
    lastMessage: {
      id: '4',
      senderId: 'current-user',
      receiverId: '2',
      content: 'Would love to try that yoga class you mentioned!',
      timestamp: '2024-01-14T16:20:00Z',
      status: 'delivered',
      type: 'text',
    },
    unreadCount: 0,
  },
];
