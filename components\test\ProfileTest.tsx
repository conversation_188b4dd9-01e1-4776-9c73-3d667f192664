import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Platform,
} from 'react-native';
import { useProfileStore } from '@/stores/profileStore';
import { theme } from '@/constants/theme';
import * as Haptics from 'expo-haptics';
import {
  User,
  Settings,
  Camera,
  TrendingUp,
  CheckCircle,
  XCircle,
  RefreshCw,
  Database,
  Zap,
} from 'lucide-react-native';

export default function ProfileTest() {
  const {
    profile,
    settings,
    analytics,
    isLoading,
    isUpdating,
    isUploadingPhoto,
    error,
    loadProfile,
    updateProfile,
    updateSettings,
    uploadPhoto,
    deletePhoto,
    setMainPhoto,
    validateProfile,
    calculateCompletion,
    loadAnalytics,
    clearError,
    reset,
  } = useProfileStore();

  const [testResults, setTestResults] = useState<{ [key: string]: boolean }>({});
  const [isRunningTests, setIsRunningTests] = useState(false);

  useEffect(() => {
    loadProfile();
    loadAnalytics();
  }, []);

  const runTest = async (testName: string, testFn: () => Promise<boolean>) => {
    try {
      const result = await testFn();
      setTestResults(prev => ({ ...prev, [testName]: result }));
      return result;
    } catch (error) {
      console.error(`Test ${testName} failed:`, error);
      setTestResults(prev => ({ ...prev, [testName]: false }));
      return false;
    }
  };

  const runAllTests = async () => {
    setIsRunningTests(true);
    setTestResults({});

    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }

    const tests = [
      {
        name: 'Profile Loading',
        test: async () => {
          await loadProfile();
          return profile !== null;
        }
      },
      {
        name: 'Profile Update',
        test: async () => {
          if (!profile) return false;
          const originalBio = profile.bio;
          const testBio = `Test bio updated at ${Date.now()}`;
          
          await updateProfile({ bio: testBio });
          const updated = profile.bio === testBio;
          
          // Restore original bio
          await updateProfile({ bio: originalBio });
          return updated;
        }
      },
      {
        name: 'Settings Update',
        test: async () => {
          if (!settings) return false;
          const originalDistance = settings.dating.maxDistance;
          const testDistance = originalDistance === 50 ? 75 : 50;
          
          await updateSettings({
            dating: {
              ...settings.dating,
              maxDistance: testDistance
            }
          });
          
          const updated = settings.dating.maxDistance === testDistance;
          
          // Restore original setting
          await updateSettings({
            dating: {
              ...settings.dating,
              maxDistance: originalDistance
            }
          });
          
          return updated;
        }
      },
      {
        name: 'Profile Validation',
        test: async () => {
          if (!profile) return false;
          const validation = validateProfile(profile);
          return validation.isValid;
        }
      },
      {
        name: 'Completion Calculation',
        test: async () => {
          if (!profile) return false;
          const completion = calculateCompletion(profile);
          return completion > 0 && completion <= 100;
        }
      },
      {
        name: 'Analytics Loading',
        test: async () => {
          await loadAnalytics();
          return analytics !== null;
        }
      },
      {
        name: 'Photo Upload Simulation',
        test: async () => {
          try {
            // Simulate photo upload with mock data
            const mockPhotoUri = 'https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg';
            await uploadPhoto({
              uri: mockPhotoUri,
              type: 'image/jpeg',
              name: 'test-photo.jpg',
              isMain: false,
            });
            return true;
          } catch (error) {
            return false;
          }
        }
      },
      {
        name: 'Error Handling',
        test: async () => {
          // Test error clearing
          clearError();
          return error === null;
        }
      },
      {
        name: 'Data Persistence',
        test: async () => {
          // Test that profile data persists
          return profile !== null && settings !== null;
        }
      },
      {
        name: 'Store Reset',
        test: async () => {
          // Test store reset functionality
          const originalProfile = profile;
          reset();
          const resetSuccessful = profile === null;

          // Restore profile
          await loadProfile();
          return resetSuccessful && profile !== null;
        }
      },
      {
        name: 'Navigation Integration',
        test: async () => {
          // Test navigation service integration
          try {
            const ProfileNavigationService = require('@/services/profileNavigation').default;
            return typeof ProfileNavigationService.navigateToEdit === 'function';
          } catch (error) {
            return false;
          }
        }
      },
      {
        name: 'Profile Utils',
        test: async () => {
          // Test profile utilities
          try {
            const ProfileUtils = require('@/services/profileUtils').default;
            if (!profile) return false;

            const validation = ProfileUtils.validateProfile(profile);
            const completion = ProfileUtils.calculateCompletion(profile);

            return validation && typeof completion === 'number';
          } catch (error) {
            return false;
          }
        }
      },
      {
        name: 'Settings Persistence',
        test: async () => {
          // Test settings persistence
          if (!settings) return false;

          const originalValue = settings.notifications.pushNotifications;
          const newValue = !originalValue;

          await updateSettings({
            ...settings,
            notifications: {
              ...settings.notifications,
              pushNotifications: newValue,
            },
          });

          const updated = settings.notifications.pushNotifications === newValue;

          // Restore original value
          await updateSettings({
            ...settings,
            notifications: {
              ...settings.notifications,
              pushNotifications: originalValue,
            },
          });

          return updated;
        }
      }
    ];

    let passedTests = 0;
    for (const { name, test } of tests) {
      const passed = await runTest(name, test);
      if (passed) passedTests++;
      
      // Small delay between tests for better UX
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    setIsRunningTests(false);

    // Show results
    Alert.alert(
      'Test Results',
      `${passedTests}/${tests.length} tests passed`,
      [{ text: 'OK' }]
    );

    if (Platform.OS !== 'web') {
      Haptics.impactAsync(
        passedTests === tests.length 
          ? Haptics.ImpactFeedbackStyle.Heavy 
          : Haptics.ImpactFeedbackStyle.Medium
      );
    }
  };

  const TestItem = ({ name, result }: { name: string; result?: boolean }) => (
    <View style={styles.testItem}>
      <Text style={styles.testName}>{name}</Text>
      <View style={styles.testResult}>
        {result === undefined ? (
          <RefreshCw size={16} color={theme.colors.gray400} />
        ) : result ? (
          <CheckCircle size={16} color={theme.colors.success} />
        ) : (
          <XCircle size={16} color={theme.colors.error} />
        )}
      </View>
    </View>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <Text style={styles.title}>Profile System Test</Text>
        <Text style={styles.subtitle}>
          Test all profile functionality including data management, validation, and persistence
        </Text>
      </View>

      {/* Current State */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Current State</Text>
        <View style={styles.stateGrid}>
          <View style={styles.stateItem}>
            <User size={20} color={theme.colors.primary} />
            <Text style={styles.stateLabel}>Profile</Text>
            <Text style={styles.stateValue}>
              {profile ? 'Loaded' : 'Not Loaded'}
            </Text>
          </View>
          <View style={styles.stateItem}>
            <Settings size={20} color={theme.colors.secondary} />
            <Text style={styles.stateLabel}>Settings</Text>
            <Text style={styles.stateValue}>
              {settings ? 'Loaded' : 'Not Loaded'}
            </Text>
          </View>
          <View style={styles.stateItem}>
            <TrendingUp size={20} color={theme.colors.accent} />
            <Text style={styles.stateLabel}>Analytics</Text>
            <Text style={styles.stateValue}>
              {analytics ? 'Loaded' : 'Not Loaded'}
            </Text>
          </View>
          <View style={styles.stateItem}>
            <Database size={20} color={theme.colors.success} />
            <Text style={styles.stateLabel}>Completion</Text>
            <Text style={styles.stateValue}>
              {profile ? `${profile.profileCompletion}%` : 'N/A'}
            </Text>
          </View>
        </View>
      </View>

      {/* Profile Info */}
      {profile && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Profile Information</Text>
          <View style={styles.infoCard}>
            <Text style={styles.infoItem}>
              <Text style={styles.infoLabel}>Name: </Text>
              {profile.name}
            </Text>
            <Text style={styles.infoItem}>
              <Text style={styles.infoLabel}>Age: </Text>
              {profile.age}
            </Text>
            <Text style={styles.infoItem}>
              <Text style={styles.infoLabel}>Location: </Text>
              {profile.location.city}, {profile.location.state}
            </Text>
            <Text style={styles.infoItem}>
              <Text style={styles.infoLabel}>Photos: </Text>
              {profile.photos.length}
            </Text>
            <Text style={styles.infoItem}>
              <Text style={styles.infoLabel}>Interests: </Text>
              {profile.interests.length}
            </Text>
            <Text style={styles.infoItem}>
              <Text style={styles.infoLabel}>Verified: </Text>
              {profile.verified ? 'Yes' : 'No'}
            </Text>
          </View>
        </View>
      )}

      {/* Test Controls */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Test Controls</Text>
        <TouchableOpacity
          style={[styles.testButton, isRunningTests && styles.testButtonDisabled]}
          onPress={runAllTests}
          disabled={isRunningTests}
        >
          <Zap size={20} color="white" />
          <Text style={styles.testButtonText}>
            {isRunningTests ? 'Running Tests...' : 'Run All Tests'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Test Results */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Test Results</Text>
        <View style={styles.testsContainer}>
          <TestItem name="Profile Loading" result={testResults['Profile Loading']} />
          <TestItem name="Profile Update" result={testResults['Profile Update']} />
          <TestItem name="Settings Update" result={testResults['Settings Update']} />
          <TestItem name="Profile Validation" result={testResults['Profile Validation']} />
          <TestItem name="Completion Calculation" result={testResults['Completion Calculation']} />
          <TestItem name="Analytics Loading" result={testResults['Analytics Loading']} />
          <TestItem name="Photo Upload Simulation" result={testResults['Photo Upload Simulation']} />
          <TestItem name="Error Handling" result={testResults['Error Handling']} />
          <TestItem name="Data Persistence" result={testResults['Data Persistence']} />
          <TestItem name="Store Reset" result={testResults['Store Reset']} />
          <TestItem name="Navigation Integration" result={testResults['Navigation Integration']} />
          <TestItem name="Profile Utils" result={testResults['Profile Utils']} />
          <TestItem name="Settings Persistence" result={testResults['Settings Persistence']} />
        </View>
      </View>

      {/* Status Indicators */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Status Indicators</Text>
        <View style={styles.statusGrid}>
          <View style={[styles.statusItem, isLoading && styles.statusActive]}>
            <Text style={styles.statusLabel}>Loading</Text>
          </View>
          <View style={[styles.statusItem, isUpdating && styles.statusActive]}>
            <Text style={styles.statusLabel}>Updating</Text>
          </View>
          <View style={[styles.statusItem, isUploadingPhoto && styles.statusActive]}>
            <Text style={styles.statusLabel}>Uploading</Text>
          </View>
          <View style={[styles.statusItem, error && styles.statusError]}>
            <Text style={styles.statusLabel}>Error</Text>
          </View>
        </View>
        {error && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
          </View>
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    padding: 20,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray200,
  },
  title: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: theme.colors.text,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
    lineHeight: 22,
  },
  section: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    marginVertical: 10,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 16,
  },
  stateGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  stateItem: {
    flex: 1,
    minWidth: '45%',
    alignItems: 'center',
    padding: 12,
    backgroundColor: theme.colors.gray50,
    borderRadius: 12,
  },
  stateLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: theme.colors.gray600,
    marginTop: 4,
  },
  stateValue: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginTop: 2,
  },
  infoCard: {
    backgroundColor: theme.colors.gray50,
    borderRadius: 12,
    padding: 16,
  },
  infoItem: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text,
    marginBottom: 8,
  },
  infoLabel: {
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.gray700,
  },
  testButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.primary,
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  testButtonDisabled: {
    opacity: 0.6,
  },
  testButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  testsContainer: {
    gap: 8,
  },
  testItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: theme.colors.gray50,
    borderRadius: 8,
  },
  testName: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: theme.colors.text,
  },
  testResult: {
    width: 20,
    alignItems: 'center',
  },
  statusGrid: {
    flexDirection: 'row',
    gap: 8,
  },
  statusItem: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: theme.colors.gray200,
    borderRadius: 8,
    alignItems: 'center',
  },
  statusActive: {
    backgroundColor: theme.colors.primary + '20',
  },
  statusError: {
    backgroundColor: theme.colors.error + '20',
  },
  statusLabel: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
  },
  errorContainer: {
    marginTop: 12,
    padding: 12,
    backgroundColor: theme.colors.error + '10',
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.error,
  },
  errorText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.error,
  },
});
