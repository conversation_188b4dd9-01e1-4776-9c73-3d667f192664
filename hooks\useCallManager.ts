import { useState, useEffect, useRef } from 'react';
import { WebRTCService } from '@/services/webrtc';
import { CallSession, User } from '@/types/messaging';

export function useCallManager() {
  const [currentCall, setCurrentCall] = useState<CallSession | null>(null);
  const [incomingCall, setIncomingCall] = useState<CallSession | null>(null);
  const [localStream, setLocalStream] = useState<MediaStream | null>(null);
  const [remoteStream, setRemoteStream] = useState<MediaStream | null>(null);
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [isSpeakerEnabled, setIsSpeakerEnabled] = useState(false);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  
  const webrtcServiceRef = useRef<WebRTCService | null>(null);

  useEffect(() => {
    webrtcServiceRef.current = new WebRTCService();
    
    const webrtcService = webrtcServiceRef.current;
    
    webrtcService.on('localStream', setLocalStream);
    webrtcService.on('remoteStream', setRemoteStream);
    webrtcService.on('callEnded', () => {
      setCurrentCall(null);
      setLocalStream(null);
      setRemoteStream(null);
      setIsVideoEnabled(true);
      setIsAudioEnabled(true);
      setIsScreenSharing(false);
      setIsRecording(false);
    });

    return () => {
      webrtcService.cleanup();
    };
  }, []);

  const startCall = async (otherUser: User, type: 'audio' | 'video') => {
    if (!webrtcServiceRef.current) return;

    const callSession: CallSession = {
      id: Date.now().toString(),
      type,
      participants: [otherUser], // Current user will be added by the service
      status: 'outgoing',
      startTime: new Date(),
    };

    setCurrentCall(callSession);
    
    try {
      await webrtcServiceRef.current.startCall(type);
    } catch (error) {
      console.error('Failed to start call:', error);
      setCurrentCall(null);
    }
  };

  const answerCall = async (callSession: CallSession) => {
    if (!webrtcServiceRef.current) return;

    setCurrentCall(callSession);
    setIncomingCall(null);
    
    try {
      // In a real implementation, you'd get the offer from the call session
      const offer = {} as RTCSessionDescriptionInit;
      await webrtcServiceRef.current.answerCall(offer);
    } catch (error) {
      console.error('Failed to answer call:', error);
      setCurrentCall(null);
    }
  };

  const declineCall = () => {
    setIncomingCall(null);
  };

  const endCall = () => {
    if (webrtcServiceRef.current) {
      webrtcServiceRef.current.endCall();
    }
    setCurrentCall(null);
  };

  const toggleVideo = async () => {
    if (webrtcServiceRef.current) {
      const enabled = await webrtcServiceRef.current.toggleVideo();
      setIsVideoEnabled(enabled);
      return enabled;
    }
    return false;
  };

  const toggleAudio = async () => {
    if (webrtcServiceRef.current) {
      const enabled = await webrtcServiceRef.current.toggleAudio();
      setIsAudioEnabled(enabled);
      return enabled;
    }
    return false;
  };

  const toggleSpeaker = () => {
    setIsSpeakerEnabled(!isSpeakerEnabled);
  };

  const startScreenShare = async () => {
    if (webrtcServiceRef.current) {
      await webrtcServiceRef.current.startScreenShare();
      setIsScreenSharing(true);
    }
  };

  const stopScreenShare = async () => {
    if (webrtcServiceRef.current) {
      await webrtcServiceRef.current.stopScreenShare();
      setIsScreenSharing(false);
    }
  };

  const startRecording = () => {
    if (webrtcServiceRef.current) {
      webrtcServiceRef.current.startRecording();
      setIsRecording(true);
    }
  };

  const stopRecording = () => {
    if (webrtcServiceRef.current) {
      webrtcServiceRef.current.stopRecording();
      setIsRecording(false);
    }
  };

  return {
    currentCall,
    incomingCall,
    localStream,
    remoteStream,
    isVideoEnabled,
    isAudioEnabled,
    isSpeakerEnabled,
    isScreenSharing,
    isRecording,
    startCall,
    answerCall,
    declineCall,
    endCall,
    toggleVideo,
    toggleAudio,
    toggleSpeaker,
    startScreenShare,
    stopScreenShare,
    startRecording,
    stopRecording,
    setIncomingCall,
  };
}