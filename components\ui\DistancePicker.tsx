import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Platform,
} from 'react-native';
import { MapPin, Globe } from 'lucide-react-native';
import { theme } from '../../constants/theme';
import { triggerHaptic } from '../../utils/haptics';

interface DistancePickerProps {
  initialDistance?: number;
  onDistanceChange?: (distance: number) => void;
  units?: 'km' | 'miles';
  onUnitsChange?: (units: 'km' | 'miles') => void;
  title?: string;
  disabled?: boolean;
  showUnitsToggle?: boolean;
}

export default function DistancePicker({
  initialDistance = 50,
  onDistanceChange,
  units = 'km',
  onUnitsChange,
  title = 'Maximum Distance',
  disabled = false,
  showUnitsToggle = true,
}: DistancePickerProps) {
  const [selectedDistance, setSelectedDistance] = useState(initialDistance);
  const [selectedUnits, setSelectedUnits] = useState(units);

  const handleHaptic = () => {
    triggerHaptic.light();
  };

  const updateDistance = (distance: number) => {
    setSelectedDistance(distance);
    onDistanceChange?.(distance);
    handleHaptic();
  };

  const toggleUnits = () => {
    const newUnits = selectedUnits === 'km' ? 'miles' : 'km';
    setSelectedUnits(newUnits);
    onUnitsChange?.(newUnits);
    
    // Convert distance when units change
    let convertedDistance = selectedDistance;
    if (newUnits === 'miles' && selectedUnits === 'km') {
      convertedDistance = Math.round(selectedDistance * 0.621371);
    } else if (newUnits === 'km' && selectedUnits === 'miles') {
      convertedDistance = Math.round(selectedDistance * 1.60934);
    }
    
    setSelectedDistance(convertedDistance);
    onDistanceChange?.(convertedDistance);
    handleHaptic();
  };

  const generateDistances = () => {
    const distances = [];
    const maxDistance = selectedUnits === 'km' ? 200 : 125; // ~200km = ~125 miles
    const step = selectedUnits === 'km' ? 5 : 3;
    
    // Add some common short distances
    if (selectedUnits === 'km') {
      distances.push(1, 2, 5, 10, 15, 20, 25);
    } else {
      distances.push(1, 2, 3, 5, 10, 15, 20);
    }
    
    // Add regular intervals
    for (let i = selectedUnits === 'km' ? 30 : 25; i <= maxDistance; i += step) {
      distances.push(i);
    }
    
    // Add "unlimited" option
    distances.push(999);
    
    return [...new Set(distances)].sort((a, b) => a - b);
  };

  const formatDistance = (distance: number) => {
    if (distance >= 999) {
      return 'Unlimited';
    }
    return `${distance} ${selectedUnits}`;
  };

  const getDistanceDescription = (distance: number) => {
    if (distance >= 999) {
      return 'Show profiles from anywhere';
    }
    
    if (selectedUnits === 'km') {
      if (distance <= 5) return 'Very close by';
      if (distance <= 15) return 'Nearby';
      if (distance <= 50) return 'Local area';
      if (distance <= 100) return 'Extended area';
      return 'Wide area';
    } else {
      if (distance <= 3) return 'Very close by';
      if (distance <= 10) return 'Nearby';
      if (distance <= 30) return 'Local area';
      if (distance <= 60) return 'Extended area';
      return 'Wide area';
    }
  };

  const distances = generateDistances();

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{title}</Text>
      
      <View style={styles.currentDistance}>
        <MapPin size={24} color={theme.colors.primary} />
        <View style={styles.distanceInfo}>
          <Text style={styles.currentDistanceText}>
            {formatDistance(selectedDistance)}
          </Text>
          <Text style={styles.distanceDescription}>
            {getDistanceDescription(selectedDistance)}
          </Text>
        </View>
      </View>

      {showUnitsToggle && (
        <View style={styles.unitsToggle}>
          <Text style={styles.unitsLabel}>Units:</Text>
          <TouchableOpacity
            style={[
              styles.unitsButton,
              selectedUnits === 'km' && styles.unitsButtonActive,
            ]}
            onPress={toggleUnits}
            disabled={disabled}
          >
            <Globe size={16} color={selectedUnits === 'km' ? 'white' : theme.colors.primary} />
            <Text
              style={[
                styles.unitsButtonText,
                selectedUnits === 'km' && styles.unitsButtonTextActive,
              ]}
            >
              Kilometers
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.unitsButton,
              selectedUnits === 'miles' && styles.unitsButtonActive,
            ]}
            onPress={toggleUnits}
            disabled={disabled}
          >
            <Globe size={16} color={selectedUnits === 'miles' ? 'white' : theme.colors.primary} />
            <Text
              style={[
                styles.unitsButtonText,
                selectedUnits === 'miles' && styles.unitsButtonTextActive,
              ]}
            >
              Miles
            </Text>
          </TouchableOpacity>
        </View>
      )}

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Select Distance</Text>
        <ScrollView 
          style={styles.distancesScroll}
          contentContainerStyle={styles.distancesContainer}
          showsVerticalScrollIndicator={false}
        >
          {distances.map((distance) => (
            <TouchableOpacity
              key={distance}
              style={[
                styles.distanceButton,
                selectedDistance === distance && styles.distanceButtonSelected,
              ]}
              onPress={() => updateDistance(distance)}
              disabled={disabled}
            >
              <Text
                style={[
                  styles.distanceButtonText,
                  selectedDistance === distance && styles.distanceButtonTextSelected,
                ]}
              >
                {formatDistance(distance)}
              </Text>
              {selectedDistance === distance && (
                <View style={styles.selectedIndicator} />
              )}
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      <View style={styles.infoBox}>
        <Text style={styles.infoText}>
          💡 Tip: You can always change this later in your settings. A larger radius means more potential matches!
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: 16,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 16,
    textAlign: 'center',
  },
  currentDistance: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    paddingVertical: 16,
    paddingHorizontal: 20,
    backgroundColor: `${theme.colors.primary}10`,
    borderRadius: 12,
  },
  distanceInfo: {
    marginLeft: 12,
    flex: 1,
  },
  currentDistanceText: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: theme.colors.primary,
    marginBottom: 2,
  },
  distanceDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
  },
  unitsToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    paddingHorizontal: 4,
  },
  unitsLabel: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: theme.colors.text,
    marginRight: 12,
  },
  unitsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginHorizontal: 4,
    borderRadius: 20,
    backgroundColor: theme.colors.gray100,
    borderWidth: 1,
    borderColor: theme.colors.gray200,
  },
  unitsButtonActive: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  unitsButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: theme.colors.text,
    marginLeft: 6,
  },
  unitsButtonTextActive: {
    color: 'white',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 12,
  },
  distancesScroll: {
    maxHeight: 300,
  },
  distancesContainer: {
    paddingVertical: 4,
  },
  distanceButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginVertical: 2,
    borderRadius: 12,
    backgroundColor: theme.colors.gray50,
    borderWidth: 1,
    borderColor: theme.colors.gray100,
  },
  distanceButtonSelected: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  distanceButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: theme.colors.text,
  },
  distanceButtonTextSelected: {
    color: 'white',
  },
  selectedIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'white',
  },
  infoBox: {
    padding: 16,
    backgroundColor: theme.colors.gray50,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.primary,
  },
  infoText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
    lineHeight: 20,
  },
});
