import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Alert,
  ScrollView,
} from 'react-native';
import { useAuth } from '@/contexts/AuthContext';

export default function AuthTester() {
  const { user, isLoading, signIn, signUp, signOut } = useAuth();
  const [testEmail, setTestEmail] = useState('<EMAIL>');
  const [testPassword, setTestPassword] = useState('password123');
  const [testFirstName, setTestFirstName] = useState('John');
  const [testLastName, setTestLastName] = useState('Doe');

  const handleTestLogin = async () => {
    try {
      await signIn(testEmail, testPassword);
      Alert.alert('Success', 'Login test successful!');
    } catch (error) {
      Alert.alert('Error', 'Login test failed');
    }
  };

  const handleTestRegister = async () => {
    try {
      await signUp({
        firstName: testFirstName,
        lastName: testLastName,
        email: testEmail,
        password: testPassword,
      });
      Alert.alert('Success', 'Registration test successful!');
    } catch (error) {
      Alert.alert('Error', 'Registration test failed');
    }
  };

  const handleTestSignOut = async () => {
    try {
      await signOut();
      Alert.alert('Success', 'Sign out test successful!');
    } catch (error) {
      Alert.alert('Error', 'Sign out test failed');
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Authentication Tester</Text>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Current Status</Text>
        <Text style={styles.statusText}>
          Loading: {isLoading ? 'Yes' : 'No'}
        </Text>
        <Text style={styles.statusText}>
          User: {user ? `${user.firstName} ${user.lastName} (${user.email})` : 'Not logged in'}
        </Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Test Credentials</Text>
        
        <TextInput
          style={styles.input}
          placeholder="First Name"
          value={testFirstName}
          onChangeText={setTestFirstName}
        />
        
        <TextInput
          style={styles.input}
          placeholder="Last Name"
          value={testLastName}
          onChangeText={setTestLastName}
        />
        
        <TextInput
          style={styles.input}
          placeholder="Email"
          value={testEmail}
          onChangeText={setTestEmail}
          keyboardType="email-address"
          autoCapitalize="none"
        />
        
        <TextInput
          style={styles.input}
          placeholder="Password"
          value={testPassword}
          onChangeText={setTestPassword}
          secureTextEntry
        />
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Test Actions</Text>
        
        <TouchableOpacity
          style={[styles.button, styles.loginButton]}
          onPress={handleTestLogin}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Test Login</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.button, styles.registerButton]}
          onPress={handleTestRegister}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Test Register</Text>
        </TouchableOpacity>
        
        {user && (
          <TouchableOpacity
            style={[styles.button, styles.signOutButton]}
            onPress={handleTestSignOut}
            disabled={isLoading}
          >
            <Text style={styles.buttonText}>Test Sign Out</Text>
          </TouchableOpacity>
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: 'white',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  section: {
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  statusText: {
    fontSize: 14,
    marginBottom: 5,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    marginBottom: 10,
    backgroundColor: 'white',
  },
  button: {
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 10,
  },
  loginButton: {
    backgroundColor: '#8B5CF6',
  },
  registerButton: {
    backgroundColor: '#EC4899',
  },
  signOutButton: {
    backgroundColor: '#EF4444',
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
  },
});
