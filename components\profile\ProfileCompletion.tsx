import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useProfileStore } from '@/stores/profileStore';
import { profileIntegrationService } from '@/services/profileIntegration';
import ProfileVerification from './ProfileVerification';
import { theme } from '@/constants/theme';
import * as Haptics from 'expo-haptics';
import {
  Camera,
  Edit3,
  Heart,
  MapPin,
  Briefcase,
  Instagram,
  Shield,
  CheckCircle,
  ArrowRight,
} from 'lucide-react-native';

interface ProfileCompletionProps {
  showTitle?: boolean;
  compact?: boolean;
}

export default function ProfileCompletion({
  showTitle = true,
  compact = false
}: ProfileCompletionProps) {
  const router = useRouter();
  const { profile } = useProfileStore();
  const [showVerification, setShowVerification] = useState(false);

  if (!profile) return null;

  const suggestions = profileIntegrationService.getProfileCompletionSuggestions();
  const completion = profile.profileCompletion;

  const completionItems = [
    {
      id: 'photos',
      title: 'Add Photos',
      subtitle: `${profile.photos.length}/4+ photos`,
      completed: profile.photos.length >= 4,
      icon: Camera,
      action: () => router.push('/profile/photos'),
      priority: 'high' as const,
    },
    {
      id: 'bio',
      title: 'Write Bio',
      subtitle: `${profile.bio.length}/50+ characters`,
      completed: profile.bio.length >= 50,
      icon: Edit3,
      action: () => router.push('/profile/edit'),
      priority: 'high' as const,
    },
    {
      id: 'interests',
      title: 'Add Interests',
      subtitle: `${profile.interests.length}/5+ interests`,
      completed: profile.interests.length >= 5,
      icon: Heart,
      action: () => router.push('/profile/edit'),
      priority: 'medium' as const,
    },
    {
      id: 'location',
      title: 'Set Location',
      subtitle: profile.location.city ? 'Complete' : 'Add your city',
      completed: !!profile.location.city,
      icon: MapPin,
      action: () => router.push('/profile/edit'),
      priority: 'medium' as const,
    },
    {
      id: 'occupation',
      title: 'Add Occupation',
      subtitle: profile.occupation ? 'Complete' : 'Add your job',
      completed: !!profile.occupation,
      icon: Briefcase,
      action: () => router.push('/profile/edit'),
      priority: 'low' as const,
    },
    {
      id: 'social',
      title: 'Connect Social Media',
      subtitle: profile.socialMedia && Object.keys(profile.socialMedia).length > 0 
        ? 'Connected' : 'Link accounts',
      completed: profile.socialMedia && Object.keys(profile.socialMedia).length > 0,
      icon: Instagram,
      action: () => router.push('/profile/edit'),
      priority: 'low' as const,
    },
    {
      id: 'verification',
      title: 'Verify Profile',
      subtitle: profile.verified ? 'Verified' : 'Get verified',
      completed: profile.verified,
      icon: Shield,
      action: () => setShowVerification(true),
      priority: 'medium' as const,
    },
  ];

  const incompleteItems = completionItems.filter(item => !item.completed);
  const highPriorityItems = incompleteItems.filter(item => item.priority === 'high');

  const handleItemPress = (item: typeof completionItems[0]) => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    item.action();
  };

  const getCompletionColor = (percentage: number) => {
    if (percentage >= 90) return theme.colors.success;
    if (percentage >= 70) return theme.colors.accent;
    if (percentage >= 50) return theme.colors.secondary;
    return theme.colors.error;
  };

  const getCompletionMessage = (percentage: number) => {
    if (percentage >= 90) return 'Your profile looks amazing! 🎉';
    if (percentage >= 70) return 'Almost there! A few more touches needed.';
    if (percentage >= 50) return 'Good progress! Keep building your profile.';
    return 'Let\'s complete your profile to get better matches!';
  };

  if (compact) {
    return (
      <View style={styles.compactContainer}>
        <View style={styles.compactHeader}>
          <View style={styles.compactProgress}>
            <View style={styles.compactProgressBar}>
              <View 
                style={[
                  styles.compactProgressFill, 
                  { 
                    width: `${completion}%`,
                    backgroundColor: getCompletionColor(completion)
                  }
                ]} 
              />
            </View>
            <Text style={styles.compactPercentage}>{completion}%</Text>
          </View>
          {highPriorityItems.length > 0 && (
            <TouchableOpacity 
              style={styles.compactAction}
              onPress={() => handleItemPress(highPriorityItems[0])}
            >
              <Text style={styles.compactActionText}>Complete</Text>
              <ArrowRight size={16} color={theme.colors.primary} />
            </TouchableOpacity>
          )}
        </View>
        {highPriorityItems.length > 0 && (
          <Text style={styles.compactSuggestion}>
            {highPriorityItems[0].title}: {highPriorityItems[0].subtitle}
          </Text>
        )}
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {showTitle && (
        <View style={styles.header}>
          <Text style={styles.title}>Profile Completion</Text>
          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <View 
                style={[
                  styles.progressFill, 
                  { 
                    width: `${completion}%`,
                    backgroundColor: getCompletionColor(completion)
                  }
                ]} 
              />
            </View>
            <Text style={[styles.percentage, { color: getCompletionColor(completion) }]}>
              {completion}%
            </Text>
          </View>
          <Text style={styles.message}>{getCompletionMessage(completion)}</Text>
        </View>
      )}

      <View style={styles.itemsContainer}>
        {completionItems.map((item) => (
          <TouchableOpacity
            key={item.id}
            style={[
              styles.completionItem,
              item.completed && styles.completedItem,
              item.priority === 'high' && !item.completed && styles.highPriorityItem,
            ]}
            onPress={() => handleItemPress(item)}
            disabled={item.completed}
          >
            <View style={styles.itemLeft}>
              <View style={[
                styles.itemIcon,
                item.completed && styles.completedIcon,
                item.priority === 'high' && !item.completed && styles.highPriorityIcon,
              ]}>
                {item.completed ? (
                  <CheckCircle size={20} color={theme.colors.success} />
                ) : (
                  <item.icon 
                    size={20} 
                    color={
                      item.priority === 'high' 
                        ? theme.colors.error 
                        : theme.colors.primary
                    } 
                  />
                )}
              </View>
              <View style={styles.itemContent}>
                <Text style={[
                  styles.itemTitle,
                  item.completed && styles.completedText,
                ]}>
                  {item.title}
                </Text>
                <Text style={[
                  styles.itemSubtitle,
                  item.completed && styles.completedSubtext,
                ]}>
                  {item.subtitle}
                </Text>
              </View>
            </View>
            {!item.completed && (
              <ArrowRight size={16} color={theme.colors.gray400} />
            )}
          </TouchableOpacity>
        ))}
      </View>

      {suggestions.length > 0 && (
        <View style={styles.suggestionsContainer}>
          <Text style={styles.suggestionsTitle}>Quick Tips</Text>
          {suggestions.slice(0, 3).map((suggestion, index) => (
            <Text key={index} style={styles.suggestion}>
              • {suggestion}
            </Text>
          ))}
        </View>
      )}

      {/* Verification Modal */}
      <ProfileVerification
        visible={showVerification}
        onClose={() => setShowVerification(false)}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  compactContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    marginBottom: 20,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 12,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressBar: {
    flex: 1,
    height: 8,
    backgroundColor: theme.colors.gray200,
    borderRadius: 4,
    marginRight: 12,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  percentage: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    minWidth: 40,
  },
  message: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
  },
  compactHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  compactProgress: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
  },
  compactProgressBar: {
    flex: 1,
    height: 6,
    backgroundColor: theme.colors.gray200,
    borderRadius: 3,
    marginRight: 8,
  },
  compactProgressFill: {
    height: '100%',
    borderRadius: 3,
  },
  compactPercentage: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    minWidth: 35,
  },
  compactAction: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  compactActionText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.primary,
  },
  compactSuggestion: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
  },
  itemsContainer: {
    gap: 12,
  },
  completionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: theme.colors.gray50,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  completedItem: {
    backgroundColor: theme.colors.success + '10',
    borderColor: theme.colors.success + '30',
  },
  highPriorityItem: {
    backgroundColor: theme.colors.error + '10',
    borderColor: theme.colors.error + '30',
  },
  itemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  itemIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  completedIcon: {
    backgroundColor: theme.colors.success + '20',
  },
  highPriorityIcon: {
    backgroundColor: theme.colors.error + '20',
  },
  itemContent: {
    flex: 1,
  },
  itemTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 2,
  },
  completedText: {
    color: theme.colors.success,
  },
  itemSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
  },
  completedSubtext: {
    color: theme.colors.success + 'AA',
  },
  suggestionsContainer: {
    marginTop: 20,
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: theme.colors.gray200,
  },
  suggestionsTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 12,
  },
  suggestion: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
    marginBottom: 6,
    lineHeight: 20,
  },
});
