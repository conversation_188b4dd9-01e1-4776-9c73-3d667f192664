import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Platform,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { theme } from '../../constants/theme';
import { triggerHaptic } from '../../utils/haptics';

interface RangePickerProps {
  min: number;
  max: number;
  step?: number;
  initialMinValue?: number;
  initialMaxValue?: number;
  onValueChange?: (min: number, max: number) => void;
  formatLabel?: (value: number) => string;
  title?: string;
  disabled?: boolean;
}

export default function RangePicker({
  min,
  max,
  step = 1,
  initialMinValue = min,
  initialMaxValue = max,
  onValueChange,
  formatLabel = (value) => value.toString(),
  title = 'Select Range',
  disabled = false,
}: RangePickerProps) {
  const [minValue, setMinValue] = useState(initialMinValue);
  const [maxValue, setMaxValue] = useState(initialMaxValue);

  const handleHaptic = () => {
    triggerHaptic.light();
  };

  const updateMinValue = (value: number) => {
    if (value <= maxValue && value >= min) {
      setMinValue(value);
      onValueChange?.(value, maxValue);
      handleHaptic();
    }
  };

  const updateMaxValue = (value: number) => {
    if (value >= minValue && value <= max) {
      setMaxValue(value);
      onValueChange?.(minValue, value);
      handleHaptic();
    }
  };

  const generateValues = () => {
    const values = [];
    for (let i = min; i <= max; i += step) {
      values.push(i);
    }
    return values;
  };

  const values = generateValues();

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{title}</Text>

      <View style={styles.currentValues}>
        <Text style={styles.currentValue}>
          {formatLabel(minValue)} - {formatLabel(maxValue)}
        </Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Minimum Value</Text>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.valuesScroll}
          contentContainerStyle={styles.valuesContainer}
        >
          {values.map((value) => (
            <TouchableOpacity
              key={`min-${value}`}
              style={[
                styles.valueButton,
                minValue === value && styles.valueButtonSelected,
                value > maxValue && styles.valueButtonDisabled,
              ]}
              onPress={() => updateMinValue(value)}
              disabled={disabled || value > maxValue}
            >
              <Text
                style={[
                  styles.valueButtonText,
                  minValue === value && styles.valueButtonTextSelected,
                  value > maxValue && styles.valueButtonTextDisabled,
                ]}
              >
                {formatLabel(value)}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Maximum Value</Text>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.valuesScroll}
          contentContainerStyle={styles.valuesContainer}
        >
          {values.map((value) => (
            <TouchableOpacity
              key={`max-${value}`}
              style={[
                styles.valueButton,
                maxValue === value && styles.valueButtonSelected,
                value < minValue && styles.valueButtonDisabled,
              ]}
              onPress={() => updateMaxValue(value)}
              disabled={disabled || value < minValue}
            >
              <Text
                style={[
                  styles.valueButtonText,
                  maxValue === value && styles.valueButtonTextSelected,
                  value < minValue && styles.valueButtonTextDisabled,
                ]}
              >
                {formatLabel(value)}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: 16,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 16,
    textAlign: 'center',
  },
  currentValues: {
    alignItems: 'center',
    marginBottom: 24,
    paddingVertical: 12,
    paddingHorizontal: 20,
    backgroundColor: `${theme.colors.primary}10`,
    borderRadius: 12,
  },
  currentValue: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: theme.colors.primary,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 12,
  },
  valuesScroll: {
    flexGrow: 0,
  },
  valuesContainer: {
    paddingHorizontal: 4,
  },
  valueButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginHorizontal: 4,
    borderRadius: 20,
    backgroundColor: theme.colors.gray100,
    borderWidth: 1,
    borderColor: theme.colors.gray200,
  },
  valueButtonSelected: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  valueButtonDisabled: {
    backgroundColor: theme.colors.gray50,
    borderColor: theme.colors.gray100,
  },
  valueButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: theme.colors.text,
    textAlign: 'center',
  },
  valueButtonTextSelected: {
    color: 'white',
  },
  valueButtonTextDisabled: {
    color: theme.colors.gray400,
  },
});
