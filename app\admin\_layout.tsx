import { Stack } from 'expo-router';
import { useAuth } from '@/contexts/AuthContext';
import { Redirect } from 'expo-router';

export default function AdminLayout() {
  const { user } = useAuth();
  
  // In a real app, you would check if the user has admin privileges
  // For demo purposes, we'll allow access if user is logged in
  if (!user) {
    return <Redirect href="/auth" />;
  }

  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen name="index" />
      <Stack.Screen name="analytics" />
      <Stack.Screen name="users" />
      <Stack.Screen name="moderation" />
      <Stack.Screen name="reports" />
    </Stack>
  );
}
