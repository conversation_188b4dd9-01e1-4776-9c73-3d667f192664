import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Message, User } from '@/types/messaging';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

export interface NotificationData {
  type: 'message' | 'match' | 'like' | 'call';
  conversationId?: string;
  messageId?: string;
  senderId?: string;
  senderName?: string;
  content?: string;
  matchId?: string;
  likeId?: string;
  callId?: string;
}

class PushNotificationService {
  private expoPushToken: string | null = null;
  private notificationListener: any = null;
  private responseListener: any = null;

  async initialize(): Promise<string | null> {
    try {
      // Check if device supports push notifications
      if (!Device.isDevice) {
        console.log('Push notifications only work on physical devices');
        return null;
      }

      // Get existing permission status
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      // Request permission if not granted
      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.log('Push notification permission not granted');
        return null;
      }

      // Get push token
      const token = await Notifications.getExpoPushTokenAsync({
        projectId: 'your-expo-project-id', // Replace with your actual project ID
      });

      this.expoPushToken = token.data;
      
      // Store token locally
      await AsyncStorage.setItem('expoPushToken', this.expoPushToken);

      // Set up notification listeners
      this.setupNotificationListeners();

      // Configure notification channel for Android
      if (Platform.OS === 'android') {
        await this.setupAndroidNotificationChannel();
      }

      console.log('Push notifications initialized successfully');
      return this.expoPushToken;
    } catch (error) {
      console.error('Failed to initialize push notifications:', error);
      return null;
    }
  }

  private setupNotificationListeners() {
    // Listener for notifications received while app is foregrounded
    this.notificationListener = Notifications.addNotificationReceivedListener(
      (notification) => {
        console.log('Notification received:', notification);
        this.handleNotificationReceived(notification);
      }
    );

    // Listener for when user taps on notification
    this.responseListener = Notifications.addNotificationResponseReceivedListener(
      (response) => {
        console.log('Notification response:', response);
        this.handleNotificationResponse(response);
      }
    );
  }

  private async setupAndroidNotificationChannel() {
    await Notifications.setNotificationChannelAsync('messages', {
      name: 'Messages',
      importance: Notifications.AndroidImportance.HIGH,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#8B5CF6',
      sound: 'default',
    });

    await Notifications.setNotificationChannelAsync('matches', {
      name: 'Matches',
      importance: Notifications.AndroidImportance.HIGH,
      vibrationPattern: [0, 500, 250, 500],
      lightColor: '#EF4444',
      sound: 'default',
    });

    await Notifications.setNotificationChannelAsync('calls', {
      name: 'Calls',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 1000, 500, 1000],
      lightColor: '#10B981',
      sound: 'default',
    });
  }

  private handleNotificationReceived(notification: Notifications.Notification) {
    const data = notification.request.content.data as NotificationData;
    
    // Handle different notification types
    switch (data.type) {
      case 'message':
        this.handleMessageNotification(data);
        break;
      case 'match':
        this.handleMatchNotification(data);
        break;
      case 'like':
        this.handleLikeNotification(data);
        break;
      case 'call':
        this.handleCallNotification(data);
        break;
    }
  }

  private handleNotificationResponse(response: Notifications.NotificationResponse) {
    const data = response.notification.request.content.data as NotificationData;
    
    // Navigate to appropriate screen based on notification type
    // This would typically use your navigation service
    console.log('Handling notification tap:', data);
  }

  private handleMessageNotification(data: NotificationData) {
    // Update badge count
    this.updateBadgeCount();
    
    // You can emit events here for real-time UI updates
    // EventEmitter.emit('newMessage', data);
  }

  private handleMatchNotification(data: NotificationData) {
    // Handle match notification
    console.log('New match notification:', data);
  }

  private handleLikeNotification(data: NotificationData) {
    // Handle like notification
    console.log('New like notification:', data);
  }

  private handleCallNotification(data: NotificationData) {
    // Handle incoming call notification
    console.log('Incoming call notification:', data);
  }

  async scheduleMessageNotification(message: Message, sender: User, conversationId: string) {
    if (!this.expoPushToken) {
      console.log('No push token available');
      return;
    }

    try {
      const notificationData: NotificationData = {
        type: 'message',
        conversationId,
        messageId: message.id,
        senderId: message.senderId,
        senderName: sender.name,
        content: message.content,
      };

      await Notifications.scheduleNotificationAsync({
        content: {
          title: sender.name,
          body: this.getMessagePreview(message),
          data: notificationData,
          sound: 'default',
        },
        trigger: null, // Show immediately
      });
    } catch (error) {
      console.error('Failed to schedule message notification:', error);
    }
  }

  async scheduleMatchNotification(matchId: string, matchedUser: User) {
    if (!this.expoPushToken) return;

    try {
      const notificationData: NotificationData = {
        type: 'match',
        matchId,
        senderName: matchedUser.name,
      };

      await Notifications.scheduleNotificationAsync({
        content: {
          title: '🎉 New Match!',
          body: `You and ${matchedUser.name} liked each other!`,
          data: notificationData,
          sound: 'default',
        },
        trigger: null,
      });
    } catch (error) {
      console.error('Failed to schedule match notification:', error);
    }
  }

  async scheduleLikeNotification(likeId: string, liker: User) {
    if (!this.expoPushToken) return;

    try {
      const notificationData: NotificationData = {
        type: 'like',
        likeId,
        senderId: liker.id,
        senderName: liker.name,
      };

      await Notifications.scheduleNotificationAsync({
        content: {
          title: '💖 Someone likes you!',
          body: `${liker.name} liked your profile`,
          data: notificationData,
          sound: 'default',
        },
        trigger: null,
      });
    } catch (error) {
      console.error('Failed to schedule like notification:', error);
    }
  }

  private getMessagePreview(message: Message): string {
    switch (message.type) {
      case 'text':
        return message.content.length > 50 
          ? message.content.substring(0, 50) + '...' 
          : message.content;
      case 'image':
        return '📷 Photo';
      case 'voice':
        return '🎵 Voice message';
      case 'file':
        return '📎 File';
      case 'video':
        return '🎥 Video';
      default:
        return 'New message';
    }
  }

  private async updateBadgeCount() {
    try {
      // Get current unread count from storage or state
      const unreadCount = await this.getUnreadMessageCount();
      await Notifications.setBadgeCountAsync(unreadCount);
    } catch (error) {
      console.error('Failed to update badge count:', error);
    }
  }

  private async getUnreadMessageCount(): Promise<number> {
    try {
      // This should integrate with your message store
      const unreadCount = await AsyncStorage.getItem('unreadMessageCount');
      return unreadCount ? parseInt(unreadCount, 10) : 0;
    } catch (error) {
      return 0;
    }
  }

  async clearBadge() {
    try {
      await Notifications.setBadgeCountAsync(0);
      await AsyncStorage.setItem('unreadMessageCount', '0');
    } catch (error) {
      console.error('Failed to clear badge:', error);
    }
  }

  async getPushToken(): Promise<string | null> {
    if (this.expoPushToken) {
      return this.expoPushToken;
    }

    try {
      const storedToken = await AsyncStorage.getItem('expoPushToken');
      if (storedToken) {
        this.expoPushToken = storedToken;
        return storedToken;
      }
    } catch (error) {
      console.error('Failed to get stored push token:', error);
    }

    return null;
  }

  cleanup() {
    if (this.notificationListener) {
      Notifications.removeNotificationSubscription(this.notificationListener);
    }
    if (this.responseListener) {
      Notifications.removeNotificationSubscription(this.responseListener);
    }
  }
}

export const pushNotificationService = new PushNotificationService();
