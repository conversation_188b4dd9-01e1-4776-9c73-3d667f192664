import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { formatTimeAgo, safeParseDate, formatDate, formatTime } from '@/utils/dateUtils';

/**
 * Debug component to test date handling functionality
 */
export default function DateHandlingTest() {
  const [testResults, setTestResults] = useState<string[]>([]);

  const addResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const testDateInputs = () => {
    addResult('=== Testing Date Inputs ===');
    
    // Test various date inputs
    const testCases = [
      { input: new Date(), label: 'Date object' },
      { input: new Date().toISOString(), label: 'ISO string' },
      { input: Date.now(), label: 'Timestamp' },
      { input: new Date(Date.now() - 2 * 60 * 60 * 1000), label: '2 hours ago' },
      { input: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), label: '1 day ago (string)' },
      { input: null, label: 'null' },
      { input: undefined, label: 'undefined' },
      { input: 'invalid-date', label: 'Invalid string' },
      { input: 'not-a-date', label: 'Non-date string' },
    ];

    testCases.forEach(({ input, label }) => {
      try {
        const result = formatTimeAgo(input as any);
        addResult(`✅ ${label}: "${result}"`);
      } catch (error) {
        addResult(`❌ ${label}: Error - ${error}`);
      }
    });
  };

  const testSafeParsing = () => {
    addResult('=== Testing Safe Date Parsing ===');
    
    const testCases = [
      new Date(),
      new Date().toISOString(),
      Date.now(),
      '2023-12-25T10:30:00Z',
      'invalid-date',
      null,
      undefined,
      123456789,
    ];

    testCases.forEach((input, index) => {
      try {
        const parsed = safeParseDate(input as any);
        const result = parsed ? `Valid: ${parsed.toISOString()}` : 'null';
        addResult(`✅ Test ${index + 1}: ${result}`);
      } catch (error) {
        addResult(`❌ Test ${index + 1}: Error - ${error}`);
      }
    });
  };

  const testFormatting = () => {
    addResult('=== Testing Date Formatting ===');
    
    const now = new Date();
    const testDates = [
      now,
      new Date(now.getTime() - 30 * 1000), // 30 seconds ago
      new Date(now.getTime() - 5 * 60 * 1000), // 5 minutes ago
      new Date(now.getTime() - 2 * 60 * 60 * 1000), // 2 hours ago
      new Date(now.getTime() - 24 * 60 * 60 * 1000), // 1 day ago
      new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000), // 1 week ago
      new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000), // 1 month ago
    ];

    testDates.forEach((date, index) => {
      const timeAgo = formatTimeAgo(date);
      const formattedDate = formatDate(date);
      const formattedTime = formatTime(date);
      
      addResult(`✅ Test ${index + 1}:`);
      addResult(`   Time ago: ${timeAgo}`);
      addResult(`   Date: ${formattedDate}`);
      addResult(`   Time: ${formattedTime}`);
    });
  };

  const testSerializationIssues = () => {
    addResult('=== Testing Serialization Issues ===');
    
    // Simulate what happens when dates are serialized/deserialized
    const originalDate = new Date(Date.now() - 3 * 60 * 60 * 1000); // 3 hours ago
    const serialized = JSON.stringify({ likedAt: originalDate });
    const deserialized = JSON.parse(serialized);
    
    addResult(`Original date: ${originalDate.toISOString()}`);
    addResult(`Serialized: ${serialized}`);
    addResult(`Deserialized likedAt type: ${typeof deserialized.likedAt}`);
    addResult(`Deserialized likedAt value: ${deserialized.likedAt}`);
    
    // Test formatting the deserialized date
    const formatted = formatTimeAgo(deserialized.likedAt);
    addResult(`✅ Formatted deserialized date: "${formatted}"`);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Date Handling Test</Text>
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.button} onPress={testDateInputs}>
          <Text style={styles.buttonText}>Test Date Inputs</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.button} onPress={testSafeParsing}>
          <Text style={styles.buttonText}>Test Safe Parsing</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.button} onPress={testFormatting}>
          <Text style={styles.buttonText}>Test Formatting</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.button} onPress={testSerializationIssues}>
          <Text style={styles.buttonText}>Test Serialization</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={[styles.button, styles.clearButton]} onPress={clearResults}>
          <Text style={styles.buttonText}>Clear Results</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.resultsContainer}>
        <Text style={styles.resultsTitle}>Test Results:</Text>
        {testResults.map((result, index) => (
          <Text key={index} style={styles.resultText}>
            {result}
          </Text>
        ))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: 'white',
    margin: 10,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    maxHeight: 600,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
    color: '#EC4899',
  },
  buttonContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
    marginBottom: 15,
    gap: 5,
  },
  button: {
    backgroundColor: '#EC4899',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 5,
    marginBottom: 5,
    minWidth: 100,
  },
  clearButton: {
    backgroundColor: '#EF4444',
  },
  buttonText: {
    color: 'white',
    fontSize: 11,
    fontWeight: '600',
    textAlign: 'center',
  },
  resultsContainer: {
    flex: 1,
    maxHeight: 300,
  },
  resultsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
    color: '#374151',
  },
  resultText: {
    fontSize: 12,
    marginBottom: 2,
    color: '#374151',
    lineHeight: 16,
  },
});
