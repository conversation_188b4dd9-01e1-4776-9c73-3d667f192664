import React, { useState, useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { useRouter } from 'expo-router';
import ChatList from '@/components/messaging/ChatList';
import ChatScreen from '@/components/messaging/ChatScreen';
import CallScreen from '@/components/calling/CallScreen';
import IncomingCallModal from '@/components/calling/IncomingCallModal';
import { useMessagesStore } from '@/stores/messagesStore';
import { WebSocketService } from '@/services/websocket';
import { WebRTCService } from '@/services/webrtc';
import { FileUploadService } from '@/services/fileUpload';
import { EncryptionService } from '@/services/encryption';
import { Message, Conversation, User, CallSession } from '@/types/messaging';

// Mock data - replace with real data from your backend
const CURRENT_USER: User = {
  id: 'current-user',
  name: 'You',
  avatar: 'https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=400',
  isOnline: true,
};

const MOCK_CONVERSATIONS: Conversation[] = [
  {
    id: 'conv-1',
    participants: [
      CURRENT_USER,
      {
        id: 'user-1',
        name: 'Sophia',
        avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400',
        isOnline: true,
      }
    ],
    lastMessage: {
      id: 'msg-1',
      senderId: 'user-1',
      receiverId: 'current-user',
      content: 'Hey! Thanks for the like 😊',
      type: 'text',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      status: 'delivered',
    },
    unreadCount: 2,
    isTyping: false,
    typingUsers: [],
  },
  {
    id: 'conv-2',
    participants: [
      CURRENT_USER,
      {
        id: 'user-2',
        name: 'Olivia',
        avatar: 'https://images.pexels.com/photos/1181690/pexels-photo-1181690.jpeg?auto=compress&cs=tinysrgb&w=400',
        isOnline: false,
      }
    ],
    lastMessage: {
      id: 'msg-2',
      senderId: 'current-user',
      receiverId: 'user-2',
      content: 'Would love to grab coffee sometime!',
      type: 'text',
      timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
      status: 'read',
    },
    unreadCount: 0,
    isTyping: false,
    typingUsers: [],
  },
];

const MOCK_MESSAGES: Message[] = [
  {
    id: 'msg-1',
    senderId: 'user-1',
    receiverId: 'current-user',
    content: 'Hey! How are you doing?',
    type: 'text',
    timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000),
    status: 'read',
  },
  {
    id: 'msg-2',
    senderId: 'current-user',
    receiverId: 'user-1',
    content: 'I\'m doing great! Thanks for asking 😊',
    type: 'text',
    timestamp: new Date(Date.now() - 2.5 * 60 * 60 * 1000),
    status: 'read',
  },
  {
    id: 'msg-3',
    senderId: 'user-1',
    receiverId: 'current-user',
    content: 'That\'s awesome! Want to grab coffee this weekend?',
    type: 'text',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
    status: 'delivered',
  },
];

export default function MessagesTab() {
  const router = useRouter();
  const [currentView, setCurrentView] = useState<'list' | 'chat' | 'call'>('list');
  const [currentCall, setCurrentCall] = useState<CallSession | null>(null);
  const [incomingCall, setIncomingCall] = useState<CallSession | null>(null);
  const [showIncomingCall, setShowIncomingCall] = useState(false);

  // Messages store
  const {
    conversations,
    messages,
    currentUser,
    activeConversationId,
    setCurrentUser,
    setActiveConversation,
    fetchConversations,
    fetchMessages,
    sendMessage,
    addMessage,
    updateMessageStatus,
    markMessagesAsRead,
    setTyping,
  } = useMessagesStore();

  // Services
  const [wsService, setWsService] = useState<WebSocketService | null>(null);
  const [webrtcService, setWebrtcService] = useState<WebRTCService | null>(null);
  const [fileUploadService] = useState(new FileUploadService());
  const [encryptionService] = useState(new EncryptionService());

  // Call states
  const [localStream, setLocalStream] = useState<MediaStream | null>(null);
  const [remoteStream, setRemoteStream] = useState<MediaStream | null>(null);
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [isSpeakerEnabled, setIsSpeakerEnabled] = useState(false);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [isRecording, setIsRecording] = useState(false);

  useEffect(() => {
    // Initialize current user first
    setCurrentUser(CURRENT_USER);

    // Small delay to ensure currentUser is set before fetching conversations
    setTimeout(() => {
      fetchConversations();
    }, 100);

    // Initialize services
    const ws = new WebSocketService(CURRENT_USER.id);
    const webrtc = new WebRTCService();

    setWsService(ws);
    setWebrtcService(webrtc);

    // Setup WebSocket listeners
    ws.on('message', handleNewMessage);
    ws.on('messageStatus', handleMessageStatus);
    ws.on('typing', handleTyping);
    ws.on('callIncoming', handleIncomingCall);
    ws.on('callAnswer', handleCallAnswer);
    ws.on('callEnd', handleCallEnd);

    // Setup WebRTC listeners
    webrtc.on('localStream', setLocalStream);
    webrtc.on('remoteStream', setRemoteStream);
    webrtc.on('offer', (offer: any) => ws.send({ type: 'callOffer', payload: offer }));
    webrtc.on('answer', (answer: any) => ws.send({ type: 'callAnswer', payload: answer }));
    webrtc.on('iceCandidate', (candidate: any) => ws.send({ type: 'iceCandidate', payload: candidate }));
    webrtc.on('callEnded', () => setCurrentCall(null));

    // Initialize encryption
    encryptionService.generateKeyPair();

    return () => {
      ws.disconnect();
      webrtc.cleanup();
    };
  }, []);

  const handleNewMessage = (message: Message) => {
    addMessage(message);
  };

  const handleMessageStatus = (data: { messageId: string; status: string }) => {
    updateMessageStatus(data.messageId, data.status as Message['status']);
  };

  const handleTyping = (data: { conversationId: string; userId: string; isTyping: boolean }) => {
    setTyping(data.conversationId, data.userId, data.isTyping);
  };

  const handleIncomingCall = (callSession: CallSession) => {
    setIncomingCall(callSession);
    setShowIncomingCall(true);
  };

  const handleCallAnswer = (data: any) => {
    webrtcService?.handleAnswer(data.answer);
  };

  const handleCallEnd = () => {
    setCurrentCall(null);
    setCurrentView('list');
    webrtcService?.endCall();
  };

  const handleConversationSelect = (conversation: Conversation) => {
    setActiveConversation(conversation.id);
    setCurrentView('chat');

    // Load messages for this conversation
    fetchMessages(conversation.id);

    // Mark messages as read
    markMessagesAsRead(conversation.id);
  };

  const handleSendMessage = async (content: string, type: 'text' | 'image' | 'file') => {
    if (!activeConversationId) return;

    try {
      await sendMessage(activeConversationId, content, type);
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  const handleStartCall = async (userId: string, type: 'audio' | 'video') => {
    if (!wsService || !webrtcService) return;

    const callSession: CallSession = {
      id: Date.now().toString(),
      type,
      participants: [
        CURRENT_USER,
        conversations.find(c => c.participants.some(p => p.id === userId))?.participants.find(p => p.id === userId)!
      ],
      status: 'outgoing',
      startTime: new Date(),
    };

    setCurrentCall(callSession);
    setCurrentView('call');

    try {
      await webrtcService.startCall(type);
      wsService.initiateCall(userId, type);
    } catch (error) {
      console.error('Failed to start call:', error);
      setCurrentCall(null);
      setCurrentView('list');
    }
  };

  const handleAcceptCall = async () => {
    if (!incomingCall || !webrtcService || !wsService) return;

    setCurrentCall(incomingCall);
    setShowIncomingCall(false);
    setCurrentView('call');

    try {
      // This would be the offer from the caller
      const offer = {}; // Get from call session
      await webrtcService.answerCall(offer as RTCSessionDescriptionInit);
      wsService.answerCall(incomingCall.id);
    } catch (error) {
      console.error('Failed to answer call:', error);
    }
  };

  const handleDeclineCall = () => {
    if (!incomingCall || !wsService) return;

    wsService.endCall(incomingCall.id);
    setIncomingCall(null);
    setShowIncomingCall(false);
  };

  const handleEndCall = () => {
    if (!currentCall || !wsService) return;

    wsService.endCall(currentCall.id);
    webrtcService?.endCall();
    setCurrentCall(null);
    setCurrentView('list');
  };

  const handleToggleVideo = async () => {
    if (webrtcService) {
      const enabled = await webrtcService.toggleVideo();
      setIsVideoEnabled(enabled);
    }
  };

  const handleToggleAudio = async () => {
    if (webrtcService) {
      const enabled = await webrtcService.toggleAudio();
      setIsAudioEnabled(enabled);
    }
  };

  const handleStartScreenShare = async () => {
    if (webrtcService) {
      await webrtcService.startScreenShare();
      setIsScreenSharing(true);
    }
  };

  const handleStopScreenShare = async () => {
    if (webrtcService) {
      await webrtcService.stopScreenShare();
      setIsScreenSharing(false);
    }
  };

  const handleStartRecording = () => {
    if (webrtcService) {
      webrtcService.startRecording();
      setIsRecording(true);
    }
  };

  const handleStopRecording = () => {
    if (webrtcService) {
      webrtcService.stopRecording();
      setIsRecording(false);
    }
  };

  const handleStartTyping = () => {
    if (selectedConversation && wsService) {
      wsService.startTyping(selectedConversation.id);
    }
  };

  const handleStopTyping = () => {
    if (selectedConversation && wsService) {
      wsService.stopTyping(selectedConversation.id);
    }
  };

  const handleMarkAsRead = (messageId: string) => {
    wsService?.markAsRead(messageId);
  };

  if (currentView === 'call' && currentCall) {
    return (
      <CallScreen
        callSession={currentCall}
        currentUser={CURRENT_USER}
        localStream={localStream || undefined}
        remoteStream={remoteStream || undefined}
        onEndCall={handleEndCall}
        onToggleVideo={handleToggleVideo}
        onToggleAudio={handleToggleAudio}
        onToggleSpeaker={() => setIsSpeakerEnabled(!isSpeakerEnabled)}
        onStartScreenShare={handleStartScreenShare}
        onStopScreenShare={handleStopScreenShare}
        onStartRecording={handleStartRecording}
        onStopRecording={handleStopRecording}
        isVideoEnabled={isVideoEnabled}
        isAudioEnabled={isAudioEnabled}
        isSpeakerEnabled={isSpeakerEnabled}
        isScreenSharing={isScreenSharing}
        isRecording={isRecording}
      />
    );
  }

  const selectedConversation = conversations.find(conv => conv.id === activeConversationId);
  const conversationMessages = activeConversationId ? messages[activeConversationId] || [] : [];

  if (currentView === 'chat' && selectedConversation) {
    return (
      <View style={styles.container}>
        <ChatScreen
          conversation={selectedConversation}
          messages={conversationMessages}
          currentUser={currentUser || CURRENT_USER}
          onSendMessage={handleSendMessage}
          onStartCall={(type) => {
            const otherUser = selectedConversation.participants.find(p => p.id !== CURRENT_USER.id);
            if (otherUser) {
              handleStartCall(otherUser.id, type);
            }
          }}
          onGoBack={() => setCurrentView('list')}
          onStartTyping={handleStartTyping}
          onStopTyping={handleStopTyping}
          onMarkAsRead={handleMarkAsRead}
        />
        
        {incomingCall && (
          <IncomingCallModal
            visible={showIncomingCall}
            callSession={incomingCall}
            caller={incomingCall.participants.find(p => p.id !== CURRENT_USER.id) || incomingCall.participants[0]}
            onAccept={handleAcceptCall}
            onDecline={handleDeclineCall}
            onSendMessage={() => {
              // Handle quick message
              setShowIncomingCall(false);
            }}
          />
        )}
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ChatList
        conversations={conversations}
        currentUser={currentUser || CURRENT_USER}
        onConversationSelect={handleConversationSelect}
        onStartCall={handleStartCall}
      />
      
      {incomingCall && (
        <IncomingCallModal
          visible={showIncomingCall}
          callSession={incomingCall}
          caller={incomingCall.participants.find(p => p.id !== CURRENT_USER.id) || incomingCall.participants[0]}
          onAccept={handleAcceptCall}
          onDecline={handleDeclineCall}
          onSendMessage={() => {
            // Handle quick message
            setShowIncomingCall(false);
          }}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});