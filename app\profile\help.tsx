import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  Platform,
  Linking,
  TextInput,
} from 'react-native';
import { useRouter } from 'expo-router';
import { theme } from '@/constants/theme';
import * as Haptics from 'expo-haptics';
import {
  ArrowLeft,
  HelpCircle,
  MessageCircle,
  Mail,
  Phone,
  ExternalLink,
  ChevronRight,
  ChevronDown,
  ChevronUp,
  Send,
  Book,
  Shield,
  AlertTriangle,
  Heart,
  Users,
  Settings,
} from 'lucide-react-native';

const FAQ_DATA = [
  {
    category: 'Getting Started',
    icon: Heart,
    questions: [
      {
        question: 'How do I create a great profile?',
        answer: 'Use high-quality photos that show your face clearly, write an authentic bio that reflects your personality, and add interests that represent who you are. Profiles with 4+ photos get 3x more matches!'
      },
      {
        question: 'How does matching work?',
        answer: 'When you and another person both like each other\'s profiles, it creates a match! You can then start messaging each other.'
      },
      {
        question: 'Why am I not getting matches?',
        answer: 'Try updating your photos, expanding your age/distance preferences, or being more active on the app. Premium features can also help increase your visibility.'
      },
    ]
  },
  {
    category: 'Safety & Privacy',
    icon: Shield,
    questions: [
      {
        question: 'How do you protect my privacy?',
        answer: 'We use industry-standard encryption, never share your personal information without consent, and give you full control over your profile visibility and data.'
      },
      {
        question: 'How do I report someone?',
        answer: 'Tap the three dots on their profile or in your conversation, then select "Report". We take all reports seriously and investigate promptly.'
      },
      {
        question: 'Can I block someone?',
        answer: 'Yes, you can block users from their profile or conversation. Blocked users won\'t be able to see your profile or contact you.'
      },
    ]
  },
  {
    category: 'Premium Features',
    icon: Users,
    questions: [
      {
        question: 'What do I get with Premium?',
        answer: 'Premium includes unlimited likes, seeing who likes you, Super Likes, advanced filters, profile boosts, and priority support.'
      },
      {
        question: 'How do I cancel my subscription?',
        answer: 'You can cancel anytime in your device\'s subscription settings (App Store or Google Play). Your premium features will remain active until the end of your billing period.'
      },
      {
        question: 'Can I get a refund?',
        answer: 'Refunds are handled through your app store (Apple or Google). Contact their support for refund requests within their policy timeframes.'
      },
    ]
  },
  {
    category: 'Technical Issues',
    icon: Settings,
    questions: [
      {
        question: 'The app is crashing or slow',
        answer: 'Try restarting the app, updating to the latest version, or restarting your device. If issues persist, contact our support team.'
      },
      {
        question: 'I\'m not receiving notifications',
        answer: 'Check your device notification settings and ensure SoulSync has permission to send notifications. Also verify your in-app notification preferences.'
      },
      {
        question: 'Photos won\'t upload',
        answer: 'Ensure you have a stable internet connection and the app has camera/photo permissions. Try using smaller image files or different photos.'
      },
    ]
  },
];

const CONTACT_OPTIONS = [
  {
    id: 'chat',
    title: 'Live Chat',
    subtitle: 'Get instant help from our support team',
    icon: MessageCircle,
    action: () => Alert.alert('Live Chat', 'Live chat feature would open here'),
  },
  {
    id: 'email',
    title: 'Email Support',
    subtitle: 'Send us a detailed message',
    icon: Mail,
    action: () => Linking.openURL('mailto:<EMAIL>'),
  },
  {
    id: 'phone',
    title: 'Phone Support',
    subtitle: 'Call us during business hours',
    icon: Phone,
    action: () => Linking.openURL('tel:******-SOUL-SYNC'),
  },
];

export default function HelpScreen() {
  const router = useRouter();
  const [expandedCategory, setExpandedCategory] = useState<string | null>(null);
  const [expandedQuestion, setExpandedQuestion] = useState<string | null>(null);
  const [contactMessage, setContactMessage] = useState('');
  const [showContactForm, setShowContactForm] = useState(false);

  const handleCategoryToggle = (category: string) => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    setExpandedCategory(expandedCategory === category ? null : category);
    setExpandedQuestion(null); // Close any open questions when switching categories
  };

  const handleQuestionToggle = (questionId: string) => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    setExpandedQuestion(expandedQuestion === questionId ? null : questionId);
  };

  const handleContactSubmit = () => {
    if (!contactMessage.trim()) {
      Alert.alert('Error', 'Please enter your message');
      return;
    }

    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }

    Alert.alert(
      'Message Sent!',
      'Thank you for contacting us. We\'ll get back to you within 24 hours.',
      [{ text: 'OK', onPress: () => {
        setContactMessage('');
        setShowContactForm(false);
      }}]
    );
  };

  const CategoryCard = ({ category }: { category: typeof FAQ_DATA[0] }) => {
    const isExpanded = expandedCategory === category.category;
    
    return (
      <View style={styles.categoryCard}>
        <TouchableOpacity
          style={styles.categoryHeader}
          onPress={() => handleCategoryToggle(category.category)}
        >
          <View style={styles.categoryLeft}>
            <View style={styles.categoryIcon}>
              <category.icon size={20} color={theme.colors.primary} />
            </View>
            <Text style={styles.categoryTitle}>{category.category}</Text>
          </View>
          {isExpanded ? (
            <ChevronUp size={20} color={theme.colors.gray600} />
          ) : (
            <ChevronDown size={20} color={theme.colors.gray600} />
          )}
        </TouchableOpacity>

        {isExpanded && (
          <View style={styles.questionsContainer}>
            {category.questions.map((item, index) => {
              const questionId = `${category.category}-${index}`;
              const isQuestionExpanded = expandedQuestion === questionId;
              
              return (
                <View key={index} style={styles.questionCard}>
                  <TouchableOpacity
                    style={styles.questionHeader}
                    onPress={() => handleQuestionToggle(questionId)}
                  >
                    <Text style={styles.questionText}>{item.question}</Text>
                    {isQuestionExpanded ? (
                      <ChevronUp size={16} color={theme.colors.gray500} />
                    ) : (
                      <ChevronDown size={16} color={theme.colors.gray500} />
                    )}
                  </TouchableOpacity>
                  
                  {isQuestionExpanded && (
                    <View style={styles.answerContainer}>
                      <Text style={styles.answerText}>{item.answer}</Text>
                    </View>
                  )}
                </View>
              );
            })}
          </View>
        )}
      </View>
    );
  };

  const ContactOption = ({ option }: { option: typeof CONTACT_OPTIONS[0] }) => (
    <TouchableOpacity style={styles.contactOption} onPress={option.action}>
      <View style={styles.contactLeft}>
        <View style={styles.contactIcon}>
          <option.icon size={20} color={theme.colors.primary} />
        </View>
        <View style={styles.contactContent}>
          <Text style={styles.contactTitle}>{option.title}</Text>
          <Text style={styles.contactSubtitle}>{option.subtitle}</Text>
        </View>
      </View>
      <ChevronRight size={20} color={theme.colors.gray400} />
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Help & Support</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Hero Section */}
        <View style={styles.heroSection}>
          <View style={styles.heroIcon}>
            <HelpCircle size={32} color={theme.colors.primary} />
          </View>
          <Text style={styles.heroTitle}>How can we help you?</Text>
          <Text style={styles.heroSubtitle}>
            Find answers to common questions or get in touch with our support team
          </Text>
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActionsSection}>
          <Text style={styles.sectionTitle}>Get Help</Text>
          <View style={styles.quickActions}>
            <TouchableOpacity 
              style={styles.quickAction}
              onPress={() => setShowContactForm(!showContactForm)}
            >
              <MessageCircle size={24} color={theme.colors.primary} />
              <Text style={styles.quickActionText}>Contact Us</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.quickAction}
              onPress={() => Alert.alert('Safety Center', 'Safety resources would open here')}
            >
              <Shield size={24} color={theme.colors.success} />
              <Text style={styles.quickActionText}>Safety Center</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.quickAction}
              onPress={() => Alert.alert('Report Issue', 'Issue reporting would open here')}
            >
              <AlertTriangle size={24} color={theme.colors.warning} />
              <Text style={styles.quickActionText}>Report Issue</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Contact Form */}
        {showContactForm && (
          <View style={styles.contactFormSection}>
            <Text style={styles.sectionTitle}>Send us a message</Text>
            <View style={styles.contactForm}>
              <TextInput
                style={styles.messageInput}
                placeholder="Describe your issue or question..."
                placeholderTextColor={theme.colors.gray400}
                multiline
                numberOfLines={4}
                value={contactMessage}
                onChangeText={setContactMessage}
                textAlignVertical="top"
              />
              <TouchableOpacity style={styles.sendButton} onPress={handleContactSubmit}>
                <Send size={16} color="white" />
                <Text style={styles.sendButtonText}>Send Message</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {/* FAQ Section */}
        <View style={styles.faqSection}>
          <Text style={styles.sectionTitle}>Frequently Asked Questions</Text>
          <View style={styles.categoriesContainer}>
            {FAQ_DATA.map((category, index) => (
              <CategoryCard key={index} category={category} />
            ))}
          </View>
        </View>

        {/* Contact Options */}
        <View style={styles.contactSection}>
          <Text style={styles.sectionTitle}>Contact Support</Text>
          <View style={styles.contactOptionsContainer}>
            {CONTACT_OPTIONS.map((option) => (
              <ContactOption key={option.id} option={option} />
            ))}
          </View>
        </View>

        {/* Additional Resources */}
        <View style={styles.resourcesSection}>
          <Text style={styles.sectionTitle}>Additional Resources</Text>
          <View style={styles.resourcesContainer}>
            <TouchableOpacity style={styles.resourceItem}>
              <Book size={20} color={theme.colors.primary} />
              <Text style={styles.resourceText}>Community Guidelines</Text>
              <ExternalLink size={16} color={theme.colors.gray400} />
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.resourceItem}>
              <Shield size={20} color={theme.colors.primary} />
              <Text style={styles.resourceText}>Privacy Policy</Text>
              <ExternalLink size={16} color={theme.colors.gray400} />
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.resourceItem}>
              <Book size={20} color={theme.colors.primary} />
              <Text style={styles.resourceText}>Terms of Service</Text>
              <ExternalLink size={16} color={theme.colors.gray400} />
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray200,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
  },
  placeholder: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  heroSection: {
    alignItems: 'center',
    paddingVertical: 30,
    paddingHorizontal: 20,
    backgroundColor: 'white',
  },
  heroIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: theme.colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  heroTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: theme.colors.text,
    marginBottom: 8,
    textAlign: 'center',
  },
  heroSubtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
    textAlign: 'center',
    lineHeight: 24,
  },
  quickActionsSection: {
    backgroundColor: 'white',
    marginTop: 10,
    paddingVertical: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  quickActions: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    gap: 16,
  },
  quickAction: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: theme.colors.gray50,
    paddingVertical: 20,
    paddingHorizontal: 12,
    borderRadius: 12,
  },
  quickActionText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: theme.colors.text,
    marginTop: 8,
    textAlign: 'center',
  },
  contactFormSection: {
    backgroundColor: 'white',
    marginTop: 10,
    paddingVertical: 20,
  },
  contactForm: {
    paddingHorizontal: 20,
  },
  messageInput: {
    borderWidth: 1,
    borderColor: theme.colors.gray300,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text,
    backgroundColor: theme.colors.gray50,
    height: 100,
    marginBottom: 16,
  },
  sendButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.primary,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  sendButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  faqSection: {
    backgroundColor: 'white',
    marginTop: 10,
    paddingVertical: 20,
  },
  categoriesContainer: {
    paddingHorizontal: 20,
    gap: 12,
  },
  categoryCard: {
    backgroundColor: theme.colors.gray50,
    borderRadius: 12,
    overflow: 'hidden',
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  categoryLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: theme.colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  categoryTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
  },
  questionsContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
    gap: 8,
  },
  questionCard: {
    backgroundColor: 'white',
    borderRadius: 8,
    overflow: 'hidden',
  },
  questionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
  },
  questionText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: theme.colors.text,
    flex: 1,
    marginRight: 8,
  },
  answerContainer: {
    paddingHorizontal: 12,
    paddingBottom: 12,
  },
  answerText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray700,
    lineHeight: 20,
  },
  contactSection: {
    backgroundColor: 'white',
    marginTop: 10,
    paddingVertical: 20,
  },
  contactOptionsContainer: {
    paddingHorizontal: 20,
    gap: 12,
  },
  contactOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: theme.colors.gray50,
    padding: 16,
    borderRadius: 12,
  },
  contactLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  contactIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  contactContent: {
    flex: 1,
  },
  contactTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 2,
  },
  contactSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
  },
  resourcesSection: {
    backgroundColor: 'white',
    marginTop: 10,
    paddingVertical: 20,
    marginBottom: 20,
  },
  resourcesContainer: {
    paddingHorizontal: 20,
    gap: 12,
  },
  resourceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.gray50,
    padding: 16,
    borderRadius: 12,
    gap: 12,
  },
  resourceText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text,
    flex: 1,
  },
});
