import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  withDelay,
} from 'react-native-reanimated';

interface TypingIndicatorProps {
  isVisible: boolean;
  userName: string;
}

export default function TypingIndicator({ isVisible, userName }: TypingIndicatorProps) {
  const dot1Anim = useSharedValue(0);
  const dot2Anim = useSharedValue(0);
  const dot3Anim = useSharedValue(0);

  useEffect(() => {
    if (isVisible) {
      // Animate dots with staggered delays
      dot1Anim.value = withRepeat(
        withTiming(1, { duration: 400 }),
        -1,
        true
      );

      dot2Anim.value = withDelay(
        200,
        withRepeat(
          withTiming(1, { duration: 400 }),
          -1,
          true
        )
      );

      dot3Anim.value = withDelay(
        400,
        withRepeat(
          withTiming(1, { duration: 400 }),
          -1,
          true
        )
      );
    } else {
      dot1Anim.value = 0;
      dot2Anim.value = 0;
      dot3Anim.value = 0;
    }
  }, [isVisible]);

  const dot1Style = useAnimatedStyle(() => ({
    opacity: dot1Anim.value,
    transform: [{ translateY: dot1Anim.value * -4 }],
  }));

  const dot2Style = useAnimatedStyle(() => ({
    opacity: dot2Anim.value,
    transform: [{ translateY: dot2Anim.value * -4 }],
  }));

  const dot3Style = useAnimatedStyle(() => ({
    opacity: dot3Anim.value,
    transform: [{ translateY: dot3Anim.value * -4 }],
  }));

  if (!isVisible) return null;

  return (
    <View style={styles.container}>
      <View style={styles.bubble}>
        <Text style={styles.text}>{userName} is typing</Text>
        <View style={styles.dotsContainer}>
          <Animated.View style={[styles.dot, dot1Style]} />
          <Animated.View style={[styles.dot, dot2Style]} />
          <Animated.View style={[styles.dot, dot3Style]} />
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  bubble: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    borderRadius: 18,
    paddingHorizontal: 16,
    paddingVertical: 12,
    alignSelf: 'flex-start',
    maxWidth: '75%',
  },
  text: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    marginRight: 8,
  },
  dotsContainer: {
    flexDirection: 'row',
    gap: 3,
  },
  dot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#8B5CF6',
  },
});