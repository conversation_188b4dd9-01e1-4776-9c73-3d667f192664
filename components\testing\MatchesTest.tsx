import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert, ScrollView } from 'react-native';
import { useLikesStore } from '@/stores/likesStore';
import { useMessagesStore } from '@/stores/messagesStore';
import { matchesMessagesIntegration } from '@/services/matchesMessagesIntegration';
import { theme } from '@/constants/theme';

export default function MatchesTest() {
  const [testResults, setTestResults] = useState<string[]>([]);
  
  const {
    matches,
    fetchMatches,
    isLoading,
    error,
  } = useLikesStore();

  const {
    conversations,
    createConversation,
    setCurrentUser,
  } = useMessagesStore();

  useEffect(() => {
    // Initialize test user
    setCurrentUser({
      id: 'current-user',
      name: 'Test User',
      avatar: 'https://via.placeholder.com/50',
      isOnline: true,
    });
  }, []);

  const runMatchesTest = async () => {
    const results: string[] = [];
    
    try {
      results.push('🔄 Testing matches functionality...');

      // Test 1: Fetch matches
      await fetchMatches(true);
      if (matches.length > 0) {
        results.push(`✅ Matches fetched successfully (${matches.length} matches)`);
      } else {
        results.push('❌ No matches found');
      }

      // Test 2: Check match data structure
      if (matches.length > 0) {
        const firstMatch = matches[0];
        if (firstMatch.id && firstMatch.users && firstMatch.timestamp) {
          results.push('✅ Match data structure is correct');
        } else {
          results.push('❌ Match data structure is incomplete');
        }
      }

      // Test 3: Test conversation creation for matches
      if (matches.length > 0) {
        const testMatch = matches[0];
        const otherUserId = testMatch.users.find(id => id !== 'current-user');
        
        if (otherUserId) {
          try {
            await createConversation([
              {
                id: 'current-user',
                name: 'Test User',
                avatar: 'https://via.placeholder.com/50',
                isOnline: true,
              },
              {
                id: otherUserId,
                name: 'Match User',
                avatar: 'https://via.placeholder.com/50',
                isOnline: true,
              }
            ]);
            results.push('✅ Conversation created for match');
          } catch (error) {
            results.push(`❌ Failed to create conversation: ${error}`);
          }
        }
      }

      // Test 4: Test integration service
      try {
        await matchesMessagesIntegration.initializeMatchConversations();
        results.push('✅ Integration service initialized successfully');

        const matchesWithData = matchesMessagesIntegration.getMatchesWithConversationData();
        results.push(`✅ Matches with conversation data: ${matchesWithData.length}`);

        // Test sync for first match
        if (matches.length > 0) {
          const firstMatch = matches[0];
          const syncData = matchesMessagesIntegration.syncMatchWithConversation(firstMatch);
          results.push(`✅ Sync data for first match: ${syncData.hasConversation ? 'Has conversation' : 'No conversation'}`);
          results.push(`✅ Last message: "${syncData.lastMessage}"`);
          results.push(`✅ Unread count: ${syncData.unreadCount}`);
        }
      } catch (error) {
        results.push(`❌ Integration service failed: ${error}`);
      }

      // Test 5: Check conversation integration
      const matchConversations = conversations.filter(conv =>
        conv.participants.some(p =>
          matches.some(match =>
            match.users.includes(p.id) && p.id !== 'current-user'
          )
        )
      );

      if (matchConversations.length > 0) {
        results.push(`✅ Match conversations integrated (${matchConversations.length} conversations)`);
      } else {
        results.push('⚠️ No match conversations found');
      }

      // Test 5: Check match status and activity
      const activeMatches = matches.filter(match => match.status === 'active');
      results.push(`✅ Active matches: ${activeMatches.length}/${matches.length}`);

      const recentMatches = matches.filter(match => {
        const daysSinceMatch = (Date.now() - match.timestamp.getTime()) / (1000 * 60 * 60 * 24);
        return daysSinceMatch <= 7; // Matches from last week
      });
      results.push(`✅ Recent matches (last 7 days): ${recentMatches.length}`);

    } catch (error) {
      results.push(`❌ Test failed: ${error}`);
    }

    setTestResults(results);
  };

  const runNavigationTest = () => {
    const results: string[] = [];
    
    results.push('🔄 Testing navigation functionality...');
    
    if (matches.length > 0) {
      const testMatch = matches[0];
      const otherUserId = testMatch.users.find(id => id !== 'current-user');
      
      if (otherUserId) {
        results.push(`✅ Navigation target found: ${otherUserId}`);
        results.push('✅ Match press handler would navigate to chat');
        results.push('✅ Match modal integration ready');
      } else {
        results.push('❌ No valid navigation target found');
      }
    } else {
      results.push('❌ No matches available for navigation test');
    }

    setTestResults(prev => [...prev, ...results]);
  };

  const runDataIntegrityTest = () => {
    const results: string[] = [];
    
    results.push('🔄 Testing data integrity...');
    
    // Check for duplicate matches
    const matchIds = matches.map(match => match.id);
    const uniqueIds = new Set(matchIds);
    if (matchIds.length === uniqueIds.size) {
      results.push('✅ No duplicate match IDs found');
    } else {
      results.push('❌ Duplicate match IDs detected');
    }

    // Check timestamp validity
    const validTimestamps = matches.every(match => 
      match.timestamp instanceof Date && !isNaN(match.timestamp.getTime())
    );
    if (validTimestamps) {
      results.push('✅ All match timestamps are valid');
    } else {
      results.push('❌ Invalid timestamps found');
    }

    // Check user array structure
    const validUserArrays = matches.every(match => 
      Array.isArray(match.users) && 
      match.users.length === 2 && 
      match.users.includes('current-user')
    );
    if (validUserArrays) {
      results.push('✅ All match user arrays are valid');
    } else {
      results.push('❌ Invalid user arrays found');
    }

    setTestResults(prev => [...prev, ...results]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Matches Functionality Tests</Text>
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.button} onPress={runMatchesTest}>
          <Text style={styles.buttonText}>Test Matches Core</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.button} onPress={runNavigationTest}>
          <Text style={styles.buttonText}>Test Navigation</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.button} onPress={runDataIntegrityTest}>
          <Text style={styles.buttonText}>Test Data Integrity</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={[styles.button, styles.clearButton]} onPress={clearResults}>
          <Text style={styles.buttonText}>Clear Results</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.statsContainer}>
        <Text style={styles.statsTitle}>Current Stats:</Text>
        <Text style={styles.statsText}>Matches: {matches.length}</Text>
        <Text style={styles.statsText}>Conversations: {conversations.length}</Text>
        <Text style={styles.statsText}>Loading: {isLoading ? 'Yes' : 'No'}</Text>
        <Text style={styles.statsText}>Error: {error || 'None'}</Text>
      </View>

      <ScrollView style={styles.resultsContainer}>
        <Text style={styles.resultsTitle}>Test Results:</Text>
        {testResults.map((result, index) => (
          <Text key={index} style={styles.resultText}>
            {result}
          </Text>
        ))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: theme.colors.background,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 20,
    textAlign: 'center',
  },
  buttonContainer: {
    marginBottom: 20,
  },
  button: {
    backgroundColor: theme.colors.primary,
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    alignItems: 'center',
  },
  clearButton: {
    backgroundColor: theme.colors.gray500,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  statsContainer: {
    backgroundColor: theme.colors.gray100,
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
  },
  statsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 10,
  },
  statsText: {
    fontSize: 14,
    color: theme.colors.text,
    marginBottom: 5,
  },
  resultsContainer: {
    flex: 1,
    backgroundColor: theme.colors.gray100,
    padding: 15,
    borderRadius: 10,
  },
  resultsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 10,
  },
  resultText: {
    fontSize: 14,
    color: theme.colors.text,
    marginBottom: 5,
    fontFamily: 'monospace',
  },
});
