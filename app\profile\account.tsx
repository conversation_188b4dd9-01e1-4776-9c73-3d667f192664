import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Switch,
  TextInput,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import * as Haptics from 'expo-haptics';
import {
  ArrowLeft,
  Mail,
  Phone,
  Lock,
  Shield,
  Smartphone,
  AlertTriangle,
  CheckCircle,
  Clock,
  Globe,
  Key,
  Settings,
} from 'lucide-react-native';

import { theme } from '../../constants/theme';
import { useProfileStore } from '../../stores/profileStore';
import LoadingSkeleton from '../../components/LoadingSkeleton';

export default function AccountSettingsScreen() {
  const router = useRouter();
  const { profile, settings, updateSettings, isLoading } = useProfileStore();
  
  const [localSettings, setLocalSettings] = useState({
    twoFactorAuth: false,
    loginAlerts: true,
    emailVerified: false,
    phoneVerified: false,
    sessionTimeout: 30,
    deviceManagement: true,
    backupEmail: '',
    recoveryPhone: '',
  });

  const [showEmailInput, setShowEmailInput] = useState(false);
  const [showPhoneInput, setShowPhoneInput] = useState(false);
  const [newEmail, setNewEmail] = useState('');
  const [newPhone, setNewPhone] = useState('');

  useEffect(() => {
    if (settings?.account) {
      setLocalSettings({
        twoFactorAuth: settings.account.twoFactorAuth,
        loginAlerts: settings.account.loginAlerts,
        emailVerified: settings.account.emailVerified,
        phoneVerified: settings.account.phoneVerified,
        sessionTimeout: settings.account.sessionTimeout,
        deviceManagement: settings.account.deviceManagement,
        backupEmail: settings.account.backupEmail || '',
        recoveryPhone: settings.account.recoveryPhone || '',
      });
    }
  }, [settings]);

  const handleBack = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    router.back();
  };

  const handleToggle = async (key: string, value: boolean) => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    setLocalSettings(prev => ({ ...prev, [key]: value }));

    try {
      const updatedSettings = {
        ...settings,
        account: {
          ...settings?.account,
          [key]: value,
        },
      };
      await updateSettings(updatedSettings);
    } catch (error) {
      console.error('Failed to update setting:', error);
      // Revert on error
      setLocalSettings(prev => ({ ...prev, [key]: !value }));
      Alert.alert('Error', 'Failed to update setting. Please try again.');
    }
  };

  const handleChangePassword = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    Alert.alert(
      'Change Password',
      'You will be redirected to change your password securely.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Continue', onPress: () => {
          // In production, this would navigate to password change flow
          Alert.alert('Password Change', 'Password change flow would open here');
        }},
      ]
    );
  };

  const handleSetup2FA = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    if (localSettings.twoFactorAuth) {
      Alert.alert(
        'Disable Two-Factor Authentication',
        'Are you sure you want to disable 2FA? This will make your account less secure.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Disable', style: 'destructive', onPress: () => handleToggle('twoFactorAuth', false) },
        ]
      );
    } else {
      Alert.alert(
        'Enable Two-Factor Authentication',
        'We recommend enabling 2FA for better account security.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Enable', onPress: () => handleToggle('twoFactorAuth', true) },
        ]
      );
    }
  };

  const handleVerifyEmail = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    Alert.alert(
      'Verify Email',
      'A verification email will be sent to your email address.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Send Email', onPress: () => {
          // Simulate email verification
          Alert.alert('Email Sent', 'Please check your email and click the verification link.');
        }},
      ]
    );
  };

  const handleVerifyPhone = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    Alert.alert(
      'Verify Phone',
      'A verification code will be sent to your phone number.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Send Code', onPress: () => {
          // Simulate phone verification
          Alert.alert('Code Sent', 'Please enter the verification code sent to your phone.');
        }},
      ]
    );
  };

  const handleManageDevices = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    router.push('/profile/devices');
  };

  const handleLoginHistory = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    router.push('/profile/login-history');
  };

  if (isLoading) {
    return (
      <LinearGradient colors={['#8B5CF6', '#EC4899']} style={styles.container}>
        <SafeAreaView style={styles.safeArea}>
          <LoadingSkeleton />
        </SafeAreaView>
      </LinearGradient>
    );
  }

  const SettingItem = ({ 
    icon: Icon, 
    title, 
    subtitle, 
    value, 
    onToggle, 
    type = 'switch',
    onPress,
    status,
  }: {
    icon: any;
    title: string;
    subtitle?: string;
    value?: boolean;
    onToggle?: (value: boolean) => void;
    type?: 'switch' | 'button' | 'status';
    onPress?: () => void;
    status?: 'verified' | 'unverified' | 'pending';
  }) => (
    <TouchableOpacity 
      style={styles.settingItem} 
      onPress={type === 'button' ? onPress : undefined}
      disabled={type === 'switch'}
    >
      <View style={styles.settingLeft}>
        <View style={styles.settingIcon}>
          <Icon size={20} color={theme.colors.primary} />
        </View>
        <View style={styles.settingContent}>
          <Text style={styles.settingTitle}>{title}</Text>
          {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
        </View>
      </View>
      
      {type === 'switch' && onToggle && (
        <Switch
          value={value}
          onValueChange={onToggle}
          trackColor={{ false: theme.colors.gray300, true: theme.colors.primary + '40' }}
          thumbColor={value ? theme.colors.primary : theme.colors.gray400}
        />
      )}
      
      {type === 'status' && status && (
        <View style={styles.statusContainer}>
          {status === 'verified' && <CheckCircle size={20} color={theme.colors.success} />}
          {status === 'unverified' && <AlertTriangle size={20} color={theme.colors.warning} />}
          {status === 'pending' && <Clock size={20} color={theme.colors.gray400} />}
        </View>
      )}
    </TouchableOpacity>
  );

  return (
    <LinearGradient colors={['#8B5CF6', '#EC4899']} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <ArrowLeft size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Account Settings</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Account Information */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Account Information</Text>
            
            <View style={styles.infoItem}>
              <Mail size={20} color={theme.colors.text} />
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>Email</Text>
                <Text style={styles.infoValue}>{profile?.email}</Text>
              </View>
              <TouchableOpacity onPress={handleVerifyEmail}>
                {localSettings.emailVerified ? (
                  <CheckCircle size={20} color={theme.colors.success} />
                ) : (
                  <AlertTriangle size={20} color={theme.colors.warning} />
                )}
              </TouchableOpacity>
            </View>

            <TouchableOpacity style={styles.actionButton} onPress={handleChangePassword}>
              <Lock size={20} color={theme.colors.primary} />
              <Text style={styles.actionButtonText}>Change Password</Text>
            </TouchableOpacity>
          </View>

          {/* Security Settings */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Security</Text>
            
            <SettingItem
              icon={Shield}
              title="Two-Factor Authentication"
              subtitle={localSettings.twoFactorAuth ? "Enabled" : "Disabled"}
              type="button"
              onPress={handleSetup2FA}
            />
            
            <SettingItem
              icon={Bell}
              title="Login Alerts"
              subtitle="Get notified of new logins"
              value={localSettings.loginAlerts}
              onToggle={(value) => handleToggle('loginAlerts', value)}
            />
            
            <SettingItem
              icon={Smartphone}
              title="Device Management"
              subtitle="Manage trusted devices"
              type="button"
              onPress={handleManageDevices}
            />
            
            <SettingItem
              icon={Clock}
              title="Login History"
              subtitle="View recent login activity"
              type="button"
              onPress={handleLoginHistory}
            />
          </View>
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  placeholder: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    marginVertical: 10,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 12,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray100,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: theme.colors.text,
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
  },
  statusContainer: {
    padding: 4,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray100,
  },
  infoContent: {
    flex: 1,
    marginLeft: 12,
  },
  infoLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: theme.colors.gray600,
    marginBottom: 2,
  },
  infoValue: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  actionButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: theme.colors.primary,
    marginLeft: 12,
  },
});
