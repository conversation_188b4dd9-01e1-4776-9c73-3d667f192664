import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useRouter } from 'expo-router';
import { ArrowLeft, TestTube, Crown, Sparkles } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import PremiumTestingSuite from '@/components/premium/PremiumTestingSuite';
import PremiumFeatureGate from '@/components/premium/PremiumFeatureGate';
import { usePremiumStore } from '@/stores/premiumStore';
import { useLikesStore } from '@/stores/likesStore';
import { useMessagesStore } from '@/stores/messagesStore';
import { theme } from '@/constants/theme';
import * as Haptics from 'expo-haptics';
import { Platform } from 'react-native';

export default function PremiumTestingScreen() {
  const router = useRouter();
  const premiumStore = usePremiumStore();
  const likesStore = useLikesStore();
  const messagesStore = useMessagesStore();

  const handleGoBack = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    router.back();
  };

  const testFeatureGate = () => {
    Alert.alert(
      'Feature Gate Test',
      'This demonstrates how premium features are gated throughout the app.',
      [{ text: 'OK' }]
    );
  };

  const testLikeLimit = async () => {
    const canSend = likesStore.canSendLike();
    Alert.alert(
      'Like Limit Test',
      `Can send like: ${canSend.canSend}\n${canSend.reason || 'No restrictions'}`,
      [{ text: 'OK' }]
    );
  };

  const testSuperLikeLimit = async () => {
    const canSend = likesStore.canSendSuperLike();
    Alert.alert(
      'Super Like Test',
      `Can send super like: ${canSend.canSend}\n${canSend.reason || 'No restrictions'}`,
      [{ text: 'OK' }]
    );
  };

  const testPriorityMessaging = () => {
    const canSend = messagesStore.canSendPriorityMessage();
    Alert.alert(
      'Priority Messaging Test',
      `Can send priority message: ${canSend}`,
      [{ text: 'OK' }]
    );
  };

  const testReadReceipts = () => {
    const canSee = messagesStore.canSeeReadReceipts();
    Alert.alert(
      'Read Receipts Test',
      `Can see read receipts: ${canSee}`,
      [{ text: 'OK' }]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleGoBack}>
          <ArrowLeft size={24} color="white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Premium Testing</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Feature Gate Demo */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Feature Gate Demo</Text>
          <Text style={styles.sectionDescription}>
            These components show how premium features are gated throughout the app:
          </Text>
          
          {/* See Who Likes You Gate */}
          <PremiumFeatureGate
            feature="see_who_likes_you"
            onUpgradePress={() => router.push('/premium')}
          >
            <View style={styles.featureContent}>
              <Crown size={24} color="#FFD700" />
              <Text style={styles.featureText}>
                ✅ You can see who likes you! (Premium Active)
              </Text>
            </View>
          </PremiumFeatureGate>

          {/* Unlimited Likes Gate */}
          <PremiumFeatureGate
            feature="unlimited_likes"
            onUpgradePress={() => router.push('/premium')}
          >
            <View style={styles.featureContent}>
              <Crown size={24} color="#FFD700" />
              <Text style={styles.featureText}>
                ✅ You have unlimited likes! (Premium Active)
              </Text>
            </View>
          </PremiumFeatureGate>

          {/* Priority Messages Gate */}
          <PremiumFeatureGate
            feature="priority_messages"
            onUpgradePress={() => router.push('/premium')}
          >
            <View style={styles.featureContent}>
              <Crown size={24} color="#FFD700" />
              <Text style={styles.featureText}>
                ✅ Your messages have priority! (Premium Active)
              </Text>
            </View>
          </PremiumFeatureGate>
        </View>

        {/* Manual Tests */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Manual Feature Tests</Text>
          <Text style={styles.sectionDescription}>
            Test individual premium features manually:
          </Text>
          
          <View style={styles.testButtons}>
            <TouchableOpacity style={styles.testButton} onPress={testLikeLimit}>
              <Text style={styles.testButtonText}>Test Like Limits</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.testButton} onPress={testSuperLikeLimit}>
              <Text style={styles.testButtonText}>Test Super Like Limits</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.testButton} onPress={testPriorityMessaging}>
              <Text style={styles.testButtonText}>Test Priority Messaging</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.testButton} onPress={testReadReceipts}>
              <Text style={styles.testButtonText}>Test Read Receipts</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Current Status */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Current Premium Status</Text>
          <View style={styles.statusCard}>
            <View style={styles.statusRow}>
              <Text style={styles.statusLabel}>Premium Active:</Text>
              <Text style={[
                styles.statusValue,
                premiumStore.isPremium ? styles.successText : styles.errorText
              ]}>
                {premiumStore.isPremium ? 'Yes' : 'No'}
              </Text>
            </View>
            
            <View style={styles.statusRow}>
              <Text style={styles.statusLabel}>Selected Plan:</Text>
              <Text style={styles.statusValue}>
                {premiumStore.selectedPlan || 'None'}
              </Text>
            </View>
            
            <View style={styles.statusRow}>
              <Text style={styles.statusLabel}>Daily Likes Used:</Text>
              <Text style={styles.statusValue}>
                {likesStore.dailyLikesUsed}
              </Text>
            </View>
            
            <View style={styles.statusRow}>
              <Text style={styles.statusLabel}>Super Likes Used:</Text>
              <Text style={styles.statusValue}>
                {likesStore.dailySuperLikesUsed}
              </Text>
            </View>
          </View>
        </View>

        {/* Automated Testing Suite */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Automated Testing Suite</Text>
          <PremiumTestingSuite />
        </View>

        {/* Implementation Summary */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Implementation Summary</Text>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryText}>
              ✅ Premium Types & Store Implementation{'\n'}
              ✅ Animated UI Components with Skeleton Loading{'\n'}
              ✅ Payment Processing Modal with Haptic Feedback{'\n'}
              ✅ Likes Store Integration with Premium Limits{'\n'}
              ✅ Messages Store with Priority Messaging{'\n'}
              ✅ Profile Store with Premium Status Indicators{'\n'}
              ✅ Feature Access Control Throughout App{'\n'}
              ✅ Comprehensive Testing Suite{'\n'}
              ✅ Real-time Premium Status Updates{'\n'}
              ✅ Local Caching & Data Persistence
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    backgroundColor: '#8B5CF6',
  },
  backButton: {
    padding: theme.spacing.sm,
  },
  headerTitle: {
    fontSize: theme.fontSize.lg,
    fontWeight: theme.fontWeight.semibold,
    color: 'white',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: theme.spacing.lg,
  },
  section: {
    marginBottom: theme.spacing.xl,
  },
  sectionTitle: {
    fontSize: theme.fontSize.lg,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  sectionDescription: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.gray600,
    marginBottom: theme.spacing.md,
    lineHeight: 20,
  },
  featureContent: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.success + '10',
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    gap: theme.spacing.sm,
    marginVertical: theme.spacing.xs,
  },
  featureText: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.success,
    fontWeight: theme.fontWeight.medium,
  },
  testButtons: {
    gap: theme.spacing.sm,
  },
  testButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.lg,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.lg,
    alignItems: 'center',
  },
  testButtonText: {
    fontSize: theme.fontSize.base,
    fontWeight: theme.fontWeight.medium,
    color: 'white',
  },
  statusCard: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.lg,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: theme.spacing.sm,
  },
  statusLabel: {
    fontSize: theme.fontSize.base,
    color: theme.colors.gray600,
  },
  statusValue: {
    fontSize: theme.fontSize.base,
    fontWeight: theme.fontWeight.medium,
    color: theme.colors.text,
  },
  successText: {
    color: theme.colors.success,
  },
  errorText: {
    color: theme.colors.error,
  },
  summaryCard: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.lg,
  },
  summaryText: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.gray700,
    lineHeight: 22,
  },
});
