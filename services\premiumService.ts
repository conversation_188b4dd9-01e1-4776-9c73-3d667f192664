import { 
  PremiumSubscription, 
  SubscriptionPlan, 
  PaymentTransaction, 
  PremiumFeatureType,
  FeatureAccess,
  DEFAULT_SUBSCRIPTION_PLANS,
  FREE_USER_LIMITS
} from '@/types/premium';

class PremiumService {
  private baseUrl = process.env.EXPO_PUBLIC_API_URL || 'https://api.soulsync.app';

  /**
   * Fetch available subscription plans
   */
  async getSubscriptionPlans(): Promise<SubscriptionPlan[]> {
    try {
      // In production, this would fetch from API
      // For demo, return default plans with simulated delay
      await new Promise(resolve => setTimeout(resolve, 500));
      return DEFAULT_SUBSCRIPTION_PLANS;
    } catch (error) {
      console.error('Failed to fetch subscription plans:', error);
      throw new Error('Failed to load subscription plans');
    }
  }

  /**
   * Get user's current subscription
   */
  async getCurrentSubscription(userId: string): Promise<PremiumSubscription | null> {
    try {
      // In production, this would fetch from API
      const response = await fetch(`${this.baseUrl}/subscriptions/${userId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          // Add authentication headers
        },
      });

      if (!response.ok) {
        if (response.status === 404) {
          return null; // No subscription found
        }
        throw new Error('Failed to fetch subscription');
      }

      const subscription = await response.json();
      return this.normalizeSubscription(subscription);
    } catch (error) {
      console.error('Failed to fetch subscription:', error);
      // For demo, return null (no subscription)
      return null;
    }
  }

  /**
   * Create a new subscription
   */
  async createSubscription(
    userId: string, 
    planId: string, 
    paymentMethodId: string
  ): Promise<PremiumSubscription> {
    try {
      // In production, this would create subscription via payment processor
      const response = await fetch(`${this.baseUrl}/subscriptions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // Add authentication headers
        },
        body: JSON.stringify({
          userId,
          planId,
          paymentMethodId,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to create subscription');
      }

      const subscription = await response.json();
      return this.normalizeSubscription(subscription);
    } catch (error) {
      console.error('Failed to create subscription:', error);
      throw error;
    }
  }

  /**
   * Cancel subscription
   */
  async cancelSubscription(subscriptionId: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/subscriptions/${subscriptionId}/cancel`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // Add authentication headers
        },
      });

      if (!response.ok) {
        throw new Error('Failed to cancel subscription');
      }

      return true;
    } catch (error) {
      console.error('Failed to cancel subscription:', error);
      throw error;
    }
  }

  /**
   * Restore purchases (for mobile app stores)
   */
  async restorePurchases(userId: string): Promise<PremiumSubscription[]> {
    try {
      // In production, this would restore purchases from app store
      const response = await fetch(`${this.baseUrl}/subscriptions/${userId}/restore`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // Add authentication headers
        },
      });

      if (!response.ok) {
        throw new Error('Failed to restore purchases');
      }

      const subscriptions = await response.json();
      return subscriptions.map(this.normalizeSubscription);
    } catch (error) {
      console.error('Failed to restore purchases:', error);
      throw error;
    }
  }

  /**
   * Check if user has access to a premium feature
   */
  checkFeatureAccess(
    subscription: PremiumSubscription | null,
    feature: PremiumFeatureType,
    usage?: any
  ): FeatureAccess {
    // Check if user has active subscription
    if (subscription && subscription.status === 'active') {
      const plan = DEFAULT_SUBSCRIPTION_PLANS.find(p => p.id === subscription.planId);
      if (plan && plan.features.includes(feature)) {
        return { canAccess: true };
      }
    }

    // Check free user limits
    return this.checkFreeUserLimits(feature, usage);
  }

  /**
   * Check free user limits for features
   */
  private checkFreeUserLimits(feature: PremiumFeatureType, usage?: any): FeatureAccess {
    const today = new Date().toDateString();
    
    switch (feature) {
      case 'unlimited_likes':
        const dailyLikes = usage?.likesUsed || 0;
        if (dailyLikes >= FREE_USER_LIMITS.daily_likes) {
          return {
            canAccess: false,
            reason: `You've reached your daily limit of ${FREE_USER_LIMITS.daily_likes} likes`,
            upgradeRequired: true,
            featureLimit: {
              current: dailyLikes,
              limit: FREE_USER_LIMITS.daily_likes,
              resetDate: new Date(new Date().setHours(24, 0, 0, 0)),
            },
          };
        }
        return { canAccess: true };

      case 'super_likes':
        const dailySuperLikes = usage?.superLikesUsed || 0;
        if (dailySuperLikes >= FREE_USER_LIMITS.super_likes) {
          return {
            canAccess: false,
            reason: `You've used your ${FREE_USER_LIMITS.super_likes} daily Super Like`,
            upgradeRequired: true,
            featureLimit: {
              current: dailySuperLikes,
              limit: FREE_USER_LIMITS.super_likes,
              resetDate: new Date(new Date().setHours(24, 0, 0, 0)),
            },
          };
        }
        return { canAccess: true };

      case 'see_who_likes_you':
      case 'priority_messages':
      case 'advanced_filters':
      case 'profile_boost':
      case 'incognito_mode':
      case 'read_receipts':
      case 'rewind_swipes':
      case 'passport_mode':
      case 'premium_support':
      case 'ad_free_experience':
        return {
          canAccess: false,
          reason: 'This is a premium feature',
          upgradeRequired: true,
        };

      default:
        return {
          canAccess: false,
          reason: 'Unknown feature',
          upgradeRequired: false,
        };
    }
  }

  /**
   * Get payment transactions for a user
   */
  async getTransactions(userId: string): Promise<PaymentTransaction[]> {
    try {
      const response = await fetch(`${this.baseUrl}/transactions/${userId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          // Add authentication headers
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch transactions');
      }

      const transactions = await response.json();
      return transactions.map(this.normalizeTransaction);
    } catch (error) {
      console.error('Failed to fetch transactions:', error);
      return [];
    }
  }

  /**
   * Validate subscription status
   */
  validateSubscription(subscription: PremiumSubscription): boolean {
    if (!subscription) return false;
    
    const now = new Date();
    const endDate = new Date(subscription.endDate);
    
    return subscription.status === 'active' && now <= endDate;
  }

  /**
   * Calculate days remaining in subscription
   */
  getDaysRemaining(subscription: PremiumSubscription): number {
    if (!subscription || subscription.status !== 'active') return 0;
    
    const now = new Date();
    const endDate = new Date(subscription.endDate);
    const diffTime = endDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return Math.max(0, diffDays);
  }

  /**
   * Get subscription renewal date
   */
  getRenewalDate(subscription: PremiumSubscription): Date | null {
    if (!subscription || !subscription.autoRenew) return null;
    return new Date(subscription.endDate);
  }

  /**
   * Normalize subscription data from API
   */
  private normalizeSubscription(subscription: any): PremiumSubscription {
    return {
      ...subscription,
      startDate: new Date(subscription.startDate),
      endDate: new Date(subscription.endDate),
      createdAt: new Date(subscription.createdAt),
      updatedAt: new Date(subscription.updatedAt),
      trialEndDate: subscription.trialEndDate ? new Date(subscription.trialEndDate) : undefined,
      cancelledAt: subscription.cancelledAt ? new Date(subscription.cancelledAt) : undefined,
      pausedAt: subscription.pausedAt ? new Date(subscription.pausedAt) : undefined,
    };
  }

  /**
   * Normalize transaction data from API
   */
  private normalizeTransaction(transaction: any): PaymentTransaction {
    return {
      ...transaction,
      timestamp: new Date(transaction.timestamp),
    };
  }

  /**
   * Format price for display
   */
  formatPrice(amount: number, currency: string = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  }

  /**
   * Calculate savings percentage
   */
  calculateSavings(originalPrice: number, discountedPrice: number): number {
    return Math.round(((originalPrice - discountedPrice) / originalPrice) * 100);
  }
}

export const premiumService = new PremiumService();
