import { UserProfile, ProfilePhoto } from '@/types/profile';
import * as ImagePicker from 'expo-image-picker';
import { Alert, Platform } from 'react-native';

/**
 * Profile Utility Functions
 * Comprehensive utilities for profile management
 */

export class ProfileUtils {
  /**
   * Validate profile completeness and return suggestions
   */
  static validateProfile(profile: UserProfile): {
    isValid: boolean;
    score: number;
    issues: string[];
    suggestions: string[];
  } {
    const issues: string[] = [];
    const suggestions: string[] = [];
    let score = 0;

    // Basic info validation (40 points)
    if (!profile.firstName || !profile.lastName) {
      issues.push('Missing name information');
    } else {
      score += 10;
    }

    if (!profile.bio || profile.bio.length < 10) {
      issues.push('Bio is too short');
      suggestions.push('Write a bio with at least 50 characters');
    } else if (profile.bio.length >= 50) {
      score += 15;
    } else {
      score += 10;
    }

    if (!profile.age || profile.age < 18) {
      issues.push('Invalid age');
    } else {
      score += 5;
    }

    if (!profile.location.city || !profile.location.state) {
      issues.push('Incomplete location');
      suggestions.push('Add your city and state');
    } else {
      score += 10;
    }

    // Photos validation (30 points)
    if (profile.photos.length === 0) {
      issues.push('No photos uploaded');
      suggestions.push('Add at least 2 photos');
    } else if (profile.photos.length === 1) {
      suggestions.push('Add more photos for better matches');
      score += 10;
    } else if (profile.photos.length >= 4) {
      score += 30;
    } else {
      score += 20;
    }

    // Interests validation (15 points)
    if (profile.interests.length === 0) {
      issues.push('No interests added');
      suggestions.push('Add at least 3 interests');
    } else if (profile.interests.length >= 5) {
      score += 15;
    } else if (profile.interests.length >= 3) {
      score += 10;
    } else {
      score += 5;
      suggestions.push('Add more interests');
    }

    // Professional info (10 points)
    if (profile.occupation) {
      score += 5;
    } else {
      suggestions.push('Add your occupation');
    }

    if (profile.education) {
      score += 5;
    } else {
      suggestions.push('Add your education');
    }

    // Verification (5 points)
    if (profile.verified) {
      score += 5;
    } else {
      suggestions.push('Verify your profile for better trust');
    }

    return {
      isValid: issues.length === 0,
      score: Math.min(score, 100),
      issues,
      suggestions,
    };
  }

  /**
   * Calculate profile completion percentage
   */
  static calculateCompletion(profile: UserProfile): number {
    const validation = this.validateProfile(profile);
    return validation.score;
  }

  /**
   * Optimize image for profile upload
   * Note: In production, you would install expo-image-manipulator for actual image optimization
   */
  static async optimizeImage(uri: string): Promise<string> {
    try {
      // For now, return the original URI
      // In production, you would use expo-image-manipulator:
      // const result = await ImageManipulator.manipulateAsync(uri, [...], {...});
      console.log('Image optimization would happen here in production');
      return uri;
    } catch (error) {
      console.error('Image optimization failed:', error);
      return uri; // Return original if optimization fails
    }
  }

  /**
   * Request camera and gallery permissions
   */
  static async requestMediaPermissions(): Promise<boolean> {
    try {
      const cameraPermission = await ImagePicker.requestCameraPermissionsAsync();
      const galleryPermission = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      return cameraPermission.status === 'granted' && galleryPermission.status === 'granted';
    } catch (error) {
      console.error('Permission request failed:', error);
      return false;
    }
  }

  /**
   * Generate profile share URL
   */
  static generateShareUrl(profileId: string): string {
    return `https://soulsync.app/profile/${profileId}`;
  }

  /**
   * Generate profile share text
   */
  static generateShareText(profile: UserProfile): string {
    return `Check out ${profile.name}'s profile on SoulSync! ${this.generateShareUrl(profile.id)}`;
  }

  /**
   * Validate photo before upload
   */
  static validatePhoto(photo: { uri: string; type?: string; size?: number }): {
    isValid: boolean;
    error?: string;
  } {
    // Check file type
    if (photo.type && !photo.type.startsWith('image/')) {
      return { isValid: false, error: 'Please select an image file' };
    }

    // Check file size (10MB limit)
    if (photo.size && photo.size > 10 * 1024 * 1024) {
      return { isValid: false, error: 'Image size must be less than 10MB' };
    }

    return { isValid: true };
  }

  /**
   * Reorder photos array
   */
  static reorderPhotos(photos: ProfilePhoto[], fromIndex: number, toIndex: number): ProfilePhoto[] {
    const newPhotos = [...photos];
    const [movedPhoto] = newPhotos.splice(fromIndex, 1);
    newPhotos.splice(toIndex, 0, movedPhoto);
    
    // Update order property
    return newPhotos.map((photo, index) => ({
      ...photo,
      order: index,
    }));
  }

  /**
   * Set main photo
   */
  static setMainPhoto(photos: ProfilePhoto[], photoId: string): ProfilePhoto[] {
    return photos.map(photo => ({
      ...photo,
      isMain: photo.id === photoId,
    }));
  }

  /**
   * Generate interest suggestions based on existing interests
   */
  static generateInterestSuggestions(currentInterests: string[]): string[] {
    const allInterests = [
      'Travel', 'Photography', 'Music', 'Movies', 'Reading', 'Cooking',
      'Fitness', 'Yoga', 'Running', 'Hiking', 'Dancing', 'Art',
      'Gaming', 'Sports', 'Football', 'Basketball', 'Tennis', 'Swimming',
      'Coffee', 'Wine', 'Beer', 'Food', 'Restaurants', 'Concerts',
      'Theater', 'Museums', 'Nature', 'Beach', 'Mountains', 'Camping',
      'Technology', 'Science', 'History', 'Politics', 'Philosophy',
      'Meditation', 'Spirituality', 'Volunteering', 'Animals', 'Dogs',
      'Cats', 'Fashion', 'Shopping', 'Beauty', 'Skincare', 'Makeup',
      'Cars', 'Motorcycles', 'Cycling', 'Skateboarding', 'Surfing',
      'Snowboarding', 'Skiing', 'Rock Climbing', 'Martial Arts',
    ];

    // Filter out current interests and return random suggestions
    const available = allInterests.filter(
      interest => !currentInterests.includes(interest)
    );

    // Return 10 random suggestions
    return available.sort(() => 0.5 - Math.random()).slice(0, 10);
  }

  /**
   * Format last seen time
   */
  static formatLastSeen(lastSeen: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - lastSeen.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) {
      return 'Just now';
    } else if (diffMins < 60) {
      return `${diffMins}m ago`;
    } else if (diffHours < 24) {
      return `${diffHours}h ago`;
    } else if (diffDays < 7) {
      return `${diffDays}d ago`;
    } else {
      return lastSeen.toLocaleDateString();
    }
  }

  /**
   * Calculate age from birth date
   */
  static calculateAge(birthDate: Date): number {
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  }

  /**
   * Validate profile data before saving
   */
  static validateProfileData(data: Partial<UserProfile>): {
    isValid: boolean;
    errors: { [key: string]: string };
  } {
    const errors: { [key: string]: string } = {};

    if (data.firstName && data.firstName.trim().length < 2) {
      errors.firstName = 'First name must be at least 2 characters';
    }

    if (data.lastName && data.lastName.trim().length < 2) {
      errors.lastName = 'Last name must be at least 2 characters';
    }

    if (data.bio && data.bio.length > 500) {
      errors.bio = 'Bio must be less than 500 characters';
    }

    if (data.age && (data.age < 18 || data.age > 100)) {
      errors.age = 'Age must be between 18 and 100';
    }

    if (data.interests && data.interests.length > 10) {
      errors.interests = 'You can add up to 10 interests';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
    };
  }

  /**
   * Generate profile analytics insights
   */
  static generateInsights(analytics: any): string[] {
    const insights: string[] = [];

    if (analytics.profileViews < 10) {
      insights.push('Add more photos to increase profile views');
    }

    if (analytics.likesReceived < 5) {
      insights.push('Update your bio to get more likes');
    }

    if (analytics.matchesCount === 0) {
      insights.push('Try expanding your age and distance preferences');
    }

    if (analytics.messagesReceived < analytics.matchesCount * 0.5) {
      insights.push('Send the first message to start conversations');
    }

    return insights;
  }

  /**
   * Check if profile meets feature requirements
   */
  static checkFeatureRequirements(profile: UserProfile, feature: string): {
    canAccess: boolean;
    reason?: string;
  } {
    const completion = this.calculateCompletion(profile);

    switch (feature) {
      case 'messaging':
        if (completion < 50) {
          return {
            canAccess: false,
            reason: 'Complete at least 50% of your profile to start messaging',
          };
        }
        break;

      case 'discovery':
        if (profile.photos.length === 0) {
          return {
            canAccess: false,
            reason: 'Add at least one photo to appear in discovery',
          };
        }
        break;

      case 'premium_features':
        if (!profile.verified) {
          return {
            canAccess: false,
            reason: 'Verify your profile to access premium features',
          };
        }
        break;
    }

    return { canAccess: true };
  }
}

export default ProfileUtils;
