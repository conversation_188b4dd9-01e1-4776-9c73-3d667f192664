import { LikeProfile, Match, Like } from '@/types/messaging';

// Mock data for received likes
const MOCK_RECEIVED_LIKES: LikeProfile[] = [
  {
    id: '1',
    name: '<PERSON>',
    age: 28,
    photos: [
      'https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=400',
      'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400',
    ],
    bio: 'Adventure seeker 🌟 Love hiking, photography, and good coffee ☕️',
    distance: 2,
    isMatch: false,
    isPremium: false,
    likedAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    mutualFriends: 3,
    interests: ['Photography', 'Hiking', 'Coffee', 'Travel'],
  },
  {
    id: '2',
    name: '<PERSON>',
    age: 26,
    photos: [
      'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400',
    ],
    bio: 'Yoga instructor and wellness enthusiast 🧘‍♀️',
    distance: 5,
    isMatch: true,
    isPremium: false,
    likedAt: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
    mutualFriends: 1,
    interests: ['Yoga', 'Wellness', 'Meditation', 'Healthy Living'],
  },
  {
    id: '3',
    name: 'Isabella',
    age: 30,
    photos: [
      'https://images.pexels.com/photos/1547971/pexels-photo-1547971.jpeg?auto=compress&cs=tinysrgb&w=400',
    ],
    bio: 'Artist and creative soul 🎨 Always looking for inspiration',
    distance: 8,
    isMatch: false,
    isPremium: true,
    likedAt: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
    mutualFriends: 0,
    interests: ['Art', 'Painting', 'Museums', 'Design'],
  },
  {
    id: '4',
    name: 'Olivia',
    age: 24,
    photos: [
      'https://images.pexels.com/photos/1181690/pexels-photo-1181690.jpeg?auto=compress&cs=tinysrgb&w=400',
    ],
    bio: 'Medical student with a passion for helping others 👩‍⚕️',
    distance: 3,
    isMatch: false,
    isPremium: false,
    likedAt: new Date(Date.now() - 8 * 60 * 60 * 1000), // 8 hours ago
    mutualFriends: 2,
    interests: ['Medicine', 'Volunteering', 'Reading', 'Running'],
  },
  {
    id: '5',
    name: 'Ava',
    age: 27,
    photos: [
      'https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=400',
    ],
    bio: 'Tech entrepreneur building the future 💻',
    distance: 12,
    isMatch: false,
    isPremium: true,
    likedAt: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours ago
    mutualFriends: 5,
    interests: ['Technology', 'Startups', 'Innovation', 'Coding'],
  },
];

// Mock data for matches
const MOCK_MATCHES: (Match & { 
  otherUser: {
    id: string;
    name: string;
    age: number;
    photo: string;
    lastMessage?: string;
    lastMessageTime?: string;
    unread?: boolean;
  }
})[] = [
  {
    id: 'match_1',
    users: ['current-user', 'user_1'],
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    status: 'active',
    otherUser: {
      id: 'user_1',
      name: 'Sophia',
      age: 26,
      photo: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400',
      lastMessage: 'Hey! Thanks for the like 😊',
      lastMessageTime: '2 hours ago',
      unread: true,
    },
  },
  {
    id: 'match_2',
    users: ['current-user', 'user_2'],
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
    status: 'active',
    otherUser: {
      id: 'user_2',
      name: 'Olivia',
      age: 24,
      photo: 'https://images.pexels.com/photos/1181690/pexels-photo-1181690.jpeg?auto=compress&cs=tinysrgb&w=400',
      lastMessage: 'Would love to grab coffee sometime!',
      lastMessageTime: '1 day ago',
      unread: false,
    },
  },
  {
    id: 'match_3',
    users: ['current-user', 'user_3'],
    timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
    status: 'active',
    otherUser: {
      id: 'user_3',
      name: 'Emma',
      age: 28,
      photo: 'https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=400',
      lastMessage: 'That hiking trail looks amazing!',
      lastMessageTime: '3 days ago',
      unread: false,
    },
  },
];

class LikesService {
  // Simulate API delay
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async fetchReceivedLikes(page: number = 1, limit: number = 20): Promise<{
    likes: LikeProfile[];
    hasMore: boolean;
    total: number;
    page: number;
    limit: number;
  }> {
    await this.delay(500); // Simulate network delay
    
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedLikes = MOCK_RECEIVED_LIKES.slice(startIndex, endIndex);
    const hasMore = endIndex < MOCK_RECEIVED_LIKES.length;
    
    return {
      likes: paginatedLikes,
      hasMore,
      total: MOCK_RECEIVED_LIKES.length,
      page,
      limit,
    };
  }

  async fetchMatches(page: number = 1, limit: number = 20): Promise<{
    matches: typeof MOCK_MATCHES;
    hasMore: boolean;
    total: number;
    page: number;
    limit: number;
  }> {
    await this.delay(400); // Simulate network delay
    
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedMatches = MOCK_MATCHES.slice(startIndex, endIndex);
    const hasMore = endIndex < MOCK_MATCHES.length;
    
    return {
      matches: paginatedMatches,
      hasMore,
      total: MOCK_MATCHES.length,
      page,
      limit,
    };
  }

  async sendLike(userId: string, type: 'like' | 'superlike'): Promise<{
    like: Like;
    isMatch: boolean;
    message: string;
  }> {
    await this.delay(300); // Simulate network delay
    
    const like: Like = {
      id: `like_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      fromUserId: 'current-user',
      toUserId: userId,
      timestamp: new Date(),
      type,
    };
    
    // Simulate match probability (30% for regular like, 50% for superlike)
    const matchProbability = type === 'superlike' ? 0.5 : 0.3;
    const isMatch = Math.random() < matchProbability;
    
    if (isMatch) {
      like.isMatch = true;
    }
    
    return {
      like,
      isMatch,
      message: isMatch ? 'It\'s a match!' : 'Like sent successfully',
    };
  }

  async sendPass(userId: string): Promise<{ message: string }> {
    await this.delay(200); // Simulate network delay
    
    return {
      message: 'Pass recorded successfully',
    };
  }

  async likeBack(userId: string): Promise<{
    isMatch: boolean;
    match?: Match;
    message: string;
  }> {
    await this.delay(300); // Simulate network delay
    
    // Liking back always creates a match
    const isMatch = true;
    
    const match: Match = {
      id: `match_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      users: ['current-user', userId],
      timestamp: new Date(),
      status: 'active',
    };
    
    return {
      isMatch,
      match,
      message: 'It\'s a match!',
    };
  }
}

export const likesService = new LikesService();
