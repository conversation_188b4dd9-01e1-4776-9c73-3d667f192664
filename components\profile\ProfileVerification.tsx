import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Platform,
  Modal,
  ScrollView,
} from 'react-native';
import { useProfileStore } from '@/stores/profileStore';
import { theme } from '@/constants/theme';
import * as Haptics from 'expo-haptics';
import {
  Shield,
  Camera,
  FileText,
  CheckCircle,
  Clock,
  X,
  Info,
  Star,
} from 'lucide-react-native';

interface ProfileVerificationProps {
  visible: boolean;
  onClose: () => void;
}

export default function ProfileVerification({ visible, onClose }: ProfileVerificationProps) {
  const { profile, updateProfile } = useProfileStore();
  const [selectedMethod, setSelectedMethod] = useState<'photo' | 'document' | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const verificationMethods = [
    {
      id: 'photo',
      title: 'Photo Verification',
      subtitle: 'Take a selfie with a specific pose',
      icon: Camera,
      description: 'We\'ll ask you to take a selfie making a specific gesture to verify your identity.',
      steps: [
        'Take a clear selfie',
        'Follow the pose instructions',
        'Submit for review',
        'Get verified within 24 hours'
      ],
      estimatedTime: '2-3 minutes',
    },
    {
      id: 'document',
      title: 'ID Verification',
      subtitle: 'Upload a government-issued ID',
      icon: FileText,
      description: 'Upload a photo of your government-issued ID to verify your identity.',
      steps: [
        'Take a photo of your ID',
        'Ensure all details are visible',
        'Submit for review',
        'Get verified within 24-48 hours'
      ],
      estimatedTime: '1-2 minutes',
    },
  ];

  const benefits = [
    'Increased trust from other users',
    'Higher visibility in discovery',
    'Access to verified-only features',
    'Blue verification badge on profile',
    'Priority customer support',
  ];

  const handleMethodSelect = (method: 'photo' | 'document') => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    setSelectedMethod(method);
  };

  const handleStartVerification = async () => {
    if (!selectedMethod) return;

    setIsSubmitting(true);

    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }

    try {
      // Simulate verification process
      await new Promise(resolve => setTimeout(resolve, 2000));

      // For demo purposes, we'll mark as verified immediately
      if (profile) {
        await updateProfile({ verified: true });
      }

      Alert.alert(
        'Verification Submitted!',
        'Your verification has been submitted and is being reviewed. You\'ll receive a notification once it\'s complete.',
        [{ text: 'OK', onPress: onClose }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to submit verification. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const MethodCard = ({ method }: { method: typeof verificationMethods[0] }) => (
    <TouchableOpacity
      style={[
        styles.methodCard,
        selectedMethod === method.id && styles.selectedMethodCard
      ]}
      onPress={() => handleMethodSelect(method.id as 'photo' | 'document')}
    >
      <View style={styles.methodHeader}>
        <View style={[
          styles.methodIcon,
          selectedMethod === method.id && styles.selectedMethodIcon
        ]}>
          <method.icon 
            size={24} 
            color={selectedMethod === method.id ? 'white' : theme.colors.primary} 
          />
        </View>
        <View style={styles.methodInfo}>
          <Text style={styles.methodTitle}>{method.title}</Text>
          <Text style={styles.methodSubtitle}>{method.subtitle}</Text>
        </View>
        {selectedMethod === method.id && (
          <CheckCircle size={20} color={theme.colors.primary} />
        )}
      </View>
      
      <Text style={styles.methodDescription}>{method.description}</Text>
      
      <View style={styles.methodDetails}>
        <View style={styles.timeContainer}>
          <Clock size={16} color={theme.colors.gray500} />
          <Text style={styles.timeText}>{method.estimatedTime}</Text>
        </View>
      </View>

      {selectedMethod === method.id && (
        <View style={styles.stepsContainer}>
          <Text style={styles.stepsTitle}>Verification Steps:</Text>
          {method.steps.map((step, index) => (
            <View key={index} style={styles.stepItem}>
              <Text style={styles.stepNumber}>{index + 1}</Text>
              <Text style={styles.stepText}>{step}</Text>
            </View>
          ))}
        </View>
      )}
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <View style={styles.headerIcon}>
              <Shield size={24} color={theme.colors.primary} />
            </View>
            <View>
              <Text style={styles.headerTitle}>Profile Verification</Text>
              <Text style={styles.headerSubtitle}>Verify your identity</Text>
            </View>
          </View>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <X size={24} color={theme.colors.gray600} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Benefits Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Why Verify Your Profile?</Text>
            <View style={styles.benefitsContainer}>
              {benefits.map((benefit, index) => (
                <View key={index} style={styles.benefitItem}>
                  <Star size={16} color={theme.colors.accent} />
                  <Text style={styles.benefitText}>{benefit}</Text>
                </View>
              ))}
            </View>
          </View>

          {/* Verification Methods */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Choose Verification Method</Text>
            <View style={styles.methodsContainer}>
              {verificationMethods.map((method) => (
                <MethodCard key={method.id} method={method} />
              ))}
            </View>
          </View>

          {/* Info Section */}
          <View style={styles.infoSection}>
            <View style={styles.infoHeader}>
              <Info size={20} color={theme.colors.primary} />
              <Text style={styles.infoTitle}>Important Information</Text>
            </View>
            <Text style={styles.infoText}>
              • Your personal information is encrypted and secure{'\n'}
              • Verification typically takes 24-48 hours{'\n'}
              • You'll receive a notification when complete{'\n'}
              • Contact support if you need help with verification
            </Text>
          </View>
        </ScrollView>

        {/* Footer */}
        <View style={styles.footer}>
          <TouchableOpacity
            style={[
              styles.startButton,
              (!selectedMethod || isSubmitting) && styles.startButtonDisabled
            ]}
            onPress={handleStartVerification}
            disabled={!selectedMethod || isSubmitting}
          >
            <Text style={styles.startButtonText}>
              {isSubmitting ? 'Submitting...' : 'Start Verification'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray200,
    backgroundColor: 'white',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
  },
  headerSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
  },
  closeButton: {
    padding: 8,
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    marginVertical: 10,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 16,
  },
  benefitsContainer: {
    gap: 12,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  benefitText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text,
    flex: 1,
  },
  methodsContainer: {
    gap: 16,
  },
  methodCard: {
    borderWidth: 2,
    borderColor: theme.colors.gray200,
    borderRadius: 12,
    padding: 16,
  },
  selectedMethodCard: {
    borderColor: theme.colors.primary,
    backgroundColor: theme.colors.primary + '05',
  },
  methodHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  methodIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: theme.colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  selectedMethodIcon: {
    backgroundColor: theme.colors.primary,
  },
  methodInfo: {
    flex: 1,
  },
  methodTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 2,
  },
  methodSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
  },
  methodDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray700,
    lineHeight: 20,
    marginBottom: 12,
  },
  methodDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  timeText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: theme.colors.gray500,
  },
  stepsContainer: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: theme.colors.gray200,
  },
  stepsTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 12,
  },
  stepItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  stepNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: theme.colors.primary,
    color: 'white',
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    textAlign: 'center',
    lineHeight: 24,
    marginRight: 12,
  },
  stepText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text,
    flex: 1,
  },
  infoSection: {
    backgroundColor: theme.colors.primary + '10',
    marginHorizontal: 20,
    marginVertical: 10,
    borderRadius: 16,
    padding: 20,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.primary,
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  infoTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
  },
  infoText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray700,
    lineHeight: 20,
  },
  footer: {
    backgroundColor: 'white',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: theme.colors.gray200,
  },
  startButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  startButtonDisabled: {
    opacity: 0.6,
  },
  startButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
});
