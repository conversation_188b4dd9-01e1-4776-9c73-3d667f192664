import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import * as Haptics from 'expo-haptics';
import {
  ArrowLeft,
  Shield,
  Camera,
  Phone,
  Mail,
  CheckCircle,
  Clock,
  AlertTriangle,
  User,
  FileText,
  Video,
  Star,
} from 'lucide-react-native';

import { theme } from '../../constants/theme';
import { useProfileStore } from '../../stores/profileStore';
import LoadingSkeleton from '../../components/LoadingSkeleton';

export default function VerificationScreen() {
  const router = useRouter();
  const { profile, isLoading } = useProfileStore();
  
  const [verificationStatus] = useState({
    identity: {
      verified: false,
      status: 'not_started' as 'pending' | 'approved' | 'rejected' | 'not_started',
      method: undefined,
    },
    phone: {
      verified: false,
      status: 'not_started' as 'pending' | 'approved' | 'rejected' | 'not_started',
      number: undefined,
    },
    email: {
      verified: true,
      status: 'approved' as 'pending' | 'approved' | 'rejected' | 'not_started',
    },
    photo: {
      verified: false,
      status: 'not_started' as 'pending' | 'approved' | 'rejected' | 'not_started',
    },
  });

  const handleBack = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    router.back();
  };

  const handleIdentityVerification = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    Alert.alert(
      'Identity Verification',
      'Choose your verification method',
      [
        { text: 'Government ID', onPress: () => startIDVerification() },
        { text: 'Selfie Verification', onPress: () => startSelfieVerification() },
        { text: 'Video Call', onPress: () => startVideoVerification() },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const startIDVerification = () => {
    Alert.alert(
      'Government ID Verification',
      'You will need to upload a photo of your government-issued ID (driver\'s license, passport, or national ID card).',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Continue', onPress: () => {
          Alert.alert('ID Upload', 'ID verification flow would start here');
        }},
      ]
    );
  };

  const startSelfieVerification = () => {
    Alert.alert(
      'Selfie Verification',
      'Take a selfie following our guidelines to verify your identity.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Take Selfie', onPress: () => {
          Alert.alert('Selfie Verification', 'Selfie verification flow would start here');
        }},
      ]
    );
  };

  const startVideoVerification = () => {
    Alert.alert(
      'Video Call Verification',
      'Schedule a quick video call with our verification team.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Schedule Call', onPress: () => {
          Alert.alert('Video Verification', 'Video call scheduling would open here');
        }},
      ]
    );
  };

  const handlePhoneVerification = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    Alert.alert(
      'Phone Verification',
      'We will send a verification code to your phone number.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Send Code', onPress: () => {
          Alert.alert('Code Sent', 'Phone verification flow would start here');
        }},
      ]
    );
  };

  const handleEmailVerification = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    if (verificationStatus.email.verified) {
      Alert.alert('Already Verified', 'Your email is already verified!');
      return;
    }
    
    Alert.alert(
      'Email Verification',
      'We will send a verification link to your email address.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Send Email', onPress: () => {
          Alert.alert('Email Sent', 'Please check your email and click the verification link.');
        }},
      ]
    );
  };

  const handlePhotoVerification = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    Alert.alert(
      'Photo Verification',
      'We will review your profile photos to ensure they meet our guidelines.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Submit for Review', onPress: () => {
          Alert.alert('Submitted', 'Your photos have been submitted for review. You will be notified within 24 hours.');
        }},
      ]
    );
  };

  if (isLoading) {
    return (
      <LinearGradient colors={['#8B5CF6', '#EC4899']} style={styles.container}>
        <SafeAreaView style={styles.safeArea}>
          <LoadingSkeleton />
        </SafeAreaView>
      </LinearGradient>
    );
  }

  const getStatusIcon = (status: string, verified: boolean) => {
    if (verified || status === 'approved') {
      return <CheckCircle size={20} color={theme.colors.success} />;
    } else if (status === 'pending') {
      return <Clock size={20} color={theme.colors.warning} />;
    } else if (status === 'rejected') {
      return <AlertTriangle size={20} color={theme.colors.error} />;
    } else {
      return <AlertTriangle size={20} color={theme.colors.gray400} />;
    }
  };

  const getStatusText = (status: string, verified: boolean) => {
    if (verified || status === 'approved') {
      return 'Verified';
    } else if (status === 'pending') {
      return 'Pending Review';
    } else if (status === 'rejected') {
      return 'Rejected';
    } else {
      return 'Not Started';
    }
  };

  const VerificationItem = ({ 
    icon: Icon, 
    title, 
    subtitle, 
    status,
    verified,
    onPress,
    benefits,
  }: {
    icon: any;
    title: string;
    subtitle: string;
    status: string;
    verified: boolean;
    onPress: () => void;
    benefits: string[];
  }) => (
    <TouchableOpacity style={styles.verificationItem} onPress={onPress}>
      <View style={styles.verificationLeft}>
        <View style={[styles.verificationIcon, verified && styles.verifiedIcon]}>
          <Icon size={24} color={verified ? 'white' : theme.colors.primary} />
        </View>
        <View style={styles.verificationContent}>
          <Text style={styles.verificationTitle}>{title}</Text>
          <Text style={styles.verificationSubtitle}>{subtitle}</Text>
          <View style={styles.statusContainer}>
            {getStatusIcon(status, verified)}
            <Text style={[
              styles.statusText,
              verified && styles.verifiedText,
              status === 'rejected' && styles.rejectedText,
            ]}>
              {getStatusText(status, verified)}
            </Text>
          </View>
          <View style={styles.benefitsContainer}>
            {benefits.map((benefit, index) => (
              <Text key={index} style={styles.benefitText}>• {benefit}</Text>
            ))}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  const completedVerifications = Object.values(verificationStatus).filter(v => v.verified || v.status === 'approved').length;
  const totalVerifications = Object.keys(verificationStatus).length;
  const verificationProgress = (completedVerifications / totalVerifications) * 100;

  return (
    <LinearGradient colors={['#8B5CF6', '#EC4899']} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <ArrowLeft size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Profile Verification</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Progress Overview */}
          <View style={styles.section}>
            <View style={styles.progressContainer}>
              <View style={styles.progressHeader}>
                <Shield size={32} color={theme.colors.primary} />
                <Text style={styles.progressTitle}>Verification Progress</Text>
                <Text style={styles.progressSubtitle}>
                  {completedVerifications} of {totalVerifications} completed
                </Text>
              </View>
              <View style={styles.progressBar}>
                <View 
                  style={[styles.progressFill, { width: `${verificationProgress}%` }]} 
                />
              </View>
              <Text style={styles.progressText}>{Math.round(verificationProgress)}% Complete</Text>
            </View>
          </View>

          {/* Benefits */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Why Verify Your Profile?</Text>
            <View style={styles.benefitsList}>
              <View style={styles.benefitItem}>
                <Star size={16} color={theme.colors.primary} />
                <Text style={styles.benefitText}>Increase trust with other users</Text>
              </View>
              <View style={styles.benefitItem}>
                <Shield size={16} color={theme.colors.primary} />
                <Text style={styles.benefitText}>Get priority in search results</Text>
              </View>
              <View style={styles.benefitItem}>
                <CheckCircle size={16} color={theme.colors.primary} />
                <Text style={styles.benefitText}>Access to verified-only features</Text>
              </View>
            </View>
          </View>

          {/* Verification Options */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Verification Methods</Text>
            
            <VerificationItem
              icon={User}
              title="Identity Verification"
              subtitle="Verify your identity with government ID"
              status={verificationStatus.identity.status}
              verified={verificationStatus.identity.verified}
              onPress={handleIdentityVerification}
              benefits={['Verified badge on profile', 'Higher match priority']}
            />
            
            <VerificationItem
              icon={Phone}
              title="Phone Verification"
              subtitle="Verify your phone number"
              status={verificationStatus.phone.status}
              verified={verificationStatus.phone.verified}
              onPress={handlePhoneVerification}
              benefits={['Account security', 'Password recovery']}
            />
            
            <VerificationItem
              icon={Mail}
              title="Email Verification"
              subtitle="Verify your email address"
              status={verificationStatus.email.status}
              verified={verificationStatus.email.verified}
              onPress={handleEmailVerification}
              benefits={['Account notifications', 'Password recovery']}
            />
            
            <VerificationItem
              icon={Camera}
              title="Photo Verification"
              subtitle="Verify your profile photos"
              status={verificationStatus.photo.status}
              verified={verificationStatus.photo.verified}
              onPress={handlePhotoVerification}
              benefits={['Authentic profile badge', 'Better match quality']}
            />
          </View>
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  placeholder: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    marginVertical: 10,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 12,
  },
  progressContainer: {
    alignItems: 'center',
    paddingVertical: 30,
    paddingHorizontal: 20,
  },
  progressHeader: {
    alignItems: 'center',
    marginBottom: 20,
  },
  progressTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginTop: 12,
    marginBottom: 4,
  },
  progressSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
  },
  progressBar: {
    width: '100%',
    height: 8,
    backgroundColor: theme.colors.gray200,
    borderRadius: 4,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: theme.colors.primary,
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: theme.colors.primary,
  },
  benefitsList: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  benefitText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text,
    marginLeft: 12,
  },
  verificationItem: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray100,
  },
  verificationLeft: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  verificationIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: theme.colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  verifiedIcon: {
    backgroundColor: theme.colors.success,
  },
  verificationContent: {
    flex: 1,
  },
  verificationTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 4,
  },
  verificationSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
    marginBottom: 8,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  statusText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: theme.colors.gray600,
    marginLeft: 8,
  },
  verifiedText: {
    color: theme.colors.success,
  },
  rejectedText: {
    color: theme.colors.error,
  },
  benefitsContainer: {
    marginTop: 8,
  },
});
