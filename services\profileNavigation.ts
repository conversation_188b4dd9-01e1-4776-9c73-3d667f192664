import { router } from 'expo-router';
import { Alert, Platform } from 'react-native';
import * as Haptics from 'expo-haptics';
import { useProfileStore } from '@/stores/profileStore';
import ProfileUtils from './profileUtils';

/**
 * Profile Navigation Service
 * Centralized navigation logic for profile-related screens
 */

export class ProfileNavigationService {
  /**
   * Navigate with haptic feedback
   */
  private static navigateWithHaptics(path: string) {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    router.push(path as any);
  }

  /**
   * Navigate to profile edit with validation
   */
  static navigateToEdit() {
    const { profile } = useProfileStore.getState();
    
    if (!profile) {
      Alert.alert('Error', 'Profile not loaded. Please try again.');
      return;
    }

    this.navigateWithHaptics('/profile/edit');
  }

  /**
   * Navigate to photo management
   */
  static navigateToPhotos() {
    const { profile } = useProfileStore.getState();
    
    if (!profile) {
      Alert.alert('Error', 'Profile not loaded. Please try again.');
      return;
    }

    this.navigateWithHaptics('/profile/photos');
  }

  /**
   * Navigate to settings
   */
  static navigateToSettings() {
    this.navigateWithHaptics('/profile/settings');
  }

  /**
   * Navigate to analytics with feature check
   */
  static navigateToAnalytics() {
    const { profile } = useProfileStore.getState();
    
    if (!profile) {
      Alert.alert('Error', 'Profile not loaded. Please try again.');
      return;
    }

    // Check if user has enough data for meaningful analytics
    if (profile.profileCompletion < 30) {
      Alert.alert(
        'Complete Your Profile',
        'Complete at least 30% of your profile to view analytics.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Complete Profile', onPress: () => this.navigateToEdit() },
        ]
      );
      return;
    }

    this.navigateWithHaptics('/profile/analytics');
  }

  /**
   * Navigate to notifications settings
   */
  static navigateToNotifications() {
    this.navigateWithHaptics('/profile/notifications');
  }

  /**
   * Navigate to premium features
   */
  static navigateToPremium() {
    this.navigateWithHaptics('/profile/premium');
  }

  /**
   * Navigate to help & support
   */
  static navigateToHelp() {
    this.navigateWithHaptics('/profile/help');
  }

  /**
   * Navigate to verification with requirements check
   */
  static navigateToVerification() {
    const { profile } = useProfileStore.getState();
    
    if (!profile) {
      Alert.alert('Error', 'Profile not loaded. Please try again.');
      return;
    }

    if (profile.verified) {
      Alert.alert('Already Verified', 'Your profile is already verified!');
      return;
    }

    // Check verification requirements
    const requirements = ProfileUtils.checkFeatureRequirements(profile, 'verification');
    if (!requirements.canAccess) {
      Alert.alert('Requirements Not Met', requirements.reason);
      return;
    }

    // For now, show verification modal (would be implemented in ProfileCompletion component)
    Alert.alert('Verification', 'Verification feature would open here');
  }

  /**
   * Handle profile completion navigation
   */
  static handleCompletionNavigation(action: string) {
    const { profile } = useProfileStore.getState();
    
    if (!profile) return;

    switch (action) {
      case 'add_photos':
        this.navigateToPhotos();
        break;
      case 'edit_bio':
      case 'add_interests':
      case 'add_location':
      case 'add_occupation':
        this.navigateToEdit();
        break;
      case 'verify_profile':
        this.navigateToVerification();
        break;
      default:
        console.warn('Unknown completion action:', action);
    }
  }

  /**
   * Navigate with feature access validation
   */
  static navigateWithFeatureCheck(feature: string, path: string) {
    const { profile } = useProfileStore.getState();
    
    if (!profile) {
      Alert.alert('Error', 'Profile not loaded. Please try again.');
      return;
    }

    const requirements = ProfileUtils.checkFeatureRequirements(profile, feature);
    
    if (!requirements.canAccess) {
      Alert.alert(
        'Feature Unavailable',
        requirements.reason,
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Complete Profile', onPress: () => this.navigateToEdit() },
        ]
      );
      return;
    }

    this.navigateWithHaptics(path);
  }

  /**
   * Handle logout with confirmation
   */
  static handleLogout() {
    Alert.alert(
      'Log Out',
      'Are you sure you want to log out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Log Out',
          style: 'destructive',
          onPress: () => {
            // Clear profile store
            const { reset } = useProfileStore.getState();
            reset();
            
            // Navigate to auth screen
            router.replace('/auth' as any);
          },
        },
      ]
    );
  }

  /**
   * Navigate to external links
   */
  static navigateToExternalLink(url: string, title: string) {
    Alert.alert(
      title,
      `This will open ${url} in your browser.`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Open', onPress: () => {
          // In a real app, you'd use Linking.openURL(url)
          console.log('Opening external link:', url);
        }},
      ]
    );
  }

  /**
   * Handle deep link navigation
   */
  static handleDeepLink(path: string, params?: Record<string, any>) {
    try {
      if (params) {
        const queryString = Object.entries(params)
          .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
          .join('&');
        router.push(`${path}?${queryString}` as any);
      } else {
        router.push(path as any);
      }
    } catch (error) {
      console.error('Deep link navigation failed:', error);
      Alert.alert('Navigation Error', 'Failed to navigate to the requested page.');
    }
  }

  /**
   * Navigate back with validation
   */
  static navigateBack(fallbackPath?: string) {
    try {
      if (router.canGoBack()) {
        router.back();
      } else if (fallbackPath) {
        router.replace(fallbackPath as any);
      } else {
        router.replace('/(tabs)/profile' as any);
      }
    } catch (error) {
      console.error('Navigation back failed:', error);
      router.replace('/(tabs)/profile' as any);
    }
  }

  /**
   * Navigate to profile sharing
   */
  static navigateToShare() {
    const { profile } = useProfileStore.getState();
    
    if (!profile) {
      Alert.alert('Error', 'Profile not loaded. Please try again.');
      return;
    }

    if (profile.profileCompletion < 50) {
      Alert.alert(
        'Complete Your Profile',
        'Complete at least 50% of your profile before sharing.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Complete Profile', onPress: () => this.navigateToEdit() },
        ]
      );
      return;
    }

    // For now, show share options (would be implemented in ProfileShare component)
    Alert.alert('Share Profile', 'Profile sharing feature would open here');
  }

  /**
   * Navigate to specific settings section
   */
  static navigateToSettingsSection(section: string) {
    switch (section) {
      case 'notifications':
        this.navigateToNotifications();
        break;
      case 'privacy':
        this.navigateToSettings();
        break;
      case 'account':
        this.navigateToSettings();
        break;
      default:
        this.navigateToSettings();
    }
  }

  /**
   * Handle tab navigation with state preservation
   */
  static navigateToTab(tab: string) {
    try {
      router.push(`/(tabs)/${tab}` as any);
    } catch (error) {
      console.error('Tab navigation failed:', error);
    }
  }

  /**
   * Navigate to profile with user ID (for viewing other profiles)
   */
  static navigateToUserProfile(userId: string) {
    this.navigateWithHaptics(`/profile/view/${userId}`);
  }

  /**
   * Handle conditional navigation based on profile state
   */
  static conditionalNavigate(condition: string, truePath: string, falsePath: string) {
    const { profile } = useProfileStore.getState();
    
    if (!profile) {
      Alert.alert('Error', 'Profile not loaded. Please try again.');
      return;
    }

    let shouldNavigateToTrue = false;

    switch (condition) {
      case 'is_verified':
        shouldNavigateToTrue = profile.verified;
        break;
      case 'has_photos':
        shouldNavigateToTrue = profile.photos.length > 0;
        break;
      case 'is_complete':
        shouldNavigateToTrue = profile.profileCompletion >= 80;
        break;
      case 'has_premium':
        shouldNavigateToTrue = profile.isPremium || false;
        break;
      default:
        console.warn('Unknown condition:', condition);
        return;
    }

    this.navigateWithHaptics(shouldNavigateToTrue ? truePath : falsePath);
  }

  /**
   * Batch navigation for onboarding flow
   */
  static startOnboardingFlow() {
    const { profile } = useProfileStore.getState();
    
    if (!profile) {
      Alert.alert('Error', 'Profile not loaded. Please try again.');
      return;
    }

    const validation = ProfileUtils.validateProfile(profile);
    
    if (validation.score < 30) {
      // Start with basic info
      this.navigateToEdit();
    } else if (profile.photos.length === 0) {
      // Add photos
      this.navigateToPhotos();
    } else if (!profile.verified) {
      // Verify profile
      this.navigateToVerification();
    } else {
      // Profile is complete
      Alert.alert('Profile Complete!', 'Your profile looks great!');
    }
  }
}

export default ProfileNavigationService;
