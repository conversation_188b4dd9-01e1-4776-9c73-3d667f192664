import { LikeProfile } from '@/types/messaging';

// Mock data for received likes
const MOCK_RECEIVED_LIKES: LikeProfile[] = [
  {
    id: '1',
    name: '<PERSON>',
    age: 28,
    photos: [
      'https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=400',
      'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400',
    ],
    bio: 'Adventure seeker 🌟 Love hiking, photography, and good coffee ☕️',
    distance: 2,
    isMatch: false,
    isPremium: false,
    likedAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    mutualFriends: 3,
    interests: ['Photography', 'Hiking', 'Coffee', 'Travel'],
  },
  {
    id: '2',
    name: '<PERSON>',
    age: 26,
    photos: [
      'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400',
    ],
    bio: 'Yoga instructor and wellness enthusiast 🧘‍♀️',
    distance: 5,
    isMatch: true,
    isPremium: false,
    likedAt: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
    mutualFriends: 1,
    interests: ['Yoga', 'Wellness', 'Meditation', 'Healthy Living'],
  },
  {
    id: '3',
    name: 'Isabella',
    age: 30,
    photos: [
      'https://images.pexels.com/photos/1547971/pexels-photo-1547971.jpeg?auto=compress&cs=tinysrgb&w=400',
    ],
    bio: 'Artist and creative soul 🎨 Always looking for inspiration',
    distance: 8,
    isMatch: false,
    isPremium: true,
    likedAt: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
    mutualFriends: 0,
    interests: ['Art', 'Painting', 'Museums', 'Design'],
  },
  {
    id: '4',
    name: 'Olivia',
    age: 24,
    photos: [
      'https://images.pexels.com/photos/1181690/pexels-photo-1181690.jpeg?auto=compress&cs=tinysrgb&w=400',
    ],
    bio: 'Medical student with a passion for helping others 👩‍⚕️',
    distance: 3,
    isMatch: false,
    isPremium: false,
    likedAt: new Date(Date.now() - 8 * 60 * 60 * 1000), // 8 hours ago
    mutualFriends: 2,
    interests: ['Medicine', 'Volunteering', 'Reading', 'Running'],
  },
  {
    id: '5',
    name: 'Ava',
    age: 27,
    photos: [
      'https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=400',
    ],
    bio: 'Tech entrepreneur building the future 💻',
    distance: 12,
    isMatch: false,
    isPremium: true,
    likedAt: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours ago
    mutualFriends: 5,
    interests: ['Technology', 'Startups', 'Innovation', 'Coding'],
  },
];

export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Calculate pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedLikes = MOCK_RECEIVED_LIKES.slice(startIndex, endIndex);
    const hasMore = endIndex < MOCK_RECEIVED_LIKES.length;
    
    return Response.json({
      success: true,
      likes: paginatedLikes,
      hasMore,
      total: MOCK_RECEIVED_LIKES.length,
      page,
      limit,
    });
  } catch (error) {
    console.error('Error fetching received likes:', error);
    return Response.json(
      { 
        success: false, 
        error: 'Failed to fetch received likes' 
      },
      { status: 500 }
    );
  }
}
