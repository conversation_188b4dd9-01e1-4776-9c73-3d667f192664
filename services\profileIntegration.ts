import { useProfileStore } from '@/stores/profileStore';
import { useMessagesStore } from '@/stores/messagesStore';
import { useLikesStore } from '@/stores/likesStore';
import { UserProfile } from '@/types/profile';
import { User } from '@/types/messaging';

/**
 * Profile Integration Service
 * Ensures profile data is synchronized across all app sections
 */
export class ProfileIntegrationService {
  private static instance: ProfileIntegrationService;

  public static getInstance(): ProfileIntegrationService {
    if (!ProfileIntegrationService.instance) {
      ProfileIntegrationService.instance = new ProfileIntegrationService();
    }
    return ProfileIntegrationService.instance;
  }

  /**
   * Sync profile updates across all stores
   */
  public async syncProfileUpdate(updatedProfile: UserProfile): Promise<void> {
    try {
      // Update messages store with new user info
      const messagesStore = useMessagesStore.getState();
      if (messagesStore.currentUser) {
        const updatedUser: User = {
          id: updatedProfile.id,
          name: updatedProfile.name,
          avatar: updatedProfile.photos.find(p => p.isMain)?.url || updatedProfile.photos[0]?.url || '',
          isOnline: updatedProfile.isOnline,
          lastSeen: updatedProfile.lastSeen,
        };
        
        messagesStore.setCurrentUser(updatedUser);
      }

      // Update conversations with new user data
      await this.updateConversationsWithNewProfile(updatedProfile);

      // Update likes store with new profile data
      await this.updateLikesWithNewProfile(updatedProfile);

      console.log('Profile synchronized across all stores');
    } catch (error) {
      console.error('Failed to sync profile update:', error);
      throw error;
    }
  }

  /**
   * Update all conversations with new profile information
   */
  private async updateConversationsWithNewProfile(profile: UserProfile): Promise<void> {
    const messagesStore = useMessagesStore.getState();
    const conversations = messagesStore.conversations;

    const updatedConversations = conversations.map(conversation => {
      const updatedParticipants = conversation.participants.map(participant => {
        if (participant.id === profile.id) {
          return {
            ...participant,
            name: profile.name,
            avatar: profile.photos.find(p => p.isMain)?.url || profile.photos[0]?.url || participant.avatar,
            isOnline: profile.isOnline,
            lastSeen: profile.lastSeen,
          };
        }
        return participant;
      });

      return {
        ...conversation,
        participants: updatedParticipants,
      };
    });

    // Update conversations in store
    for (const conversation of updatedConversations) {
      // This would typically be done through a store method
      // For now, we'll log the update
      console.log(`Updated conversation ${conversation.id} with new profile data`);
    }
  }

  /**
   * Update likes store with new profile information
   */
  private async updateLikesWithNewProfile(profile: UserProfile): Promise<void> {
    const likesStore = useLikesStore.getState();
    
    // Update received likes that might reference this user
    const updatedReceivedLikes = likesStore.receivedLikes.map(like => {
      if (like.id === profile.id) {
        return {
          ...like,
          name: profile.name,
          photos: profile.photos.map(p => p.url),
          bio: profile.bio,
          interests: profile.interests,
        };
      }
      return like;
    });

    // Update matches with new profile data
    const updatedMatches = likesStore.matches.map(match => {
      // This would update match data if the current user is part of the match
      return match;
    });

    console.log('Updated likes store with new profile data');
  }

  /**
   * Initialize profile data across all stores
   */
  public async initializeProfileData(): Promise<void> {
    try {
      const profileStore = useProfileStore.getState();
      await profileStore.loadProfile();
      
      const profile = profileStore.profile;
      if (profile) {
        await this.syncProfileUpdate(profile);
      }
    } catch (error) {
      console.error('Failed to initialize profile data:', error);
    }
  }

  /**
   * Get current user data for messaging
   */
  public getCurrentUserForMessaging(): User | null {
    const profileStore = useProfileStore.getState();
    const profile = profileStore.profile;
    
    if (!profile) return null;

    return {
      id: profile.id,
      name: profile.name,
      avatar: profile.photos.find(p => p.isMain)?.url || profile.photos[0]?.url || '',
      isOnline: profile.isOnline,
      lastSeen: profile.lastSeen,
    };
  }

  /**
   * Update online status across all stores
   */
  public async updateOnlineStatus(isOnline: boolean): Promise<void> {
    try {
      const profileStore = useProfileStore.getState();
      const profile = profileStore.profile;
      
      if (profile) {
        await profileStore.updateProfile({
          isOnline,
          lastSeen: isOnline ? undefined : new Date(),
        });

        // Update messages store
        const messagesStore = useMessagesStore.getState();
        if (messagesStore.currentUser) {
          messagesStore.setCurrentUser({
            ...messagesStore.currentUser,
            isOnline,
            lastSeen: isOnline ? undefined : new Date(),
          });
        }
      }
    } catch (error) {
      console.error('Failed to update online status:', error);
    }
  }

  /**
   * Handle profile photo updates
   */
  public async handleProfilePhotoUpdate(photoUrl: string, isMain: boolean = false): Promise<void> {
    try {
      const profileStore = useProfileStore.getState();
      const profile = profileStore.profile;
      
      if (!profile) return;

      // If this is the main photo, update avatar across all stores
      if (isMain) {
        const messagesStore = useMessagesStore.getState();
        if (messagesStore.currentUser) {
          messagesStore.setCurrentUser({
            ...messagesStore.currentUser,
            avatar: photoUrl,
          });
        }

        // Update conversations
        await this.updateConversationsWithNewProfile({
          ...profile,
          photos: profile.photos.map(p => 
            p.isMain ? { ...p, url: photoUrl } : { ...p, isMain: false }
          ),
        });
      }
    } catch (error) {
      console.error('Failed to handle profile photo update:', error);
    }
  }

  /**
   * Validate profile completeness for features
   */
  public validateProfileForFeature(feature: 'messaging' | 'matching' | 'discovery'): {
    isValid: boolean;
    missingFields: string[];
    recommendations: string[];
  } {
    const profileStore = useProfileStore.getState();
    const profile = profileStore.profile;
    
    if (!profile) {
      return {
        isValid: false,
        missingFields: ['profile'],
        recommendations: ['Please complete your profile setup'],
      };
    }

    const missingFields: string[] = [];
    const recommendations: string[] = [];

    // Basic requirements for all features
    if (!profile.photos || profile.photos.length < 2) {
      missingFields.push('photos');
      recommendations.push('Add at least 2 photos');
    }

    if (!profile.bio || profile.bio.length < 10) {
      missingFields.push('bio');
      recommendations.push('Write a bio with at least 10 characters');
    }

    if (!profile.interests || profile.interests.length < 3) {
      missingFields.push('interests');
      recommendations.push('Add at least 3 interests');
    }

    // Feature-specific requirements
    switch (feature) {
      case 'messaging':
        if (!profile.verified) {
          recommendations.push('Verify your profile for better messaging experience');
        }
        break;
        
      case 'matching':
        if (!profile.location.city) {
          missingFields.push('location');
          recommendations.push('Add your location for better matches');
        }
        break;
        
      case 'discovery':
        if (!profile.occupation) {
          recommendations.push('Add your occupation to improve discovery');
        }
        if (!profile.education) {
          recommendations.push('Add your education background');
        }
        break;
    }

    return {
      isValid: missingFields.length === 0,
      missingFields,
      recommendations,
    };
  }

  /**
   * Get profile completion suggestions
   */
  public getProfileCompletionSuggestions(): string[] {
    const profileStore = useProfileStore.getState();
    const profile = profileStore.profile;
    
    if (!profile) return ['Complete your profile setup'];

    const suggestions: string[] = [];

    if (profile.photos.length < 4) {
      suggestions.push(`Add ${4 - profile.photos.length} more photos`);
    }

    if (profile.bio.length < 50) {
      suggestions.push('Write a more detailed bio');
    }

    if (profile.interests.length < 5) {
      suggestions.push(`Add ${5 - profile.interests.length} more interests`);
    }

    if (!profile.socialMedia || Object.keys(profile.socialMedia).length === 0) {
      suggestions.push('Connect your social media accounts');
    }

    if (!profile.verified) {
      suggestions.push('Verify your profile');
    }

    if (profile.profileCompletion < 90) {
      suggestions.push('Complete remaining profile sections');
    }

    return suggestions;
  }

  /**
   * Handle profile deletion
   */
  public async handleProfileDeletion(): Promise<void> {
    try {
      // Clear profile store
      const profileStore = useProfileStore.getState();
      profileStore.reset();

      // Clear messages store
      const messagesStore = useMessagesStore.getState();
      messagesStore.reset();

      // Clear likes store
      const likesStore = useLikesStore.getState();
      likesStore.reset();

      console.log('Profile and all associated data cleared');
    } catch (error) {
      console.error('Failed to handle profile deletion:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const profileIntegrationService = ProfileIntegrationService.getInstance();
