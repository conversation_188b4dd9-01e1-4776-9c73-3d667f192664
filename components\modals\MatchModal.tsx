import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Image,
  Dimensions,
  Platform,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  withSequence,
  withDelay,
  runOnJS,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { Heart, MessageCircle, X } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import { theme } from '@/constants/theme';
import HeartBurst from '@/components/animations/HeartBurst';

const { width, height } = Dimensions.get('window');

interface MatchModalProps {
  visible: boolean;
  onClose: () => void;
  onMessage: () => void;
  onKeepSwiping: () => void;
  currentUserPhoto?: string;
  matchedUserPhoto?: string;
  matchedUserName?: string;
}

const triggerHaptic = () => {
  if (Platform.OS !== 'web') {
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
  }
};

export default function MatchModal({
  visible,
  onClose,
  onMessage,
  onKeepSwiping,
  currentUserPhoto = 'https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=400',
  matchedUserPhoto = 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400',
  matchedUserName = 'Someone Special',
}: MatchModalProps) {
  const backgroundOpacity = useSharedValue(0);
  const contentScale = useSharedValue(0);
  const titleOpacity = useSharedValue(0);
  const titleTranslateY = useSharedValue(50);
  const photosScale = useSharedValue(0);
  const buttonsOpacity = useSharedValue(0);
  const buttonsTranslateY = useSharedValue(50);
  const heartBurstVisible = useSharedValue(false);

  useEffect(() => {
    let isMounted = true;
    let timeouts: NodeJS.Timeout[] = [];

    if (visible) {
      triggerHaptic();

      // Background fade in
      backgroundOpacity.value = withTiming(1, { duration: 300 });

      // Content scale in
      contentScale.value = withSpring(1, { damping: 15, stiffness: 100 });

      // Staggered animations
      const firstTimeout = setTimeout(() => {
        if (!isMounted) return;

        // Photos animation
        photosScale.value = withSpring(1, { damping: 12, stiffness: 100 });

        // Title animation
        titleOpacity.value = withTiming(1, { duration: 400 });
        titleTranslateY.value = withSpring(0, { damping: 10, stiffness: 100 });

        // Heart burst
        heartBurstVisible.value = true;

        // Buttons animation
        const secondTimeout = setTimeout(() => {
          if (!isMounted) return;

          buttonsOpacity.value = withTiming(1, { duration: 400 });
          buttonsTranslateY.value = withSpring(0, { damping: 10, stiffness: 100 });
        }, 500);

        timeouts.push(secondTimeout);
      }, 200);

      timeouts.push(firstTimeout);
    } else {
      // Reset all values
      backgroundOpacity.value = 0;
      contentScale.value = 0;
      titleOpacity.value = 0;
      titleTranslateY.value = 50;
      photosScale.value = 0;
      buttonsOpacity.value = 0;
      buttonsTranslateY.value = 50;
      heartBurstVisible.value = false;
    }

    return () => {
      isMounted = false;
      timeouts.forEach(timeout => clearTimeout(timeout));
    };
  }, [visible]);

  const backgroundStyle = useAnimatedStyle(() => ({
    opacity: backgroundOpacity.value,
  }));

  const contentStyle = useAnimatedStyle(() => ({
    transform: [{ scale: contentScale.value }],
  }));

  const titleStyle = useAnimatedStyle(() => ({
    opacity: titleOpacity.value,
    transform: [{ translateY: titleTranslateY.value }],
  }));

  const photosStyle = useAnimatedStyle(() => ({
    transform: [{ scale: photosScale.value }],
  }));

  const buttonsStyle = useAnimatedStyle(() => ({
    opacity: buttonsOpacity.value,
    transform: [{ translateY: buttonsTranslateY.value }],
  }));

  const handleClose = () => {
    backgroundOpacity.value = withTiming(0, { duration: 200 });
    contentScale.value = withTiming(0, { duration: 200 });
    setTimeout(() => runOnJS(onClose)(), 200);
  };

  const handleMessage = () => {
    runOnJS(onMessage)();
    handleClose();
  };

  const handleKeepSwiping = () => {
    runOnJS(onKeepSwiping)();
    handleClose();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      statusBarTranslucent
    >
      <Animated.View style={[styles.overlay, backgroundStyle]}>
        <LinearGradient
          colors={['rgba(139, 92, 246, 0.9)', 'rgba(236, 72, 153, 0.9)']}
          style={styles.gradient}
        >
          <Animated.View style={[styles.container, contentStyle]}>
            {/* Close Button */}
            <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
              <X size={24} color="white" />
            </TouchableOpacity>

            {/* Title */}
            <Animated.View style={[styles.titleContainer, titleStyle]}>
              <Text style={styles.title}>It's a Match!</Text>
              <Text style={styles.subtitle}>
                You and {matchedUserName} liked each other
              </Text>
            </Animated.View>

            {/* Photos */}
            <Animated.View style={[styles.photosContainer, photosStyle]}>
              <View style={styles.photoWrapper}>
                <Image source={{ uri: currentUserPhoto }} style={styles.photo} />
              </View>
              <View style={styles.heartContainer}>
                <Heart size={40} color="white" fill="white" />
              </View>
              <View style={styles.photoWrapper}>
                <Image source={{ uri: matchedUserPhoto }} style={styles.photo} />
              </View>
            </Animated.View>

            {/* Buttons */}
            <Animated.View style={[styles.buttonsContainer, buttonsStyle]}>
              <TouchableOpacity style={styles.secondaryButton} onPress={handleKeepSwiping}>
                <Text style={styles.secondaryButtonText}>Keep Swiping</Text>
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.primaryButton} onPress={handleMessage}>
                <MessageCircle size={20} color="white" />
                <Text style={styles.primaryButtonText}>Send Message</Text>
              </TouchableOpacity>
            </Animated.View>
          </Animated.View>

          {/* Heart Burst Animation */}
          <HeartBurst 
            visible={heartBurstVisible.value} 
            size={80}
            color="white"
          />
        </LinearGradient>
      </Animated.View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  gradient: {
    width: width,
    height: height,
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    width: width - 48,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 24,
    padding: 32,
    alignItems: 'center',
    ...theme.shadows.xl,
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    padding: 8,
    zIndex: 10,
  },
  titleContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 32,
    fontFamily: 'Poppins-Bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
  },
  photosContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 40,
  },
  photoWrapper: {
    width: 100,
    height: 100,
    borderRadius: 50,
    overflow: 'hidden',
    borderWidth: 4,
    borderColor: 'white',
    ...theme.shadows.lg,
  },
  photo: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  heartContainer: {
    marginHorizontal: 20,
    padding: 8,
  },
  buttonsContainer: {
    width: '100%',
    gap: 16,
  },
  primaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 25,
    ...theme.shadows.md,
  },
  primaryButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.primary,
    marginLeft: 8,
  },
  secondaryButton: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 25,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.5)',
  },
  secondaryButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
});
