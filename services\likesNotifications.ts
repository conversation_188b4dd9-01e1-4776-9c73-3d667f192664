import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Conditionally import expo-notifications only on native platforms
let Notifications: any = null;
if (Platform.OS !== 'web') {
  try {
    Notifications = require('expo-notifications');
  } catch (error) {
    console.warn('expo-notifications not available:', error);
  }
}

export interface LikeNotificationData {
  type: 'like' | 'match' | 'superlike';
  userId: string;
  userName: string;
  userPhoto?: string;
  matchId?: string;
}

// Configure notification handler (only on native platforms)
if (Notifications) {
  Notifications.setNotificationHandler({
    handleNotification: async (notification: any) => {
      const data = notification.request.content.data as LikeNotificationData;

      return {
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: true,
        priority: data.type === 'match' ? Notifications.AndroidNotificationPriority.HIGH : Notifications.AndroidNotificationPriority.DEFAULT,
      };
    },
  });
}

export class LikesNotificationService {
  private static instance: LikesNotificationService;
  private expoPushToken: string | null = null;
  private isInitialized = false;

  private constructor() {}

  public static getInstance(): LikesNotificationService {
    if (!LikesNotificationService.instance) {
      LikesNotificationService.instance = new LikesNotificationService();
    }
    return LikesNotificationService.instance;
  }

  public async initialize(): Promise<void> {
    try {
      // Check if notifications are available
      if (!Notifications) {
        console.warn('Notifications not available on this platform');
        this.isInitialized = true; // Mark as initialized to prevent errors
        return;
      }

      // Request permissions
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.warn('Notification permissions not granted');
        this.isInitialized = true; // Still mark as initialized
        return;
      }

      // Get push token
      if (Platform.OS !== 'web') {
        try {
          const token = await Notifications.getExpoPushTokenAsync({
            projectId: 'your-expo-project-id', // Replace with your actual project ID
          });
          this.expoPushToken = token.data;
          console.log('Expo push token:', this.expoPushToken);
        } catch (error) {
          console.warn('Failed to get push token:', error);
        }
      }

      // Set up notification channels for Android
      if (Platform.OS === 'android') {
        await this.setupAndroidChannels();
      }

      this.isInitialized = true;
      console.log('LikesNotificationService initialized');
    } catch (error) {
      console.error('Failed to initialize LikesNotificationService:', error);
      this.isInitialized = true; // Mark as initialized to prevent repeated attempts
    }
  }

  private async setupAndroidChannels(): Promise<void> {
    if (!Notifications) return;

    try {
      // Likes channel
      await Notifications.setNotificationChannelAsync('likes', {
        name: 'Likes',
        description: 'Notifications for new likes',
        importance: Notifications.AndroidImportance.DEFAULT,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FF231F7C',
        sound: 'default',
      });

      // Matches channel
      await Notifications.setNotificationChannelAsync('matches', {
        name: 'Matches',
        description: 'Notifications for new matches',
        importance: Notifications.AndroidImportance.HIGH,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FF231F7C',
        sound: 'default',
      });

      // Super likes channel
      await Notifications.setNotificationChannelAsync('superlikes', {
        name: 'Super Likes',
        description: 'Notifications for super likes',
        importance: Notifications.AndroidImportance.HIGH,
        vibrationPattern: [0, 500, 250, 500],
        lightColor: '#FFD700',
        sound: 'default',
      });
    } catch (error) {
      console.warn('Failed to setup Android notification channels:', error);
    }
  }

  public async sendLikeNotification(data: LikeNotificationData): Promise<void> {
    if (!this.isInitialized) {
      console.warn('LikesNotificationService not initialized');
      return;
    }

    if (!Notifications) {
      console.log(`Would send ${data.type} notification for ${data.userName} (notifications not available)`);
      return;
    }

    try {
      const notificationContent = this.buildNotificationContent(data);

      // Check if notifications are enabled for this type
      const isEnabled = await this.isNotificationTypeEnabled(data.type);
      if (!isEnabled) {
        console.log(`Notifications disabled for type: ${data.type}`);
        return;
      }

      // Schedule local notification
      await Notifications.scheduleNotificationAsync({
        content: notificationContent,
        trigger: null, // Show immediately
      });

      // Update badge count
      await this.updateBadgeCount();

      console.log(`${data.type} notification sent for user: ${data.userName}`);
    } catch (error) {
      console.error('Failed to send like notification:', error);
    }
  }

  private buildNotificationContent(data: LikeNotificationData): any {
    let title: string;
    let body: string;
    let channelId: string;

    switch (data.type) {
      case 'like':
        title = 'New Like! 💕';
        body = `${data.userName} liked you!`;
        channelId = 'likes';
        break;
      case 'superlike':
        title = 'Super Like! ⭐';
        body = `${data.userName} super liked you!`;
        channelId = 'superlikes';
        break;
      case 'match':
        title = "It's a Match! 🎉";
        body = `You and ${data.userName} liked each other!`;
        channelId = 'matches';
        break;
      default:
        title = 'New Activity';
        body = 'You have new activity in the app';
        channelId = 'likes';
    }

    return {
      title,
      body,
      data: data,
      sound: 'default',
      badge: 1,
      ...(Platform.OS === 'android' && { channelId }),
    };
  }

  private async isNotificationTypeEnabled(type: string): Promise<boolean> {
    try {
      const settings = await AsyncStorage.getItem('notification_settings');
      if (!settings) return true; // Default to enabled

      const parsedSettings = JSON.parse(settings);
      return parsedSettings[type] !== false;
    } catch (error) {
      console.warn('Failed to check notification settings:', error);
      return true; // Default to enabled on error
    }
  }

  private async updateBadgeCount(): Promise<void> {
    if (!Notifications) return;

    try {
      // Get current badge count
      const currentBadge = await Notifications.getBadgeCountAsync();
      await Notifications.setBadgeCountAsync(currentBadge + 1);
    } catch (error) {
      console.warn('Failed to update badge count:', error);
    }
  }

  public async clearBadge(): Promise<void> {
    if (!Notifications) return;

    try {
      await Notifications.setBadgeCountAsync(0);
    } catch (error) {
      console.warn('Failed to clear badge:', error);
    }
  }

  public async setNotificationPreference(type: string, enabled: boolean): Promise<void> {
    try {
      const settings = await AsyncStorage.getItem('notification_settings');
      const parsedSettings = settings ? JSON.parse(settings) : {};
      
      parsedSettings[type] = enabled;
      
      await AsyncStorage.setItem('notification_settings', JSON.stringify(parsedSettings));
      console.log(`Notification preference updated: ${type} = ${enabled}`);
    } catch (error) {
      console.error('Failed to set notification preference:', error);
    }
  }

  public async getNotificationPreferences(): Promise<Record<string, boolean>> {
    try {
      const settings = await AsyncStorage.getItem('notification_settings');
      return settings ? JSON.parse(settings) : {
        like: true,
        match: true,
        superlike: true,
      };
    } catch (error) {
      console.warn('Failed to get notification preferences:', error);
      return {
        like: true,
        match: true,
        superlike: true,
      };
    }
  }

  public getPushToken(): string | null {
    return this.expoPushToken;
  }

  public isReady(): boolean {
    return this.isInitialized;
  }
}

// Export singleton instance
export const likesNotificationService = LikesNotificationService.getInstance();
