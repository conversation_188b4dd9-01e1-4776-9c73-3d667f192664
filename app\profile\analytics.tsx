import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useProfileStore } from '@/stores/profileStore';
import { theme } from '@/constants/theme';
import {
  ArrowLeft,
  Eye,
  Heart,
  Star,
  MessageCircle,
  TrendingUp,
  Clock,
  Users,
  Camera,
} from 'lucide-react-native';

const { width } = Dimensions.get('window');

export default function ProfileAnalyticsScreen() {
  const router = useRouter();
  const { analytics, loadAnalytics, profile } = useProfileStore();

  useEffect(() => {
    loadAnalytics();
  }, []);

  const StatCard = ({ 
    icon: Icon, 
    title, 
    value, 
    subtitle, 
    color = theme.colors.primary,
    trend 
  }: {
    icon: any;
    title: string;
    value: string | number;
    subtitle?: string;
    color?: string;
    trend?: 'up' | 'down' | 'neutral';
  }) => (
    <View style={styles.statCard}>
      <View style={[styles.statIcon, { backgroundColor: color + '20' }]}>
        <Icon size={24} color={color} />
      </View>
      <View style={styles.statContent}>
        <Text style={styles.statValue}>{value}</Text>
        <Text style={styles.statTitle}>{title}</Text>
        {subtitle && <Text style={styles.statSubtitle}>{subtitle}</Text>}
      </View>
      {trend && (
        <View style={styles.trendContainer}>
          <TrendingUp 
            size={16} 
            color={trend === 'up' ? theme.colors.success : theme.colors.error} 
          />
        </View>
      )}
    </View>
  );

  const ProgressBar = ({ 
    label, 
    value, 
    maxValue, 
    color = theme.colors.primary 
  }: {
    label: string;
    value: number;
    maxValue: number;
    color?: string;
  }) => {
    const percentage = Math.min((value / maxValue) * 100, 100);
    
    return (
      <View style={styles.progressContainer}>
        <View style={styles.progressHeader}>
          <Text style={styles.progressLabel}>{label}</Text>
          <Text style={styles.progressValue}>{value}</Text>
        </View>
        <View style={styles.progressBar}>
          <View 
            style={[
              styles.progressFill, 
              { width: `${percentage}%`, backgroundColor: color }
            ]} 
          />
        </View>
      </View>
    );
  };

  if (!analytics) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Profile Analytics</Text>
          <View style={styles.placeholder} />
        </View>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading analytics...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Profile Analytics</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Overview Stats */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Overview</Text>
          <View style={styles.statsGrid}>
            <StatCard
              icon={Eye}
              title="Profile Views"
              value={analytics.profileViews}
              subtitle="Total views"
              color={theme.colors.primary}
              trend="up"
            />
            <StatCard
              icon={Heart}
              title="Likes Received"
              value={analytics.likesReceived}
              subtitle="People who liked you"
              color={theme.colors.secondary}
              trend="up"
            />
            <StatCard
              icon={Star}
              title="Matches"
              value={analytics.matchesCount}
              subtitle="Mutual connections"
              color={theme.colors.accent}
              trend="neutral"
            />
            <StatCard
              icon={MessageCircle}
              title="Messages"
              value={analytics.messagesReceived}
              subtitle="Conversations started"
              color={theme.colors.success}
              trend="up"
            />
          </View>
        </View>

        {/* Weekly Performance */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>This Week</Text>
          <View style={styles.weeklyCard}>
            <View style={styles.weeklyHeader}>
              <Eye size={20} color={theme.colors.primary} />
              <Text style={styles.weeklyTitle}>Profile Views</Text>
            </View>
            <Text style={styles.weeklyValue}>{analytics.profileViewsThisWeek}</Text>
            <Text style={styles.weeklySubtitle}>
              {analytics.profileViewsThisWeek > 20 ? 'Great visibility!' : 'Keep your profile active'}
            </Text>
          </View>
        </View>

        {/* Profile Completion */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Profile Strength</Text>
          <View style={styles.completionCard}>
            <View style={styles.completionHeader}>
              <Text style={styles.completionTitle}>Profile Completion</Text>
              <Text style={styles.completionPercentage}>{profile?.profileCompletion || 0}%</Text>
            </View>
            <View style={styles.completionBar}>
              <View 
                style={[
                  styles.completionFill, 
                  { width: `${profile?.profileCompletion || 0}%` }
                ]} 
              />
            </View>
            <Text style={styles.completionSubtitle}>
              {(profile?.profileCompletion || 0) >= 80 
                ? 'Your profile looks great!' 
                : 'Complete your profile to get more matches'
              }
            </Text>
          </View>
        </View>

        {/* Photo Performance */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Photo Performance</Text>
          <View style={styles.photoCard}>
            <View style={styles.photoHeader}>
              <Camera size={20} color={theme.colors.primary} />
              <Text style={styles.photoTitle}>Most Popular Photos</Text>
            </View>
            <View style={styles.photoStats}>
              {analytics.popularPhotos.slice(0, 3).map((photoId, index) => (
                <View key={photoId} style={styles.photoRank}>
                  <Text style={styles.photoRankNumber}>#{index + 1}</Text>
                  <Text style={styles.photoRankLabel}>Photo {index + 1}</Text>
                </View>
              ))}
            </View>
          </View>
        </View>

        {/* Activity Insights */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Activity Insights</Text>
          
          <View style={styles.insightCard}>
            <View style={styles.insightHeader}>
              <Clock size={20} color={theme.colors.primary} />
              <Text style={styles.insightTitle}>Peak Activity Hours</Text>
            </View>
            <View style={styles.hoursContainer}>
              {analytics.peakActivityHours.map((hour) => (
                <View key={hour} style={styles.hourBadge}>
                  <Text style={styles.hourText}>{hour}:00</Text>
                </View>
              ))}
            </View>
            <Text style={styles.insightSubtitle}>
              Most people are active during these hours
            </Text>
          </View>

          <View style={styles.insightCard}>
            <View style={styles.insightHeader}>
              <MessageCircle size={20} color={theme.colors.success} />
              <Text style={styles.insightTitle}>Response Time</Text>
            </View>
            <Text style={styles.responseTime}>{analytics.averageResponseTime} minutes</Text>
            <Text style={styles.insightSubtitle}>
              Your average response time to messages
            </Text>
          </View>
        </View>

        {/* Engagement Breakdown */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Engagement Breakdown</Text>
          <View style={styles.engagementCard}>
            <ProgressBar
              label="Profile Views"
              value={analytics.profileViews}
              maxValue={200}
              color={theme.colors.primary}
            />
            <ProgressBar
              label="Likes Received"
              value={analytics.likesReceived}
              maxValue={50}
              color={theme.colors.secondary}
            />
            <ProgressBar
              label="Matches Made"
              value={analytics.matchesCount}
              maxValue={20}
              color={theme.colors.accent}
            />
            <ProgressBar
              label="Messages Received"
              value={analytics.messagesReceived}
              maxValue={100}
              color={theme.colors.success}
            />
          </View>
        </View>

        {/* Tips Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Tips to Improve</Text>
          <View style={styles.tipsCard}>
            <View style={styles.tip}>
              <Text style={styles.tipTitle}>📸 Add more photos</Text>
              <Text style={styles.tipText}>
                Profiles with 4+ photos get 3x more matches
              </Text>
            </View>
            <View style={styles.tip}>
              <Text style={styles.tipTitle}>✍️ Update your bio</Text>
              <Text style={styles.tipText}>
                A detailed bio increases profile views by 40%
              </Text>
            </View>
            <View style={styles.tip}>
              <Text style={styles.tipTitle}>⚡ Stay active</Text>
              <Text style={styles.tipText}>
                Active users get shown to more people
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray200,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
  },
  placeholder: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.text,
    fontFamily: 'Inter-Regular',
  },
  section: {
    marginHorizontal: 20,
    marginVertical: 10,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: theme.colors.text,
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  statCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 16,
    width: (width - 52) / 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  statIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  statContent: {
    flex: 1,
  },
  statValue: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: theme.colors.text,
    marginBottom: 4,
  },
  statTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.gray700,
    marginBottom: 2,
  },
  statSubtitle: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray500,
  },
  trendContainer: {
    position: 'absolute',
    top: 16,
    right: 16,
  },
  weeklyCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  weeklyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  weeklyTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
  },
  weeklyValue: {
    fontSize: 32,
    fontFamily: 'Inter-Bold',
    color: theme.colors.primary,
    marginBottom: 4,
  },
  weeklySubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
  },
  completionCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  completionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  completionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
  },
  completionPercentage: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: theme.colors.primary,
  },
  completionBar: {
    height: 8,
    backgroundColor: theme.colors.gray200,
    borderRadius: 4,
    marginBottom: 8,
  },
  completionFill: {
    height: '100%',
    backgroundColor: theme.colors.primary,
    borderRadius: 4,
  },
  completionSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
  },
  photoCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  photoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  photoTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
  },
  photoStats: {
    flexDirection: 'row',
    gap: 16,
  },
  photoRank: {
    alignItems: 'center',
  },
  photoRankNumber: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: theme.colors.primary,
    marginBottom: 4,
  },
  photoRankLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
  },
  insightCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  insightHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  insightTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
  },
  hoursContainer: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 8,
  },
  hourBadge: {
    backgroundColor: theme.colors.primary + '20',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  hourText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.primary,
  },
  responseTime: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: theme.colors.success,
    marginBottom: 4,
  },
  insightSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
  },
  engagementCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  progressContainer: {
    marginBottom: 16,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: theme.colors.text,
  },
  progressValue: {
    fontSize: 14,
    fontFamily: 'Inter-Bold',
    color: theme.colors.text,
  },
  progressBar: {
    height: 6,
    backgroundColor: theme.colors.gray200,
    borderRadius: 3,
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  tipsCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  tip: {
    marginBottom: 16,
  },
  tipTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 4,
  },
  tipText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
    lineHeight: 20,
  },
});
