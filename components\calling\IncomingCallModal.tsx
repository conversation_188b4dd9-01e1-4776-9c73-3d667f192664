import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Modal,
  Dimensions,
  Animated,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Phone, PhoneOff, Video, MessageCircle } from 'lucide-react-native';
import { CallSession, User } from '@/types/messaging';

interface IncomingCallModalProps {
  visible: boolean;
  callSession: CallSession;
  caller: User;
  onAccept: () => void;
  onDecline: () => void;
  onSendMessage: () => void;
}

const { width, height } = Dimensions.get('window');

export default function IncomingCallModal({
  visible,
  callSession,
  caller,
  onAccept,
  onDecline,
  onSendMessage,
}: IncomingCallModalProps) {
  const [pulseAnim] = useState(new Animated.Value(1));

  useEffect(() => {
    if (visible) {
      const pulse = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      );
      pulse.start();

      return () => pulse.stop();
    }
  }, [visible, pulseAnim]);

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
    >
      <LinearGradient colors={['#8B5CF6', '#EC4899']} style={styles.container}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.callType}>
              Incoming {callSession.type} call
            </Text>
          </View>

          <View style={styles.callerInfo}>
            <Animated.View
              style={[
                styles.avatarContainer,
                { transform: [{ scale: pulseAnim }] }
              ]}
            >
              <Image source={{ uri: caller.avatar }} style={styles.avatar} />
            </Animated.View>
            
            <Text style={styles.callerName}>{caller.name}</Text>
            <Text style={styles.callerStatus}>
              {caller.isOnline ? 'Online' : 'Last seen recently'}
            </Text>
          </View>

          <View style={styles.actions}>
            {/* Quick Message */}
            <TouchableOpacity style={styles.messageButton} onPress={onSendMessage}>
              <MessageCircle size={24} color="white" />
            </TouchableOpacity>

            {/* Decline */}
            <TouchableOpacity style={styles.declineButton} onPress={onDecline}>
              <PhoneOff size={32} color="white" />
            </TouchableOpacity>

            {/* Accept */}
            <TouchableOpacity style={styles.acceptButton} onPress={onAccept}>
              {callSession.type === 'video' ? (
                <Video size={32} color="white" />
              ) : (
                <Phone size={32} color="white" />
              )}
            </TouchableOpacity>
          </View>

          <View style={styles.quickResponses}>
            <Text style={styles.quickResponsesTitle}>Quick responses</Text>
            <TouchableOpacity style={styles.quickResponse} onPress={onSendMessage}>
              <Text style={styles.quickResponseText}>Can't talk right now</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.quickResponse} onPress={onSendMessage}>
              <Text style={styles.quickResponseText}>I'll call you back</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.quickResponse} onPress={onSendMessage}>
              <Text style={styles.quickResponseText}>What's up?</Text>
            </TouchableOpacity>
          </View>
        </View>
      </LinearGradient>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'space-between',
    paddingVertical: 60,
    paddingHorizontal: 32,
  },
  header: {
    alignItems: 'center',
  },
  callType: {
    fontSize: 18,
    fontFamily: 'Inter-Medium',
    color: 'rgba(255, 255, 255, 0.9)',
  },
  callerInfo: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  avatarContainer: {
    marginBottom: 24,
  },
  avatar: {
    width: 160,
    height: 160,
    borderRadius: 80,
    borderWidth: 4,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  callerName: {
    fontSize: 32,
    fontFamily: 'Poppins-Bold',
    color: 'white',
    marginBottom: 8,
    textAlign: 'center',
  },
  callerStatus: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 40,
  },
  messageButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  declineButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#EF4444',
    justifyContent: 'center',
    alignItems: 'center',
  },
  acceptButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#10B981',
    justifyContent: 'center',
    alignItems: 'center',
  },
  quickResponses: {
    alignItems: 'center',
  },
  quickResponsesTitle: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 16,
  },
  quickResponse: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
    paddingHorizontal: 20,
    paddingVertical: 10,
    marginBottom: 8,
  },
  quickResponseText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: 'white',
  },
});