import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Switch,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { 
  Crown, 
  TestTube, 
  CheckCircle, 
  XCircle, 
  Play, 
  Pause,
  RotateCcw,
  Settings
} from 'lucide-react-native';
import { usePremiumStore } from '@/stores/premiumStore';
import { useLikesStore } from '@/stores/likesStore';
import { useMessagesStore } from '@/stores/messagesStore';
import { useProfileStore } from '@/stores/profileStore';
import { theme } from '@/constants/theme';
import { DEFAULT_SUBSCRIPTION_PLANS, PREMIUM_FEATURES } from '@/types/premium';

interface TestResult {
  name: string;
  status: 'pass' | 'fail' | 'pending';
  message: string;
  timestamp: Date;
}

export default function PremiumTestingSuite() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [autoRun, setAutoRun] = useState(false);

  const premiumStore = usePremiumStore();
  const likesStore = useLikesStore();
  const messagesStore = useMessagesStore();
  const profileStore = useProfileStore();

  const addTestResult = (name: string, status: 'pass' | 'fail', message: string) => {
    setTestResults(prev => [...prev, {
      name,
      status,
      message,
      timestamp: new Date(),
    }]);
  };

  const runTest = async (testName: string, testFn: () => Promise<boolean> | boolean) => {
    try {
      const result = await testFn();
      addTestResult(testName, result ? 'pass' : 'fail', result ? 'Test passed' : 'Test failed');
      return result;
    } catch (error) {
      addTestResult(testName, 'fail', error instanceof Error ? error.message : 'Unknown error');
      return false;
    }
  };

  const testPremiumStoreBasics = async () => {
    return runTest('Premium Store Initialization', () => {
      return premiumStore.availablePlans.length > 0 && 
             DEFAULT_SUBSCRIPTION_PLANS.length > 0;
    });
  };

  const testSubscriptionFlow = async () => {
    return runTest('Subscription Flow', async () => {
      // Test plan selection
      premiumStore.selectPlan('3_months');
      if (premiumStore.selectedPlan !== '3_months') return false;

      // Test subscription (demo mode)
      const success = await premiumStore.subscribe('3_months');
      return success && premiumStore.isPremium;
    });
  };

  const testFeatureAccess = async () => {
    return runTest('Feature Access Control', () => {
      // Test premium feature access
      const hasUnlimitedLikes = premiumStore.hasFeature('unlimited_likes');
      const hasSeeWhoLikes = premiumStore.hasFeature('see_who_likes_you');
      
      if (premiumStore.isPremium) {
        return hasUnlimitedLikes && hasSeeWhoLikes;
      } else {
        return !hasUnlimitedLikes && !hasSeeWhoLikes;
      }
    });
  };

  const testLikesIntegration = async () => {
    return runTest('Likes Store Integration', () => {
      const canSendLike = likesStore.canSendLike();
      const canSendSuperLike = likesStore.canSendSuperLike();
      const canViewLikes = likesStore.canViewWhoLikesYou();

      // Test that premium status affects like limits
      if (premiumStore.isPremium) {
        return canSendLike.canSend && canViewLikes;
      } else {
        // Free users should have limits
        return typeof canSendLike.canSend === 'boolean';
      }
    });
  };

  const testMessagesIntegration = async () => {
    return runTest('Messages Store Integration', () => {
      const canSendPriority = messagesStore.canSendPriorityMessage();
      const canSeeReceipts = messagesStore.canSeeReadReceipts();

      if (premiumStore.isPremium) {
        return canSendPriority && canSeeReceipts;
      } else {
        return !canSendPriority && !canSeeReceipts;
      }
    });
  };

  const testProfileIntegration = async () => {
    return runTest('Profile Store Integration', () => {
      profileStore.updatePremiumStatus();
      const badgeInfo = profileStore.getPremiumBadgeInfo();
      
      return badgeInfo.isPremium === premiumStore.isPremium;
    });
  };

  const testUsageTracking = async () => {
    return runTest('Usage Tracking', () => {
      const initialUsage = premiumStore.usage;
      
      // Simulate feature usage
      premiumStore.incrementUsage('unlimited_likes');
      premiumStore.incrementUsage('super_likes');
      
      const updatedUsage = premiumStore.usage;
      return updatedUsage !== null && 
             updatedUsage.likesUsed > (initialUsage?.likesUsed || 0);
    });
  };

  const testSubscriptionCancellation = async () => {
    return runTest('Subscription Cancellation', async () => {
      if (!premiumStore.isPremium) return true; // Skip if not premium
      
      const success = await premiumStore.cancelSubscription();
      return success && premiumStore.subscription?.status === 'cancelled';
    });
  };

  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults([]);

    const tests = [
      testPremiumStoreBasics,
      testSubscriptionFlow,
      testFeatureAccess,
      testLikesIntegration,
      testMessagesIntegration,
      testProfileIntegration,
      testUsageTracking,
      testSubscriptionCancellation,
    ];

    for (const test of tests) {
      await test();
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    setIsRunning(false);
  };

  const resetTests = () => {
    setTestResults([]);
    premiumStore.reset();
  };

  const getTestStatusIcon = (status: 'pass' | 'fail' | 'pending') => {
    switch (status) {
      case 'pass':
        return <CheckCircle size={16} color={theme.colors.success} />;
      case 'fail':
        return <XCircle size={16} color={theme.colors.error} />;
      default:
        return <TestTube size={16} color={theme.colors.gray400} />;
    }
  };

  const passedTests = testResults.filter(r => r.status === 'pass').length;
  const totalTests = testResults.length;
  const successRate = totalTests > 0 ? (passedTests / totalTests) * 100 : 0;

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#8B5CF6', '#EC4899']}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TestTube size={24} color="white" />
          <Text style={styles.headerTitle}>Premium Testing Suite</Text>
          <Crown size={24} color="#FFD700" />
        </View>
        
        {totalTests > 0 && (
          <View style={styles.statsContainer}>
            <Text style={styles.statsText}>
              {passedTests}/{totalTests} tests passed ({successRate.toFixed(1)}%)
            </Text>
          </View>
        )}
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Controls */}
        <View style={styles.controlsSection}>
          <TouchableOpacity
            style={[styles.controlButton, styles.primaryButton]}
            onPress={runAllTests}
            disabled={isRunning}
          >
            {isRunning ? (
              <Pause size={20} color="white" />
            ) : (
              <Play size={20} color="white" />
            )}
            <Text style={styles.controlButtonText}>
              {isRunning ? 'Running...' : 'Run All Tests'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.controlButton, styles.secondaryButton]}
            onPress={resetTests}
            disabled={isRunning}
          >
            <RotateCcw size={20} color={theme.colors.primary} />
            <Text style={styles.secondaryButtonText}>Reset</Text>
          </TouchableOpacity>
        </View>

        {/* Auto-run Toggle */}
        <View style={styles.settingRow}>
          <View style={styles.settingInfo}>
            <Settings size={16} color={theme.colors.gray600} />
            <Text style={styles.settingLabel}>Auto-run on changes</Text>
          </View>
          <Switch
            value={autoRun}
            onValueChange={setAutoRun}
            trackColor={{ false: theme.colors.gray300, true: theme.colors.primary }}
          />
        </View>

        {/* Test Results */}
        <View style={styles.resultsSection}>
          <Text style={styles.sectionTitle}>Test Results</Text>
          
          {testResults.length === 0 ? (
            <View style={styles.emptyState}>
              <TestTube size={48} color={theme.colors.gray400} />
              <Text style={styles.emptyStateText}>No tests run yet</Text>
              <Text style={styles.emptyStateSubtext}>
                Tap "Run All Tests" to start testing premium features
              </Text>
            </View>
          ) : (
            <View style={styles.testsList}>
              {testResults.map((result, index) => (
                <View key={index} style={styles.testResult}>
                  <View style={styles.testResultHeader}>
                    {getTestStatusIcon(result.status)}
                    <Text style={styles.testName}>{result.name}</Text>
                    <Text style={styles.testTime}>
                      {result.timestamp.toLocaleTimeString()}
                    </Text>
                  </View>
                  <Text style={[
                    styles.testMessage,
                    result.status === 'fail' && styles.errorMessage
                  ]}>
                    {result.message}
                  </Text>
                </View>
              ))}
            </View>
          )}
        </View>

        {/* Premium Status */}
        <View style={styles.statusSection}>
          <Text style={styles.sectionTitle}>Current Premium Status</Text>
          <View style={styles.statusCard}>
            <View style={styles.statusRow}>
              <Text style={styles.statusLabel}>Premium Active:</Text>
              <Text style={[
                styles.statusValue,
                premiumStore.isPremium ? styles.successText : styles.errorText
              ]}>
                {premiumStore.isPremium ? 'Yes' : 'No'}
              </Text>
            </View>
            
            <View style={styles.statusRow}>
              <Text style={styles.statusLabel}>Selected Plan:</Text>
              <Text style={styles.statusValue}>
                {premiumStore.selectedPlan || 'None'}
              </Text>
            </View>
            
            <View style={styles.statusRow}>
              <Text style={styles.statusLabel}>Features Count:</Text>
              <Text style={styles.statusValue}>
                {premiumStore.premiumFeatures.length}/{PREMIUM_FEATURES.length}
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    paddingVertical: theme.spacing.xl,
    paddingHorizontal: theme.spacing.lg,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: theme.spacing.md,
  },
  headerTitle: {
    fontSize: theme.fontSize.xl,
    fontWeight: theme.fontWeight.bold,
    color: 'white',
  },
  statsContainer: {
    alignItems: 'center',
  },
  statsText: {
    fontSize: theme.fontSize.sm,
    color: 'rgba(255, 255, 255, 0.9)',
  },
  content: {
    flex: 1,
    padding: theme.spacing.lg,
  },
  controlsSection: {
    flexDirection: 'row',
    gap: theme.spacing.md,
    marginBottom: theme.spacing.lg,
  },
  controlButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    gap: theme.spacing.sm,
  },
  primaryButton: {
    backgroundColor: theme.colors.primary,
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: theme.colors.primary,
  },
  controlButtonText: {
    fontSize: theme.fontSize.base,
    fontWeight: theme.fontWeight.medium,
    color: 'white',
  },
  secondaryButtonText: {
    fontSize: theme.fontSize.base,
    fontWeight: theme.fontWeight.medium,
    color: theme.colors.primary,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.lg,
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.lg,
    marginBottom: theme.spacing.lg,
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  settingLabel: {
    fontSize: theme.fontSize.base,
    color: theme.colors.text,
  },
  resultsSection: {
    marginBottom: theme.spacing.xl,
  },
  sectionTitle: {
    fontSize: theme.fontSize.lg,
    fontWeight: theme.fontWeight.bold,
    color: theme.colors.text,
    marginBottom: theme.spacing.md,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: theme.spacing.xxl,
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.lg,
  },
  emptyStateText: {
    fontSize: theme.fontSize.lg,
    fontWeight: theme.fontWeight.medium,
    color: theme.colors.gray600,
    marginTop: theme.spacing.md,
  },
  emptyStateSubtext: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.gray500,
    textAlign: 'center',
    marginTop: theme.spacing.xs,
  },
  testsList: {
    gap: theme.spacing.sm,
  },
  testResult: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.gray200,
  },
  testResultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
    marginBottom: theme.spacing.xs,
  },
  testName: {
    flex: 1,
    fontSize: theme.fontSize.base,
    fontWeight: theme.fontWeight.medium,
    color: theme.colors.text,
  },
  testTime: {
    fontSize: theme.fontSize.xs,
    color: theme.colors.gray500,
  },
  testMessage: {
    fontSize: theme.fontSize.sm,
    color: theme.colors.gray600,
    marginLeft: theme.spacing.lg,
  },
  errorMessage: {
    color: theme.colors.error,
  },
  statusSection: {
    marginBottom: theme.spacing.xl,
  },
  statusCard: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.lg,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: theme.spacing.sm,
  },
  statusLabel: {
    fontSize: theme.fontSize.base,
    color: theme.colors.gray600,
  },
  statusValue: {
    fontSize: theme.fontSize.base,
    fontWeight: theme.fontWeight.medium,
    color: theme.colors.text,
  },
  successText: {
    color: theme.colors.success,
  },
  errorText: {
    color: theme.colors.error,
  },
});
