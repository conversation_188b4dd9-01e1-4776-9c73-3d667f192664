import AsyncStorage from '@react-native-async-storage/async-storage';
import { LikeProfile, Like, Match } from '@/types/messaging';

interface CachedLikesData {
  receivedLikes: LikeProfile[];
  sentLikes: Like[];
  matches: Match[];
  lastUpdated: number;
  version: string;
}

interface PendingAction {
  id: string;
  type: 'like' | 'superlike' | 'pass' | 'likeBack';
  userId: string;
  timestamp: number;
  data?: any;
}

export class LikesCacheService {
  private static instance: LikesCacheService;
  private readonly CACHE_KEY = 'likes_cache';
  private readonly PENDING_ACTIONS_KEY = 'pending_likes_actions';
  private readonly CACHE_VERSION = '1.0.0';
  private readonly CACHE_EXPIRY = 24 * 60 * 60 * 1000; // 24 hours

  private constructor() {}

  public static getInstance(): LikesCacheService {
    if (!LikesCacheService.instance) {
      LikesCacheService.instance = new LikesCacheService();
    }
    return LikesCacheService.instance;
  }

  // Cache likes data
  public async cacheLikesData(data: {
    receivedLikes: LikeProfile[];
    sentLikes: Like[];
    matches: Match[];
  }): Promise<void> {
    try {
      const cacheData: CachedLikesData = {
        ...data,
        lastUpdated: Date.now(),
        version: this.CACHE_VERSION,
      };

      await AsyncStorage.setItem(this.CACHE_KEY, JSON.stringify(cacheData));
      console.log('Likes data cached successfully');
    } catch (error) {
      console.error('Failed to cache likes data:', error);
    }
  }

  // Get cached likes data
  public async getCachedLikesData(): Promise<CachedLikesData | null> {
    try {
      const cachedData = await AsyncStorage.getItem(this.CACHE_KEY);
      if (!cachedData) return null;

      const parsedData: CachedLikesData = JSON.parse(cachedData);
      
      // Check if cache is expired
      if (this.isCacheExpired(parsedData.lastUpdated)) {
        console.log('Cache expired, removing old data');
        await this.clearCache();
        return null;
      }

      // Check version compatibility
      if (parsedData.version !== this.CACHE_VERSION) {
        console.log('Cache version mismatch, clearing cache');
        await this.clearCache();
        return null;
      }

      return parsedData;
    } catch (error) {
      console.error('Failed to get cached likes data:', error);
      return null;
    }
  }

  // Check if cache is expired
  private isCacheExpired(lastUpdated: number): boolean {
    return Date.now() - lastUpdated > this.CACHE_EXPIRY;
  }

  // Clear cache
  public async clearCache(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.CACHE_KEY);
      console.log('Likes cache cleared');
    } catch (error) {
      console.error('Failed to clear likes cache:', error);
    }
  }

  // Add pending action for offline support
  public async addPendingAction(action: Omit<PendingAction, 'id' | 'timestamp'>): Promise<void> {
    try {
      const pendingActions = await this.getPendingActions();
      
      const newAction: PendingAction = {
        ...action,
        id: `${action.type}_${action.userId}_${Date.now()}`,
        timestamp: Date.now(),
      };

      pendingActions.push(newAction);
      
      await AsyncStorage.setItem(this.PENDING_ACTIONS_KEY, JSON.stringify(pendingActions));
      console.log('Pending action added:', newAction.type, newAction.userId);
    } catch (error) {
      console.error('Failed to add pending action:', error);
    }
  }

  // Get pending actions
  public async getPendingActions(): Promise<PendingAction[]> {
    try {
      const actionsData = await AsyncStorage.getItem(this.PENDING_ACTIONS_KEY);
      return actionsData ? JSON.parse(actionsData) : [];
    } catch (error) {
      console.error('Failed to get pending actions:', error);
      return [];
    }
  }

  // Remove pending action
  public async removePendingAction(actionId: string): Promise<void> {
    try {
      const pendingActions = await this.getPendingActions();
      const filteredActions = pendingActions.filter(action => action.id !== actionId);
      
      await AsyncStorage.setItem(this.PENDING_ACTIONS_KEY, JSON.stringify(filteredActions));
      console.log('Pending action removed:', actionId);
    } catch (error) {
      console.error('Failed to remove pending action:', error);
    }
  }

  // Clear all pending actions
  public async clearPendingActions(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.PENDING_ACTIONS_KEY);
      console.log('All pending actions cleared');
    } catch (error) {
      console.error('Failed to clear pending actions:', error);
    }
  }

  // Update specific like in cache
  public async updateLikeInCache(likeId: string, updates: Partial<LikeProfile>): Promise<void> {
    try {
      const cachedData = await this.getCachedLikesData();
      if (!cachedData) return;

      const updatedLikes = cachedData.receivedLikes.map(like =>
        like.id === likeId ? { ...like, ...updates } : like
      );

      await this.cacheLikesData({
        ...cachedData,
        receivedLikes: updatedLikes,
      });

      console.log('Like updated in cache:', likeId);
    } catch (error) {
      console.error('Failed to update like in cache:', error);
    }
  }

  // Add new like to cache
  public async addLikeToCache(like: LikeProfile): Promise<void> {
    try {
      const cachedData = await this.getCachedLikesData();
      if (!cachedData) return;

      // Check if like already exists
      const existingLikeIndex = cachedData.receivedLikes.findIndex(l => l.id === like.id);
      
      if (existingLikeIndex >= 0) {
        // Update existing like
        cachedData.receivedLikes[existingLikeIndex] = like;
      } else {
        // Add new like to the beginning
        cachedData.receivedLikes.unshift(like);
      }

      await this.cacheLikesData(cachedData);
      console.log('Like added to cache:', like.id);
    } catch (error) {
      console.error('Failed to add like to cache:', error);
    }
  }

  // Add new match to cache
  public async addMatchToCache(match: Match): Promise<void> {
    try {
      const cachedData = await this.getCachedLikesData();
      if (!cachedData) return;

      // Check if match already exists
      const existingMatchIndex = cachedData.matches.findIndex(m => m.id === match.id);
      
      if (existingMatchIndex >= 0) {
        // Update existing match
        cachedData.matches[existingMatchIndex] = match;
      } else {
        // Add new match to the beginning
        cachedData.matches.unshift(match);
      }

      await this.cacheLikesData(cachedData);
      console.log('Match added to cache:', match.id);
    } catch (error) {
      console.error('Failed to add match to cache:', error);
    }
  }

  // Get cache statistics
  public async getCacheStats(): Promise<{
    cacheSize: number;
    lastUpdated: number | null;
    pendingActionsCount: number;
    isExpired: boolean;
  }> {
    try {
      const cachedData = await this.getCachedLikesData();
      const pendingActions = await this.getPendingActions();
      
      const cacheSize = cachedData ? 
        (cachedData.receivedLikes.length + cachedData.sentLikes.length + cachedData.matches.length) : 0;
      
      return {
        cacheSize,
        lastUpdated: cachedData?.lastUpdated || null,
        pendingActionsCount: pendingActions.length,
        isExpired: cachedData ? this.isCacheExpired(cachedData.lastUpdated) : true,
      };
    } catch (error) {
      console.error('Failed to get cache stats:', error);
      return {
        cacheSize: 0,
        lastUpdated: null,
        pendingActionsCount: 0,
        isExpired: true,
      };
    }
  }

  // Sync pending actions when back online
  public async syncPendingActions(
    onSyncAction: (action: PendingAction) => Promise<boolean>
  ): Promise<{ synced: number; failed: number }> {
    let synced = 0;
    let failed = 0;

    try {
      const pendingActions = await this.getPendingActions();
      console.log(`Syncing ${pendingActions.length} pending actions`);

      for (const action of pendingActions) {
        try {
          const success = await onSyncAction(action);
          if (success) {
            await this.removePendingAction(action.id);
            synced++;
          } else {
            failed++;
          }
        } catch (error) {
          console.error('Failed to sync action:', action.id, error);
          failed++;
        }
      }

      console.log(`Sync completed: ${synced} synced, ${failed} failed`);
    } catch (error) {
      console.error('Failed to sync pending actions:', error);
    }

    return { synced, failed };
  }
}

// Export singleton instance
export const likesCacheService = LikesCacheService.getInstance();
