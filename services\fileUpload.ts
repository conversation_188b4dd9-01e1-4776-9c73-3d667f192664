import { FileUpload } from '@/types/messaging';

export class FileUploadService {
  private uploads: Map<string, FileUpload> = new Map();
  private listeners: Map<string, Function[]> = new Map();

  public async uploadFile(file: File, conversationId: string): Promise<string> {
    const uploadId = this.generateId();
    const maxSize = 50 * 1024 * 1024; // 50MB limit

    if (file.size > maxSize) {
      throw new Error('File size exceeds 50MB limit');
    }

    const upload: FileUpload = {
      id: uploadId,
      file,
      progress: 0,
      status: 'uploading'
    };

    this.uploads.set(uploadId, upload);
    this.emit('uploadStarted', upload);

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('conversationId', conversationId);

      const xhr = new XMLHttpRequest();

      return new Promise((resolve, reject) => {
        xhr.upload.onprogress = (event) => {
          if (event.lengthComputable) {
            const progress = (event.loaded / event.total) * 100;
            upload.progress = progress;
            this.emit('uploadProgress', upload);
          }
        };

        xhr.onload = () => {
          if (xhr.status === 200) {
            const response = JSON.parse(xhr.responseText);
            upload.status = 'completed';
            this.emit('uploadCompleted', upload);
            this.uploads.delete(uploadId);
            resolve(response.fileUrl);
          } else {
            upload.status = 'failed';
            this.emit('uploadFailed', upload);
            reject(new Error('Upload failed'));
          }
        };

        xhr.onerror = () => {
          upload.status = 'failed';
          this.emit('uploadFailed', upload);
          reject(new Error('Upload failed'));
        };

        // In production, replace with your actual upload endpoint
        xhr.open('POST', '/api/upload');
        xhr.send(formData);
      });
    } catch (error) {
      upload.status = 'failed';
      this.emit('uploadFailed', upload);
      throw error;
    }
  }

  public cancelUpload(uploadId: string): void {
    const upload = this.uploads.get(uploadId);
    if (upload) {
      upload.status = 'failed';
      this.uploads.delete(uploadId);
      this.emit('uploadCancelled', upload);
    }
  }

  public getUpload(uploadId: string): FileUpload | undefined {
    return this.uploads.get(uploadId);
  }

  public getAllUploads(): FileUpload[] {
    return Array.from(this.uploads.values());
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  public on(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  }

  public off(event: string, callback: Function) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      const index = eventListeners.indexOf(callback);
      if (index > -1) {
        eventListeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data?: any) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(callback => callback(data));
    }
  }
}