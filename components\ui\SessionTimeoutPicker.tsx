import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Platform,
} from 'react-native';
import { Clock, Shield, AlertTriangle } from 'lucide-react-native';
import { theme } from '../../constants/theme';
import { triggerHaptic } from '../../utils/haptics';

interface SessionTimeoutPickerProps {
  initialTimeout?: number; // in minutes
  onTimeoutChange?: (timeout: number) => void;
  title?: string;
  disabled?: boolean;
}

export default function SessionTimeoutPicker({
  initialTimeout = 30,
  onTimeoutChange,
  title = 'Session Timeout',
  disabled = false,
}: SessionTimeoutPickerProps) {
  const [selectedTimeout, setSelectedTimeout] = useState(initialTimeout);

  const handleHaptic = () => {
    triggerHaptic.light();
  };

  const updateTimeout = (timeout: number) => {
    setSelectedTimeout(timeout);
    onTimeoutChange?.(timeout);
    handleHaptic();
  };

  const generateTimeouts = () => {
    return [
      { value: 5, label: '5 minutes', description: 'Very secure - frequent re-authentication' },
      { value: 10, label: '10 minutes', description: 'High security' },
      { value: 15, label: '15 minutes', description: 'Secure' },
      { value: 30, label: '30 minutes', description: 'Balanced security and convenience' },
      { value: 60, label: '1 hour', description: 'Convenient for longer sessions' },
      { value: 120, label: '2 hours', description: 'Extended sessions' },
      { value: 240, label: '4 hours', description: 'Long sessions' },
      { value: 480, label: '8 hours', description: 'All-day sessions' },
      { value: 0, label: 'Never', description: 'Stay logged in until manual logout' },
    ];
  };

  const formatTimeout = (timeout: number) => {
    if (timeout === 0) return 'Never';
    if (timeout < 60) return `${timeout} minutes`;
    const hours = Math.floor(timeout / 60);
    const minutes = timeout % 60;
    if (minutes === 0) return `${hours} hour${hours > 1 ? 's' : ''}`;
    return `${hours}h ${minutes}m`;
  };

  const getSecurityLevel = (timeout: number) => {
    if (timeout === 0) return { level: 'Low', color: theme.colors.error };
    if (timeout <= 15) return { level: 'High', color: theme.colors.success };
    if (timeout <= 60) return { level: 'Medium', color: theme.colors.warning };
    return { level: 'Low', color: theme.colors.error };
  };

  const timeouts = generateTimeouts();
  const securityLevel = getSecurityLevel(selectedTimeout);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{title}</Text>
      
      <View style={styles.currentTimeout}>
        <Clock size={24} color={theme.colors.primary} />
        <View style={styles.timeoutInfo}>
          <Text style={styles.currentTimeoutText}>
            {formatTimeout(selectedTimeout)}
          </Text>
          <View style={styles.securityInfo}>
            <Shield size={16} color={securityLevel.color} />
            <Text style={[styles.securityText, { color: securityLevel.color }]}>
              {securityLevel.level} Security
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Select Timeout Duration</Text>
        <ScrollView 
          style={styles.timeoutsScroll}
          contentContainerStyle={styles.timeoutsContainer}
          showsVerticalScrollIndicator={false}
        >
          {timeouts.map((timeout) => (
            <TouchableOpacity
              key={timeout.value}
              style={[
                styles.timeoutButton,
                selectedTimeout === timeout.value && styles.timeoutButtonSelected,
              ]}
              onPress={() => updateTimeout(timeout.value)}
              disabled={disabled}
            >
              <View style={styles.timeoutButtonContent}>
                <View style={styles.timeoutButtonLeft}>
                  <Text
                    style={[
                      styles.timeoutButtonText,
                      selectedTimeout === timeout.value && styles.timeoutButtonTextSelected,
                    ]}
                  >
                    {timeout.label}
                  </Text>
                  <Text
                    style={[
                      styles.timeoutButtonDescription,
                      selectedTimeout === timeout.value && styles.timeoutButtonDescriptionSelected,
                    ]}
                  >
                    {timeout.description}
                  </Text>
                </View>
                {selectedTimeout === timeout.value && (
                  <View style={styles.selectedIndicator} />
                )}
              </View>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      <View style={styles.infoBox}>
        <AlertTriangle size={16} color={theme.colors.warning} />
        <View style={styles.infoContent}>
          <Text style={styles.infoTitle}>Security Recommendation</Text>
          <Text style={styles.infoText}>
            For better security, choose a shorter timeout duration. This will automatically log you out after periods of inactivity.
          </Text>
        </View>
      </View>

      <View style={styles.explanationBox}>
        <Text style={styles.explanationTitle}>How Session Timeout Works</Text>
        <Text style={styles.explanationText}>
          • Your session will automatically expire after the selected period of inactivity{'\n'}
          • You'll need to log in again to continue using the app{'\n'}
          • This helps protect your account if you forget to log out{'\n'}
          • Choose "Never" only if you're the only user of this device
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: 16,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 16,
    textAlign: 'center',
  },
  currentTimeout: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    paddingVertical: 16,
    paddingHorizontal: 20,
    backgroundColor: `${theme.colors.primary}10`,
    borderRadius: 12,
  },
  timeoutInfo: {
    marginLeft: 12,
    flex: 1,
  },
  currentTimeoutText: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: theme.colors.primary,
    marginBottom: 4,
  },
  securityInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  securityText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    marginLeft: 6,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 12,
  },
  timeoutsScroll: {
    maxHeight: 400,
  },
  timeoutsContainer: {
    paddingVertical: 4,
  },
  timeoutButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginVertical: 2,
    borderRadius: 12,
    backgroundColor: theme.colors.gray50,
    borderWidth: 1,
    borderColor: theme.colors.gray100,
  },
  timeoutButtonSelected: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  timeoutButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  timeoutButtonLeft: {
    flex: 1,
  },
  timeoutButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 2,
  },
  timeoutButtonTextSelected: {
    color: 'white',
  },
  timeoutButtonDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
  },
  timeoutButtonDescriptionSelected: {
    color: 'rgba(255, 255, 255, 0.8)',
  },
  selectedIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'white',
  },
  infoBox: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 16,
    backgroundColor: `${theme.colors.warning}10`,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.warning,
    marginBottom: 16,
  },
  infoContent: {
    marginLeft: 12,
    flex: 1,
  },
  infoTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.warning,
    marginBottom: 4,
  },
  infoText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
    lineHeight: 20,
  },
  explanationBox: {
    padding: 16,
    backgroundColor: theme.colors.gray50,
    borderRadius: 12,
  },
  explanationTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 8,
  },
  explanationText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
    lineHeight: 20,
  },
});
