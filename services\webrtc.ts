import { CallSession, User } from '@/types/messaging';

export class WebRTCService {
  private peerConnection: RTCPeerConnection | null = null;
  private localStream: MediaStream | null = null;
  private remoteStream: MediaStream | null = null;
  private mediaRecorder: MediaRecorder | null = null;
  private recordedChunks: Blob[] = [];
  private listeners: Map<string, Function[]> = new Map();

  private configuration: RTCConfiguration = {
    iceServers: [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' },
    ]
  };

  constructor() {
    this.setupPeerConnection();
  }

  private setupPeerConnection() {
    this.peerConnection = new RTCPeerConnection(this.configuration);

    this.peerConnection.onicecandidate = (event) => {
      if (event.candidate) {
        this.emit('iceCandidate', event.candidate);
      }
    };

    this.peerConnection.ontrack = (event) => {
      this.remoteStream = event.streams[0];
      this.emit('remoteStream', this.remoteStream);
    };

    this.peerConnection.onconnectionstatechange = () => {
      this.emit('connectionStateChange', this.peerConnection?.connectionState);
    };
  }

  public async startCall(type: 'audio' | 'video'): Promise<void> {
    try {
      const constraints = {
        audio: true,
        video: type === 'video'
      };

      this.localStream = await navigator.mediaDevices.getUserMedia(constraints);
      this.emit('localStream', this.localStream);

      this.localStream.getTracks().forEach(track => {
        this.peerConnection?.addTrack(track, this.localStream!);
      });

      const offer = await this.peerConnection?.createOffer();
      await this.peerConnection?.setLocalDescription(offer);
      
      this.emit('offer', offer);
    } catch (error) {
      console.error('Error starting call:', error);
      this.emit('error', error);
    }
  }

  public async answerCall(offer: RTCSessionDescriptionInit): Promise<void> {
    try {
      await this.peerConnection?.setRemoteDescription(offer);

      const constraints = {
        audio: true,
        video: offer.type === 'offer' // Determine video based on offer
      };

      this.localStream = await navigator.mediaDevices.getUserMedia(constraints);
      this.emit('localStream', this.localStream);

      this.localStream.getTracks().forEach(track => {
        this.peerConnection?.addTrack(track, this.localStream!);
      });

      const answer = await this.peerConnection?.createAnswer();
      await this.peerConnection?.setLocalDescription(answer);
      
      this.emit('answer', answer);
    } catch (error) {
      console.error('Error answering call:', error);
      this.emit('error', error);
    }
  }

  public async handleAnswer(answer: RTCSessionDescriptionInit): Promise<void> {
    try {
      await this.peerConnection?.setRemoteDescription(answer);
    } catch (error) {
      console.error('Error handling answer:', error);
      this.emit('error', error);
    }
  }

  public async handleIceCandidate(candidate: RTCIceCandidateInit): Promise<void> {
    try {
      await this.peerConnection?.addIceCandidate(candidate);
    } catch (error) {
      console.error('Error handling ICE candidate:', error);
    }
  }

  public async toggleVideo(): Promise<boolean> {
    if (this.localStream) {
      const videoTrack = this.localStream.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !videoTrack.enabled;
        this.emit('videoToggled', videoTrack.enabled);
        return videoTrack.enabled;
      }
    }
    return false;
  }

  public async toggleAudio(): Promise<boolean> {
    if (this.localStream) {
      const audioTrack = this.localStream.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled;
        this.emit('audioToggled', audioTrack.enabled);
        return audioTrack.enabled;
      }
    }
    return false;
  }

  public async startScreenShare(): Promise<void> {
    try {
      const screenStream = await navigator.mediaDevices.getDisplayMedia({
        video: true,
        audio: true
      });

      const videoTrack = screenStream.getVideoTracks()[0];
      const sender = this.peerConnection?.getSenders().find(s => 
        s.track && s.track.kind === 'video'
      );

      if (sender) {
        await sender.replaceTrack(videoTrack);
      }

      videoTrack.onended = () => {
        this.stopScreenShare();
      };

      this.emit('screenShareStarted');
    } catch (error) {
      console.error('Error starting screen share:', error);
      this.emit('error', error);
    }
  }

  public async stopScreenShare(): Promise<void> {
    try {
      if (this.localStream) {
        const videoTrack = this.localStream.getVideoTracks()[0];
        const sender = this.peerConnection?.getSenders().find(s => 
          s.track && s.track.kind === 'video'
        );

        if (sender && videoTrack) {
          await sender.replaceTrack(videoTrack);
        }
      }

      this.emit('screenShareStopped');
    } catch (error) {
      console.error('Error stopping screen share:', error);
    }
  }

  public startRecording(): void {
    if (this.localStream && this.remoteStream) {
      try {
        // Combine local and remote streams for recording
        const combinedStream = new MediaStream([
          ...this.localStream.getTracks(),
          ...this.remoteStream.getTracks()
        ]);

        this.mediaRecorder = new MediaRecorder(combinedStream);
        this.recordedChunks = [];

        this.mediaRecorder.ondataavailable = (event) => {
          if (event.data.size > 0) {
            this.recordedChunks.push(event.data);
          }
        };

        this.mediaRecorder.onstop = () => {
          const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
          this.emit('recordingComplete', blob);
        };

        this.mediaRecorder.start();
        this.emit('recordingStarted');
      } catch (error) {
        console.error('Error starting recording:', error);
        this.emit('error', error);
      }
    }
  }

  public stopRecording(): void {
    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
      this.mediaRecorder.stop();
      this.emit('recordingStopped');
    }
  }

  public endCall(): void {
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
    }

    if (this.peerConnection) {
      this.peerConnection.close();
      this.setupPeerConnection();
    }

    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
      this.stopRecording();
    }

    this.remoteStream = null;
    this.emit('callEnded');
  }

  public on(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  }

  public off(event: string, callback: Function) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      const index = eventListeners.indexOf(callback);
      if (index > -1) {
        eventListeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data?: any) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(callback => callback(data));
    }
  }

  public cleanup() {
    this.endCall();
    this.listeners.clear();
  }
}