import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { Check, CheckCheck, Clock, Download, Play } from 'lucide-react-native';
import { Message } from '@/types/messaging';

interface MessageBubbleProps {
  message: Message;
  isOwn: boolean;
  showAvatar: boolean;
  isConsecutive: boolean;
  senderAvatar: string;
}

const { width } = Dimensions.get('window');
const MAX_BUBBLE_WIDTH = width * 0.75;

export default function MessageBubble({
  message,
  isOwn,
  showAvatar,
  isConsecutive,
  senderAvatar
}: MessageBubbleProps) {
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getStatusIcon = () => {
    switch (message.status) {
      case 'sending':
        return <Clock size={12} color="#9CA3AF" />;
      case 'sent':
        return <Check size={12} color="#9CA3AF" />;
      case 'delivered':
        return <CheckCheck size={12} color="#9CA3AF" />;
      case 'read':
        return <CheckCheck size={12} color="#8B5CF6" />;
      default:
        return null;
    }
  };

  const renderMessageContent = () => {
    switch (message.type) {
      case 'text':
        return (
          <Text style={[styles.messageText, isOwn && styles.ownMessageText]}>
            {message.content}
          </Text>
        );
      
      case 'image':
        return (
          <View style={styles.imageContainer}>
            <Image source={{ uri: message.fileUrl }} style={styles.messageImage} />
            {message.content && (
              <Text style={[styles.messageText, isOwn && styles.ownMessageText, styles.imageCaption]}>
                {message.content}
              </Text>
            )}
          </View>
        );
      
      case 'file':
        return (
          <View style={styles.fileContainer}>
            <View style={styles.fileIcon}>
              <Download size={20} color={isOwn ? 'white' : '#8B5CF6'} />
            </View>
            <View style={styles.fileInfo}>
              <Text style={[styles.fileName, isOwn && styles.ownMessageText]}>
                {message.fileName}
              </Text>
              <Text style={[styles.fileSize, isOwn && styles.ownFileSize]}>
                {formatFileSize(message.fileSize || 0)}
              </Text>
            </View>
          </View>
        );
      
      case 'voice':
        return (
          <View style={styles.voiceContainer}>
            <TouchableOpacity style={styles.playButton}>
              <Play size={16} color={isOwn ? 'white' : '#8B5CF6'} />
            </TouchableOpacity>
            <View style={styles.waveform}>
              {/* Waveform visualization would go here */}
              <View style={styles.waveformBars}>
                {Array.from({ length: 20 }, (_, i) => (
                  <View 
                    key={i} 
                    style={[
                      styles.waveformBar,
                      { height: Math.random() * 20 + 5 },
                      isOwn && styles.ownWaveformBar
                    ]} 
                  />
                ))}
              </View>
            </View>
            <Text style={[styles.voiceDuration, isOwn && styles.ownMessageText]}>
              {formatDuration(message.duration || 0)}
            </Text>
          </View>
        );
      
      default:
        return (
          <Text style={[styles.messageText, isOwn && styles.ownMessageText]}>
            {message.content}
          </Text>
        );
    }
  };

  return (
    <View style={[
      styles.messageContainer,
      isOwn ? styles.ownMessageContainer : styles.otherMessageContainer,
      isConsecutive && styles.consecutiveMessage
    ]}>
      {!isOwn && showAvatar && (
        <Image source={{ uri: senderAvatar }} style={styles.avatar} />
      )}
      
      {!isOwn && !showAvatar && <View style={styles.avatarSpacer} />}
      
      <View style={[
        styles.messageBubble,
        isOwn ? styles.ownMessageBubble : styles.otherMessageBubble,
        isConsecutive && (isOwn ? styles.ownConsecutiveBubble : styles.otherConsecutiveBubble)
      ]}>
        {renderMessageContent()}
        
        <View style={styles.messageFooter}>
          <Text style={[styles.timestamp, isOwn && styles.ownTimestamp]}>
            {formatTime(message.timestamp)}
          </Text>
          {isOwn && (
            <View style={styles.statusIcon}>
              {getStatusIcon()}
            </View>
          )}
        </View>
      </View>
    </View>
  );
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const formatDuration = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins}:${secs.toString().padStart(2, '0')}`;
};

const styles = StyleSheet.create({
  messageContainer: {
    flexDirection: 'row',
    marginVertical: 2,
    paddingHorizontal: 4,
  },
  ownMessageContainer: {
    justifyContent: 'flex-end',
  },
  otherMessageContainer: {
    justifyContent: 'flex-start',
  },
  consecutiveMessage: {
    marginTop: 1,
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 8,
    alignSelf: 'flex-end',
  },
  avatarSpacer: {
    width: 40,
  },
  messageBubble: {
    maxWidth: MAX_BUBBLE_WIDTH,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 18,
  },
  ownMessageBubble: {
    backgroundColor: '#8B5CF6',
    borderBottomRightRadius: 4,
  },
  otherMessageBubble: {
    backgroundColor: '#F3F4F6',
    borderBottomLeftRadius: 4,
  },
  ownConsecutiveBubble: {
    borderBottomRightRadius: 18,
  },
  otherConsecutiveBubble: {
    borderBottomLeftRadius: 18,
  },
  messageText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#1F2937',
    lineHeight: 20,
  },
  ownMessageText: {
    color: 'white',
  },
  imageContainer: {
    overflow: 'hidden',
  },
  messageImage: {
    width: 200,
    height: 200,
    borderRadius: 12,
    marginBottom: 4,
  },
  imageCaption: {
    marginTop: 4,
  },
  fileContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    minWidth: 200,
  },
  fileIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  fileInfo: {
    flex: 1,
  },
  fileName: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 2,
  },
  fileSize: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  ownFileSize: {
    color: 'rgba(255, 255, 255, 0.8)',
  },
  voiceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    minWidth: 200,
  },
  playButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  waveform: {
    flex: 1,
    marginRight: 8,
  },
  waveformBars: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 20,
    gap: 1,
  },
  waveformBar: {
    width: 2,
    backgroundColor: '#6B7280',
    borderRadius: 1,
  },
  ownWaveformBar: {
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  voiceDuration: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  messageFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginTop: 4,
    gap: 4,
  },
  timestamp: {
    fontSize: 11,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
  },
  ownTimestamp: {
    color: 'rgba(255, 255, 255, 0.8)',
  },
  statusIcon: {
    marginLeft: 2,
  },
});