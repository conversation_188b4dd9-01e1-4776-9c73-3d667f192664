import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import ChatScreen from '@/components/messaging/ChatScreen';
import { Message, User, Conversation } from '@/types/messaging';

// Mock data - replace with real data from your backend
const CURRENT_USER: User = {
  id: 'current-user',
  name: 'You',
  avatar: 'https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=400',
  isOnline: true,
};

const MOCK_CONVERSATION: Conversation = {
  id: 'conv-1',
  participants: [
    CURRENT_USER,
    {
      id: 'user-1',
      name: '<PERSON>',
      avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400',
      isOnline: true,
    }
  ],
  lastMessage: {
    id: 'msg-1',
    senderId: 'user-1',
    receiverId: 'current-user',
    content: 'Hey! Thanks for the like 😊',
    type: 'text',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
    status: 'delivered',
  },
  unreadCount: 0,
  isTyping: false,
  typingUsers: [],
};

const MOCK_MESSAGES: Message[] = [
  {
    id: 'msg-1',
    senderId: 'user-1',
    receiverId: 'current-user',
    content: 'Hey! How are you doing?',
    type: 'text',
    timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000),
    status: 'read',
  },
  {
    id: 'msg-2',
    senderId: 'current-user',
    receiverId: 'user-1',
    content: 'I\'m doing great! Thanks for asking 😊',
    type: 'text',
    timestamp: new Date(Date.now() - 2.5 * 60 * 60 * 1000),
    status: 'read',
  },
  {
    id: 'msg-3',
    senderId: 'user-1',
    receiverId: 'current-user',
    content: 'That\'s awesome! Want to grab coffee this weekend?',
    type: 'text',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
    status: 'delivered',
  },
];

export default function ChatPage() {
  const { id } = useLocalSearchParams();
  const router = useRouter();

  const handleSendMessage = (content: string, type: 'text' | 'image' | 'file') => {
    // Implement message sending logic
    console.log('Sending message:', content, type);
  };

  const handleStartCall = (type: 'audio' | 'video') => {
    // Navigate to call screen or start call
    console.log('Starting call:', type);
  };

  const handleGoBack = () => {
    router.back();
  };

  const handleStartTyping = () => {
    console.log('Started typing');
  };

  const handleStopTyping = () => {
    console.log('Stopped typing');
  };

  const handleMarkAsRead = (messageId: string) => {
    console.log('Marking as read:', messageId);
  };

  return (
    <View style={styles.container}>
      <ChatScreen
        conversation={MOCK_CONVERSATION}
        messages={MOCK_MESSAGES}
        currentUser={CURRENT_USER}
        onSendMessage={handleSendMessage}
        onStartCall={handleStartCall}
        onGoBack={handleGoBack}
        onStartTyping={handleStartTyping}
        onStopTyping={handleStopTyping}
        onMarkAsRead={handleMarkAsRead}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});