import { Match } from '@/types/messaging';

// Mock data for matches with enhanced user information
const MOCK_MATCHES: (Match & {
  otherUser: {
    id: string;
    name: string;
    age: number;
    photo: string;
    lastMessage?: string;
    lastMessageTime?: string;
    unread?: boolean;
    isOnline?: boolean;
    bio?: string;
    interests?: string[];
  }
})[] = [
  {
    id: 'match_1',
    users: ['current-user', 'user_1'],
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    lastActivity: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
    status: 'active',
    otherUser: {
      id: 'user_1',
      name: '<PERSON>',
      age: 26,
      photo: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=400',
      lastMessage: 'Hey! Thanks for the like 😊',
      lastMessageTime: '30 minutes ago',
      unread: true,
      isOnline: true,
      bio: 'Love hiking and photography 📸',
      interests: ['Photography', 'Hiking', 'Travel'],
    },
  },
  {
    id: 'match_2',
    users: ['current-user', 'user_2'],
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
    lastActivity: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
    status: 'active',
    otherUser: {
      id: 'user_2',
      name: 'Emma',
      age: 24,
      photo: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=400',
      lastMessage: 'Would love to grab coffee sometime!',
      lastMessageTime: '4 hours ago',
      unread: false,
      isOnline: false,
      bio: 'Coffee enthusiast ☕ and book lover 📚',
      interests: ['Reading', 'Coffee', 'Yoga'],
    },
  },
  {
    id: 'match_3',
    users: ['current-user', 'user_3'],
    timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
    lastActivity: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    status: 'active',
    otherUser: {
      id: 'user_3',
      name: 'Isabella',
      age: 28,
      photo: 'https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=400',
      lastMessage: 'That hiking photo is amazing! 🏔️',
      lastMessageTime: '2 hours ago',
      unread: true,
      isOnline: true,
      bio: 'Adventure seeker 🏔️ and nature photographer',
      interests: ['Hiking', 'Photography', 'Nature'],
    },
  },
  {
    id: 'match_4',
    users: ['current-user', 'user_4'],
    timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
    lastActivity: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
    status: 'active',
    otherUser: {
      id: 'user_4',
      name: 'Olivia',
      age: 25,
      photo: 'https://images.pexels.com/photos/1542085/pexels-photo-1542085.jpeg?auto=compress&cs=tinysrgb&w=400',
      lastMessage: 'Your profile made me smile 😄',
      lastMessageTime: '1 day ago',
      unread: false,
      isOnline: false,
      bio: 'Artist 🎨 and music lover 🎵',
      interests: ['Art', 'Music', 'Dancing'],
    },
  },
  {
    id: 'match_5',
    users: ['current-user', 'user_5'],
    timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 1 week ago
    lastActivity: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
    status: 'active',
    otherUser: {
      id: 'user_5',
      name: 'Ava',
      age: 27,
      photo: 'https://images.pexels.com/photos/1858175/pexels-photo-1858175.jpeg?auto=compress&cs=tinysrgb&w=400',
      lastMessage: 'We have so much in common!',
      lastMessageTime: '6 hours ago',
      unread: true,
      isOnline: false,
      bio: 'Fitness enthusiast 💪 and healthy living advocate',
      interests: ['Fitness', 'Nutrition', 'Wellness'],
    },
  },
  {
    id: 'match_6',
    users: ['current-user', 'user_6'],
    timestamp: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
    lastActivity: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours ago
    status: 'active',
    otherUser: {
      id: 'user_6',
      name: 'Mia',
      age: 23,
      photo: 'https://images.pexels.com/photos/1040880/pexels-photo-1040880.jpeg?auto=compress&cs=tinysrgb&w=400',
      lastMessage: 'Looking forward to chatting more',
      lastMessageTime: '12 hours ago',
      unread: false,
      isOnline: true,
      bio: 'Tech enthusiast 💻 and startup founder',
      interests: ['Technology', 'Startups', 'Innovation'],
    },
  },
  {
    id: 'match_7',
    users: ['current-user', 'user_7'],
    timestamp: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000), // 2 weeks ago
    lastActivity: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
    status: 'active',
    otherUser: {
      id: 'user_7',
      name: 'Charlotte',
      age: 29,
      photo: 'https://images.pexels.com/photos/1181519/pexels-photo-1181519.jpeg?auto=compress&cs=tinysrgb&w=400',
      lastMessage: 'Great taste in music! 🎵',
      lastMessageTime: '3 days ago',
      unread: false,
      isOnline: false,
      bio: 'Music producer 🎧 and concert lover',
      interests: ['Music', 'Concerts', 'Production'],
    },
  },
  {
    id: 'match_8',
    users: ['current-user', 'user_8'],
    timestamp: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000), // 3 weeks ago
    lastActivity: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 1 week ago
    status: 'active',
    otherUser: {
      id: 'user_8',
      name: 'Amelia',
      age: 26,
      photo: 'https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=400',
      lastMessage: 'Hope you\'re having a great day!',
      lastMessageTime: '1 week ago',
      unread: false,
      isOnline: false,
      bio: 'Travel blogger ✈️ and culture enthusiast',
      interests: ['Travel', 'Culture', 'Writing'],
    },
  },
];

export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 400));
    
    // Calculate pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedMatches = MOCK_MATCHES.slice(startIndex, endIndex);
    const hasMore = endIndex < MOCK_MATCHES.length;
    
    return Response.json({
      success: true,
      matches: paginatedMatches,
      hasMore,
      total: MOCK_MATCHES.length,
      page,
      limit,
    });
  } catch (error) {
    console.error('Error fetching matches:', error);
    return Response.json(
      { 
        success: false, 
        error: 'Failed to fetch matches' 
      },
      { status: 500 }
    );
  }
}
