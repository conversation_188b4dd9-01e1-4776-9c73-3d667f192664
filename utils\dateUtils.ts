/**
 * Utility functions for handling dates in the dating app
 * Handles serialization/deserialization issues with Zustand persistence
 */

export type DateInput = Date | string | number | null | undefined;

/**
 * Safely converts various date inputs to a Date object
 * @param dateInput - Date, string, number, null, or undefined
 * @returns Valid Date object or null if conversion fails
 */
export function safeParseDate(dateInput: DateInput): Date | null {
  try {
    if (!dateInput) {
      return null;
    }

    let date: Date;
    
    if (dateInput instanceof Date) {
      date = dateInput;
    } else if (typeof dateInput === 'string') {
      date = new Date(dateInput);
    } else if (typeof dateInput === 'number') {
      date = new Date(dateInput);
    } else {
      console.warn('Unexpected date type:', typeof dateInput, dateInput);
      return null;
    }

    // Validate the Date object
    if (isNaN(date.getTime())) {
      console.warn('Invalid date:', dateInput);
      return null;
    }

    return date;
  } catch (error) {
    console.error('Error parsing date:', error, dateInput);
    return null;
  }
}

/**
 * Formats a date input as a relative time string (e.g., "2h ago", "3d ago")
 * @param dateInput - Date, string, number, null, or undefined
 * @param fallback - Fallback string if date parsing fails
 * @returns Formatted relative time string
 */
export function formatTimeAgo(dateInput: DateInput, fallback: string = 'Recently'): string {
  const date = safeParseDate(dateInput);
  
  if (!date) {
    return fallback;
  }

  const now = new Date();
  const diffInMs = now.getTime() - date.getTime();
  
  // Handle future dates (shouldn't happen, but just in case)
  if (diffInMs < 0) {
    return 'Just now';
  }
  
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
  
  if (diffInMinutes < 1) return 'Just now';
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
  if (diffInHours < 24) return `${diffInHours}h ago`;
  if (diffInDays < 7) return `${diffInDays}d ago`;
  if (diffInDays < 30) {
    const weeks = Math.floor(diffInDays / 7);
    return `${weeks}w ago`;
  }
  if (diffInDays < 365) {
    const months = Math.floor(diffInDays / 30);
    return `${months}mo ago`;
  }
  
  const years = Math.floor(diffInDays / 365);
  return `${years}y ago`;
}

/**
 * Formats a date input as a readable date string
 * @param dateInput - Date, string, number, null, or undefined
 * @param options - Intl.DateTimeFormatOptions for formatting
 * @param fallback - Fallback string if date parsing fails
 * @returns Formatted date string
 */
export function formatDate(
  dateInput: DateInput, 
  options: Intl.DateTimeFormatOptions = { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric' 
  },
  fallback: string = 'Unknown date'
): string {
  const date = safeParseDate(dateInput);
  
  if (!date) {
    return fallback;
  }

  try {
    return date.toLocaleDateString(undefined, options);
  } catch (error) {
    console.error('Error formatting date:', error, dateInput);
    return fallback;
  }
}

/**
 * Formats a date input as a time string
 * @param dateInput - Date, string, number, null, or undefined
 * @param options - Intl.DateTimeFormatOptions for formatting
 * @param fallback - Fallback string if date parsing fails
 * @returns Formatted time string
 */
export function formatTime(
  dateInput: DateInput,
  options: Intl.DateTimeFormatOptions = { 
    hour: '2-digit', 
    minute: '2-digit' 
  },
  fallback: string = 'Unknown time'
): string {
  const date = safeParseDate(dateInput);
  
  if (!date) {
    return fallback;
  }

  try {
    return date.toLocaleTimeString(undefined, options);
  } catch (error) {
    console.error('Error formatting time:', error, dateInput);
    return fallback;
  }
}

/**
 * Checks if a date input represents today
 * @param dateInput - Date, string, number, null, or undefined
 * @returns True if the date is today, false otherwise
 */
export function isToday(dateInput: DateInput): boolean {
  const date = safeParseDate(dateInput);
  
  if (!date) {
    return false;
  }

  const today = new Date();
  return date.toDateString() === today.toDateString();
}

/**
 * Checks if a date input represents yesterday
 * @param dateInput - Date, string, number, null, or undefined
 * @returns True if the date is yesterday, false otherwise
 */
export function isYesterday(dateInput: DateInput): boolean {
  const date = safeParseDate(dateInput);
  
  if (!date) {
    return false;
  }

  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  return date.toDateString() === yesterday.toDateString();
}

/**
 * Gets the number of days between two dates
 * @param date1 - First date
 * @param date2 - Second date (defaults to now)
 * @returns Number of days between the dates
 */
export function daysBetween(date1: DateInput, date2: DateInput = new Date()): number {
  const d1 = safeParseDate(date1);
  const d2 = safeParseDate(date2);
  
  if (!d1 || !d2) {
    return 0;
  }

  const diffInMs = Math.abs(d2.getTime() - d1.getTime());
  return Math.floor(diffInMs / (1000 * 60 * 60 * 24));
}
