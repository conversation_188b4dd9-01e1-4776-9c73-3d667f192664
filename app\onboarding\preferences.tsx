import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { ArrowR<PERSON>, ArrowLeft, Heart, Users, Coffee } from 'lucide-react-native';

const RELATIONSHIP_GOALS = [
  { id: 'serious', label: 'Serious Relationship', icon: Heart, description: 'Looking for something long-term' },
  { id: 'casual', label: 'Casual Dating', icon: Coffee, description: 'Open to see where things go' },
  { id: 'friends', label: 'New Friends', icon: Users, description: 'Looking to expand my social circle' },
];

const AGE_RANGES = [
  '18-25', '25-30', '30-35', '35-40', '40-45', '45-50', '50+'
];

export default function OnboardingPreferences() {
  const router = useRouter();
  const [relationshipGoal, setRelationshipGoal] = useState<string>('');
  const [minAge, setMinAge] = useState<string>('');
  const [maxAge, setMaxAge] = useState<string>('');
  const [maxDistance, setMaxDistance] = useState<number>(25);

  const handleContinue = () => {
    router.push('/onboarding/location');
  };

  return (
    <LinearGradient colors={['#8B5CF6', '#EC4899']} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <ArrowLeft size={24} color="white" />
            </TouchableOpacity>
            <Text style={styles.title}>Your preferences</Text>
            <Text style={styles.subtitle}>
              Help us understand what you're looking for
            </Text>
            <View style={styles.progressBar}>
              <View style={[styles.progressStep, styles.activeStep]} />
              <View style={[styles.progressStep, styles.activeStep]} />
              <View style={[styles.progressStep, styles.activeStep]} />
              <View style={[styles.progressStep, styles.activeStep]} />
              <View style={styles.progressStep} />
            </View>
          </View>

          <View style={styles.content}>
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>What are you looking for?</Text>
              <View style={styles.goalsContainer}>
                {RELATIONSHIP_GOALS.map((goal) => {
                  const IconComponent = goal.icon;
                  return (
                    <TouchableOpacity
                      key={goal.id}
                      style={[
                        styles.goalOption,
                        relationshipGoal === goal.id && styles.goalOptionSelected,
                      ]}
                      onPress={() => setRelationshipGoal(goal.id)}
                    >
                      <IconComponent 
                        size={24} 
                        color={relationshipGoal === goal.id ? '#8B5CF6' : 'rgba(255, 255, 255, 0.8)'} 
                      />
                      <View style={styles.goalContent}>
                        <Text
                          style={[
                            styles.goalLabel,
                            relationshipGoal === goal.id && styles.goalLabelSelected,
                          ]}
                        >
                          {goal.label}
                        </Text>
                        <Text
                          style={[
                            styles.goalDescription,
                            relationshipGoal === goal.id && styles.goalDescriptionSelected,
                          ]}
                        >
                          {goal.description}
                        </Text>
                      </View>
                    </TouchableOpacity>
                  );
                })}
              </View>
            </View>

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Age Range</Text>
              <View style={styles.ageContainer}>
                <View style={styles.ageGroup}>
                  <Text style={styles.ageLabel}>Minimum Age</Text>
                  <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.ageScroll}>
                    <View style={styles.ageOptions}>
                      {AGE_RANGES.map((age) => (
                        <TouchableOpacity
                          key={age}
                          style={[
                            styles.ageOption,
                            minAge === age && styles.ageOptionSelected,
                          ]}
                          onPress={() => setMinAge(age)}
                        >
                          <Text
                            style={[
                              styles.ageOptionText,
                              minAge === age && styles.ageOptionTextSelected,
                            ]}
                          >
                            {age}
                          </Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  </ScrollView>
                </View>

                <View style={styles.ageGroup}>
                  <Text style={styles.ageLabel}>Maximum Age</Text>
                  <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.ageScroll}>
                    <View style={styles.ageOptions}>
                      {AGE_RANGES.map((age) => (
                        <TouchableOpacity
                          key={age}
                          style={[
                            styles.ageOption,
                            maxAge === age && styles.ageOptionSelected,
                          ]}
                          onPress={() => setMaxAge(age)}
                        >
                          <Text
                            style={[
                              styles.ageOptionText,
                              maxAge === age && styles.ageOptionTextSelected,
                            ]}
                          >
                            {age}
                          </Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  </ScrollView>
                </View>
              </View>
            </View>

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Maximum Distance</Text>
              <View style={styles.distanceContainer}>
                <Text style={styles.distanceValue}>{maxDistance} miles</Text>
                <View style={styles.distanceSlider}>
                  <View style={styles.sliderTrack}>
                    <View 
                      style={[
                        styles.sliderFill,
                        { width: `${(maxDistance / 100) * 100}%` }
                      ]} 
                    />
                  </View>
                </View>
                <View style={styles.sliderLabels}>
                  <Text style={styles.sliderLabel}>1 mile</Text>
                  <Text style={styles.sliderLabel}>100+ miles</Text>
                </View>
              </View>
            </View>
          </View>
        </ScrollView>

        <View style={styles.footer}>
          <TouchableOpacity style={styles.continueButton} onPress={handleContinue}>
            <Text style={styles.continueButtonText}>Continue</Text>
            <ArrowRight size={20} color="#8B5CF6" />
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 24,
  },
  header: {
    paddingTop: 20,
    marginBottom: 32,
  },
  backButton: {
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Poppins-Bold',
    color: 'white',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: 24,
    marginBottom: 24,
  },
  progressBar: {
    flexDirection: 'row',
    gap: 8,
  },
  progressStep: {
    flex: 1,
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
  },
  activeStep: {
    backgroundColor: 'white',
  },
  content: {
    flex: 1,
    paddingBottom: 20,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
    marginBottom: 16,
  },
  goalsContainer: {
    gap: 12,
  },
  goalOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  goalOptionSelected: {
    backgroundColor: 'white',
    borderColor: 'white',
  },
  goalContent: {
    marginLeft: 12,
    flex: 1,
  },
  goalLabel: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
    marginBottom: 4,
  },
  goalLabelSelected: {
    color: '#8B5CF6',
  },
  goalDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  goalDescriptionSelected: {
    color: '#666',
  },
  ageContainer: {
    gap: 20,
  },
  ageGroup: {
    gap: 12,
  },
  ageLabel: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: 'white',
  },
  ageScroll: {
    maxHeight: 50,
  },
  ageOptions: {
    flexDirection: 'row',
    gap: 8,
  },
  ageOption: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  ageOptionSelected: {
    backgroundColor: 'white',
    borderColor: 'white',
  },
  ageOptionText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: 'rgba(255, 255, 255, 0.9)',
  },
  ageOptionTextSelected: {
    color: '#8B5CF6',
  },
  distanceContainer: {
    alignItems: 'center',
    gap: 16,
  },
  distanceValue: {
    fontSize: 24,
    fontFamily: 'Poppins-Bold',
    color: 'white',
  },
  distanceSlider: {
    width: '100%',
    height: 40,
    justifyContent: 'center',
  },
  sliderTrack: {
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
    position: 'relative',
  },
  sliderFill: {
    height: '100%',
    backgroundColor: 'white',
    borderRadius: 2,
  },
  sliderLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  sliderLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.7)',
  },
  footer: {
    paddingHorizontal: 24,
    paddingBottom: 32,
  },
  continueButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
    borderRadius: 12,
    paddingVertical: 16,
    gap: 8,
  },
  continueButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#8B5CF6',
  },
});