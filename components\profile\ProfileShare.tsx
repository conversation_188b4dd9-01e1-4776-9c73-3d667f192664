import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Platform,
  Modal,
  ScrollView,
  Share,
  Clipboard,
} from 'react-native';
import { useProfileStore } from '@/stores/profileStore';
import { theme } from '@/constants/theme';
import * as Haptics from 'expo-haptics';
import {
  Share2,
  Copy,
  MessageCircle,
  Mail,
  X,
  QrCode,
  Link,
  CheckCircle,
} from 'lucide-react-native';

interface ProfileShareProps {
  visible: boolean;
  onClose: () => void;
}

export default function ProfileShare({ visible, onClose }: ProfileShareProps) {
  const { profile } = useProfileStore();
  const [copied, setCopied] = useState(false);

  if (!profile) return null;

  const profileUrl = `https://soulsync.app/profile/${profile.id}`;
  const shareText = `Check out ${profile.name}'s profile on SoulSync! ${profileUrl}`;

  const handleCopyLink = async () => {
    try {
      if (Platform.OS === 'web') {
        await navigator.clipboard.writeText(profileUrl);
      } else {
        Clipboard.setString(profileUrl);
      }
      
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
      
      if (Platform.OS !== 'web') {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to copy link');
    }
  };

  const handleNativeShare = async () => {
    try {
      if (Platform.OS !== 'web') {
        await Share.share({
          message: shareText,
          url: profileUrl,
          title: `${profile.name}'s Profile`,
        });
        
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      }
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  const handleEmailShare = () => {
    const subject = encodeURIComponent(`Check out ${profile.name}'s profile`);
    const body = encodeURIComponent(shareText);
    const emailUrl = `mailto:?subject=${subject}&body=${body}`;
    
    if (Platform.OS === 'web') {
      window.open(emailUrl);
    } else {
      // Handle email sharing on mobile
      Alert.alert('Email Share', 'Email sharing would open your email app');
    }
  };

  const handleSMSShare = () => {
    const smsUrl = `sms:?body=${encodeURIComponent(shareText)}`;
    
    if (Platform.OS === 'web') {
      window.open(smsUrl);
    } else {
      // Handle SMS sharing on mobile
      Alert.alert('SMS Share', 'SMS sharing would open your messages app');
    }
  };

  const shareOptions = [
    {
      id: 'native',
      title: 'Share',
      subtitle: 'Share via system dialog',
      icon: Share2,
      action: handleNativeShare,
      available: Platform.OS !== 'web',
    },
    {
      id: 'copy',
      title: 'Copy Link',
      subtitle: 'Copy profile URL to clipboard',
      icon: Copy,
      action: handleCopyLink,
      available: true,
    },
    {
      id: 'sms',
      title: 'Text Message',
      subtitle: 'Share via SMS',
      icon: MessageCircle,
      action: handleSMSShare,
      available: true,
    },
    {
      id: 'email',
      title: 'Email',
      subtitle: 'Share via email',
      icon: Mail,
      action: handleEmailShare,
      available: true,
    },
  ];

  const ShareOption = ({ option }: { option: typeof shareOptions[0] }) => {
    if (!option.available) return null;

    return (
      <TouchableOpacity
        style={styles.shareOption}
        onPress={option.action}
      >
        <View style={styles.shareOptionLeft}>
          <View style={styles.shareOptionIcon}>
            <option.icon size={20} color={theme.colors.primary} />
          </View>
          <View style={styles.shareOptionContent}>
            <Text style={styles.shareOptionTitle}>{option.title}</Text>
            <Text style={styles.shareOptionSubtitle}>{option.subtitle}</Text>
          </View>
        </View>
        {option.id === 'copy' && copied && (
          <CheckCircle size={20} color={theme.colors.success} />
        )}
      </TouchableOpacity>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <View style={styles.headerIcon}>
              <Share2 size={24} color={theme.colors.primary} />
            </View>
            <View>
              <Text style={styles.headerTitle}>Share Profile</Text>
              <Text style={styles.headerSubtitle}>Share your profile with others</Text>
            </View>
          </View>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <X size={24} color={theme.colors.gray600} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Profile Preview */}
          <View style={styles.previewSection}>
            <Text style={styles.sectionTitle}>Profile Preview</Text>
            <View style={styles.previewCard}>
              <Text style={styles.previewName}>{profile.name}, {profile.age}</Text>
              <Text style={styles.previewLocation}>
                {profile.location.city}, {profile.location.state}
              </Text>
              <Text style={styles.previewBio} numberOfLines={2}>
                {profile.bio}
              </Text>
              <View style={styles.previewStats}>
                <Text style={styles.previewStat}>
                  {profile.photos.length} photos
                </Text>
                <Text style={styles.previewStat}>
                  {profile.interests.length} interests
                </Text>
                {profile.verified && (
                  <Text style={styles.previewStat}>✓ Verified</Text>
                )}
              </View>
            </View>
          </View>

          {/* Share Options */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Share Options</Text>
            <View style={styles.shareOptionsContainer}>
              {shareOptions.map((option) => (
                <ShareOption key={option.id} option={option} />
              ))}
            </View>
          </View>

          {/* Profile Link */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Profile Link</Text>
            <View style={styles.linkContainer}>
              <View style={styles.linkIcon}>
                <Link size={20} color={theme.colors.primary} />
              </View>
              <Text style={styles.linkText} numberOfLines={1}>
                {profileUrl}
              </Text>
              <TouchableOpacity
                style={styles.copyButton}
                onPress={handleCopyLink}
              >
                {copied ? (
                  <CheckCircle size={20} color={theme.colors.success} />
                ) : (
                  <Copy size={20} color={theme.colors.primary} />
                )}
              </TouchableOpacity>
            </View>
          </View>

          {/* QR Code Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>QR Code</Text>
            <View style={styles.qrContainer}>
              <View style={styles.qrPlaceholder}>
                <QrCode size={40} color={theme.colors.gray400} />
                <Text style={styles.qrText}>QR Code</Text>
                <Text style={styles.qrSubtext}>
                  QR code generation coming soon
                </Text>
              </View>
            </View>
          </View>

          {/* Privacy Notice */}
          <View style={styles.privacySection}>
            <Text style={styles.privacyTitle}>Privacy Notice</Text>
            <Text style={styles.privacyText}>
              When you share your profile, others will be able to view your public information including photos, bio, and interests. Your private information like contact details and location coordinates remain protected.
            </Text>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray200,
    backgroundColor: 'white',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
  },
  headerSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
  },
  closeButton: {
    padding: 8,
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    marginVertical: 10,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  previewSection: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    marginTop: 20,
    marginBottom: 10,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 16,
  },
  previewCard: {
    backgroundColor: theme.colors.gray50,
    borderRadius: 12,
    padding: 16,
  },
  previewName: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: theme.colors.text,
    marginBottom: 4,
  },
  previewLocation: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
    marginBottom: 8,
  },
  previewBio: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray700,
    lineHeight: 20,
    marginBottom: 12,
  },
  previewStats: {
    flexDirection: 'row',
    gap: 16,
  },
  previewStat: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: theme.colors.primary,
  },
  shareOptionsContainer: {
    gap: 12,
  },
  shareOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: theme.colors.gray50,
    borderRadius: 12,
  },
  shareOptionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  shareOptionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  shareOptionContent: {
    flex: 1,
  },
  shareOptionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 2,
  },
  shareOptionSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
  },
  linkContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.gray50,
    borderRadius: 12,
    padding: 16,
  },
  linkIcon: {
    marginRight: 12,
  },
  linkText: {
    flex: 1,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text,
  },
  copyButton: {
    marginLeft: 12,
  },
  qrContainer: {
    alignItems: 'center',
  },
  qrPlaceholder: {
    width: 200,
    height: 200,
    backgroundColor: theme.colors.gray100,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: theme.colors.gray200,
    borderStyle: 'dashed',
  },
  qrText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.gray600,
    marginTop: 8,
  },
  qrSubtext: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray500,
    marginTop: 4,
  },
  privacySection: {
    backgroundColor: theme.colors.primary + '10',
    marginHorizontal: 20,
    marginVertical: 10,
    borderRadius: 16,
    padding: 20,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.primary,
  },
  privacyTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 8,
  },
  privacyText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray700,
    lineHeight: 20,
  },
});
