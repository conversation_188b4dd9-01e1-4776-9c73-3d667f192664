import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { likesService } from '@/services/likesService';

/**
 * Debug component to test the likes service functionality
 */
export default function LikesServiceTest() {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const testFetchLikes = async () => {
    setIsLoading(true);
    addResult('Testing fetchReceivedLikes...');
    
    try {
      const result = await likesService.fetchReceivedLikes(1, 5);
      addResult(`✅ Fetched ${result.likes.length} likes, hasMore: ${result.hasMore}`);
      addResult(`First like: ${result.likes[0]?.name || 'None'}`);
    } catch (error) {
      addResult(`❌ Error: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testFetchMatches = async () => {
    setIsLoading(true);
    addResult('Testing fetchMatches...');
    
    try {
      const result = await likesService.fetchMatches(1, 5);
      addResult(`✅ Fetched ${result.matches.length} matches, hasMore: ${result.hasMore}`);
      addResult(`First match: ${result.matches[0]?.otherUser.name || 'None'}`);
    } catch (error) {
      addResult(`❌ Error: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testSendLike = async () => {
    setIsLoading(true);
    addResult('Testing sendLike...');
    
    try {
      const result = await likesService.sendLike('test-user-123', 'like');
      addResult(`✅ Like sent, isMatch: ${result.isMatch}`);
      addResult(`Message: ${result.message}`);
    } catch (error) {
      addResult(`❌ Error: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testSendSuperLike = async () => {
    setIsLoading(true);
    addResult('Testing sendSuperLike...');
    
    try {
      const result = await likesService.sendLike('test-user-456', 'superlike');
      addResult(`✅ Super like sent, isMatch: ${result.isMatch}`);
      addResult(`Message: ${result.message}`);
    } catch (error) {
      addResult(`❌ Error: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testLikeBack = async () => {
    setIsLoading(true);
    addResult('Testing likeBack...');
    
    try {
      const result = await likesService.likeBack('test-user-789');
      addResult(`✅ Like back sent, isMatch: ${result.isMatch}`);
      addResult(`Message: ${result.message}`);
    } catch (error) {
      addResult(`❌ Error: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testSendPass = async () => {
    setIsLoading(true);
    addResult('Testing sendPass...');
    
    try {
      const result = await likesService.sendPass('test-user-000');
      addResult(`✅ Pass sent: ${result.message}`);
    } catch (error) {
      addResult(`❌ Error: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Likes Service Test</Text>
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity 
          style={[styles.button, isLoading && styles.buttonDisabled]} 
          onPress={testFetchLikes}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Test Fetch Likes</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.button, isLoading && styles.buttonDisabled]} 
          onPress={testFetchMatches}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Test Fetch Matches</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.button, isLoading && styles.buttonDisabled]} 
          onPress={testSendLike}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Test Send Like</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.button, isLoading && styles.buttonDisabled]} 
          onPress={testSendSuperLike}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Test Super Like</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.button, isLoading && styles.buttonDisabled]} 
          onPress={testLikeBack}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Test Like Back</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.button, isLoading && styles.buttonDisabled]} 
          onPress={testSendPass}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Test Send Pass</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.button, styles.clearButton]} 
          onPress={clearResults}
        >
          <Text style={styles.buttonText}>Clear Results</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.resultsContainer}>
        <Text style={styles.resultsTitle}>Test Results:</Text>
        {isLoading && (
          <Text style={styles.loadingText}>⏳ Loading...</Text>
        )}
        {testResults.map((result, index) => (
          <Text key={index} style={styles.resultText}>
            {result}
          </Text>
        ))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: 'white',
    margin: 10,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    maxHeight: 600,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
    color: '#8B5CF6',
  },
  buttonContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
    marginBottom: 15,
    gap: 5,
  },
  button: {
    backgroundColor: '#8B5CF6',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 5,
    marginBottom: 5,
    minWidth: 100,
  },
  buttonDisabled: {
    backgroundColor: '#D1D5DB',
  },
  clearButton: {
    backgroundColor: '#EF4444',
  },
  buttonText: {
    color: 'white',
    fontSize: 11,
    fontWeight: '600',
    textAlign: 'center',
  },
  resultsContainer: {
    flex: 1,
    maxHeight: 300,
  },
  resultsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
    color: '#374151',
  },
  loadingText: {
    fontSize: 14,
    color: '#8B5CF6',
    fontStyle: 'italic',
    marginBottom: 5,
  },
  resultText: {
    fontSize: 12,
    marginBottom: 3,
    color: '#374151',
    lineHeight: 16,
  },
});
