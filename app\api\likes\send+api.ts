import { Like } from '@/types/messaging';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { userId, type } = body;
    
    if (!userId || !type) {
      return Response.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    if (!['like', 'superlike'].includes(type)) {
      return Response.json(
        { success: false, error: 'Invalid like type' },
        { status: 400 }
      );
    }
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // Create mock like
    const like: Like = {
      id: `like_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      fromUserId: 'current-user', // In real app, get from auth
      toUserId: userId,
      timestamp: new Date(),
      type: type as 'like' | 'superlike',
    };
    
    // Simulate match probability (30% for regular like, 50% for superlike)
    const matchProbability = type === 'superlike' ? 0.5 : 0.3;
    const isMatch = Math.random() < matchProbability;
    
    if (isMatch) {
      like.isMatch = true;
    }
    
    // In a real app, you would:
    // 1. Save the like to the database
    // 2. Check if the target user has already liked the current user
    // 3. Create a match if both users have liked each other
    // 4. Send push notifications
    // 5. Update analytics
    
    return Response.json({
      success: true,
      like,
      isMatch,
      message: isMatch ? 'It\'s a match!' : 'Like sent successfully',
    });
  } catch (error) {
    console.error('Error sending like:', error);
    return Response.json(
      { 
        success: false, 
        error: 'Failed to send like' 
      },
      { status: 500 }
    );
  }
}
