import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Switch,
  Alert,
  Platform,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useProfileStore } from '@/stores/profileStore';
import { theme } from '@/constants/theme';
import * as Haptics from 'expo-haptics';
import * as Notifications from 'expo-notifications';
import {
  ArrowLeft,
  Bell,
  MessageCircle,
  Heart,
  Star,
  Users,
  Volume2,
  Vibrate,
  Moon,
  Clock,
  Smartphone,
  Mail,
  Settings,
} from 'lucide-react-native';

export default function NotificationsScreen() {
  const router = useRouter();
  const { settings, updateSettings, isUpdating } = useProfileStore();

  const [localSettings, setLocalSettings] = useState({
    // Push Notifications
    pushNotifications: true,
    emailNotifications: false,
    
    // Content Notifications
    newMatches: true,
    newMessages: true,
    likes: true,
    superLikes: true,
    profileViews: false,
    
    // Social Notifications
    friendRequests: true,
    eventInvites: true,
    
    // Marketing
    promotions: false,
    tips: true,
    newsletters: false,
    
    // Sound & Vibration
    soundEnabled: true,
    vibrationEnabled: true,
    
    // Quiet Hours
    quietHoursEnabled: false,
    quietHoursStart: '22:00',
    quietHoursEnd: '08:00',
    
    // Frequency
    instantNotifications: true,
    batchNotifications: false,
    dailyDigest: false,
  });

  const [permissionStatus, setPermissionStatus] = useState<string>('unknown');

  useEffect(() => {
    checkNotificationPermissions();
    loadSettings();
  }, []);

  const checkNotificationPermissions = async () => {
    const { status } = await Notifications.getPermissionsAsync();
    setPermissionStatus(status);
  };

  const loadSettings = () => {
    if (settings?.notifications) {
      setLocalSettings({
        pushNotifications: settings.notifications.pushNotifications,
        emailNotifications: settings.notifications.emailNotifications,
        newMatches: settings.notifications.newMatches,
        newMessages: settings.notifications.newMessages,
        likes: settings.notifications.likes,
        superLikes: settings.notifications.superLikes,
        profileViews: settings.notifications.profileViews || false,
        friendRequests: settings.notifications.friendRequests || true,
        eventInvites: settings.notifications.eventInvites || true,
        promotions: settings.notifications.promotions,
        tips: settings.notifications.tips,
        newsletters: settings.notifications.newsletters || false,
        soundEnabled: settings.notifications.soundEnabled,
        vibrationEnabled: settings.notifications.vibrationEnabled,
        quietHoursEnabled: settings.notifications.quietHours.enabled,
        quietHoursStart: settings.notifications.quietHours.startTime,
        quietHoursEnd: settings.notifications.quietHours.endTime,
        instantNotifications: settings.notifications.instantNotifications || true,
        batchNotifications: settings.notifications.batchNotifications || false,
        dailyDigest: settings.notifications.dailyDigest || false,
      });
    }
  };

  const requestNotificationPermissions = async () => {
    try {
      const { status } = await Notifications.requestPermissionsAsync();
      setPermissionStatus(status);
      
      if (status === 'granted') {
        Alert.alert('Success', 'Notification permissions granted!');
      } else {
        Alert.alert(
          'Permission Denied',
          'You can enable notifications later in your device settings.',
          [
            { text: 'OK' },
            { text: 'Open Settings', onPress: () => Notifications.openSettingsAsync() }
          ]
        );
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to request notification permissions');
    }
  };

  const handleToggle = async (key: string, value: boolean) => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    // If enabling push notifications but permission not granted, request permission
    if (key === 'pushNotifications' && value && permissionStatus !== 'granted') {
      await requestNotificationPermissions();
      if (permissionStatus !== 'granted') {
        return; // Don't update setting if permission denied
      }
    }

    setLocalSettings(prev => ({ ...prev, [key]: value }));

    // Update the store
    if (settings) {
      const updatedSettings = {
        ...settings,
        notifications: {
          ...settings.notifications,
          [key]: value,
          // Handle nested quiet hours
          ...(key === 'quietHoursEnabled' && {
            quietHours: {
              ...settings.notifications.quietHours,
              enabled: value,
            }
          }),
        },
      };

      await updateSettings(updatedSettings);
    }
  };

  const handleTimeChange = (type: 'start' | 'end', time: string) => {
    const key = type === 'start' ? 'quietHoursStart' : 'quietHoursEnd';
    setLocalSettings(prev => ({ ...prev, [key]: time }));
    
    // Update store
    if (settings) {
      const updatedSettings = {
        ...settings,
        notifications: {
          ...settings.notifications,
          quietHours: {
            ...settings.notifications.quietHours,
            [type === 'start' ? 'startTime' : 'endTime']: time,
          },
        },
      };
      updateSettings(updatedSettings);
    }
  };

  const SettingItem = ({ 
    icon: Icon, 
    title, 
    subtitle, 
    value, 
    onToggle,
    disabled = false,
  }: {
    icon: any;
    title: string;
    subtitle?: string;
    value?: boolean;
    onToggle?: (value: boolean) => void;
    disabled?: boolean;
  }) => (
    <View style={[styles.settingItem, disabled && styles.disabledItem]}>
      <View style={styles.settingLeft}>
        <View style={[styles.settingIcon, disabled && styles.disabledIcon]}>
          <Icon size={20} color={disabled ? theme.colors.gray400 : theme.colors.primary} />
        </View>
        <View style={styles.settingContent}>
          <Text style={[styles.settingTitle, disabled && styles.disabledText]}>
            {title}
          </Text>
          {subtitle && (
            <Text style={[styles.settingSubtitle, disabled && styles.disabledText]}>
              {subtitle}
            </Text>
          )}
        </View>
      </View>
      {onToggle && (
        <Switch
          value={value}
          onValueChange={onToggle}
          disabled={disabled}
          trackColor={{ 
            false: theme.colors.gray300, 
            true: theme.colors.primary + '40' 
          }}
          thumbColor={value ? theme.colors.primary : theme.colors.gray400}
        />
      )}
    </View>
  );

  const TimePickerItem = ({ 
    title, 
    time, 
    onTimeChange 
  }: { 
    title: string; 
    time: string; 
    onTimeChange: (time: string) => void;
  }) => (
    <TouchableOpacity 
      style={styles.timePickerItem}
      onPress={() => {
        // In a real app, you'd open a time picker
        Alert.alert('Time Picker', 'Time picker would open here');
      }}
    >
      <Text style={styles.timePickerTitle}>{title}</Text>
      <View style={styles.timeDisplay}>
        <Clock size={16} color={theme.colors.primary} />
        <Text style={styles.timeText}>{time}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Notifications</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Permission Status */}
        {permissionStatus !== 'granted' && (
          <View style={styles.permissionBanner}>
            <Bell size={20} color={theme.colors.warning} />
            <View style={styles.permissionContent}>
              <Text style={styles.permissionTitle}>Enable Notifications</Text>
              <Text style={styles.permissionText}>
                Allow notifications to stay updated on matches and messages
              </Text>
            </View>
            <TouchableOpacity 
              style={styles.enableButton}
              onPress={requestNotificationPermissions}
            >
              <Text style={styles.enableButtonText}>Enable</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Push Notifications */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Push Notifications</Text>
          
          <SettingItem
            icon={Smartphone}
            title="Push Notifications"
            subtitle="Receive notifications on your device"
            value={localSettings.pushNotifications}
            onToggle={(value) => handleToggle('pushNotifications', value)}
            disabled={permissionStatus !== 'granted'}
          />
          
          <SettingItem
            icon={Mail}
            title="Email Notifications"
            subtitle="Receive notifications via email"
            value={localSettings.emailNotifications}
            onToggle={(value) => handleToggle('emailNotifications', value)}
          />
        </View>

        {/* Content Notifications */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Dating Activity</Text>
          
          <SettingItem
            icon={Users}
            title="New Matches"
            subtitle="When you get a new match"
            value={localSettings.newMatches}
            onToggle={(value) => handleToggle('newMatches', value)}
            disabled={!localSettings.pushNotifications}
          />
          
          <SettingItem
            icon={MessageCircle}
            title="New Messages"
            subtitle="When you receive messages"
            value={localSettings.newMessages}
            onToggle={(value) => handleToggle('newMessages', value)}
            disabled={!localSettings.pushNotifications}
          />
          
          <SettingItem
            icon={Heart}
            title="Likes"
            subtitle="When someone likes your profile"
            value={localSettings.likes}
            onToggle={(value) => handleToggle('likes', value)}
            disabled={!localSettings.pushNotifications}
          />
          
          <SettingItem
            icon={Star}
            title="Super Likes"
            subtitle="When someone super likes you"
            value={localSettings.superLikes}
            onToggle={(value) => handleToggle('superLikes', value)}
            disabled={!localSettings.pushNotifications}
          />
        </View>

        {/* Sound & Vibration */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Sound & Vibration</Text>
          
          <SettingItem
            icon={Volume2}
            title="Sound"
            subtitle="Play sounds for notifications"
            value={localSettings.soundEnabled}
            onToggle={(value) => handleToggle('soundEnabled', value)}
          />
          
          <SettingItem
            icon={Vibrate}
            title="Vibration"
            subtitle="Vibrate for notifications"
            value={localSettings.vibrationEnabled}
            onToggle={(value) => handleToggle('vibrationEnabled', value)}
          />
        </View>

        {/* Quiet Hours */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quiet Hours</Text>
          
          <SettingItem
            icon={Moon}
            title="Enable Quiet Hours"
            subtitle="Pause notifications during specified hours"
            value={localSettings.quietHoursEnabled}
            onToggle={(value) => handleToggle('quietHoursEnabled', value)}
          />
          
          {localSettings.quietHoursEnabled && (
            <View style={styles.timePickersContainer}>
              <TimePickerItem
                title="Start Time"
                time={localSettings.quietHoursStart}
                onTimeChange={(time) => handleTimeChange('start', time)}
              />
              <TimePickerItem
                title="End Time"
                time={localSettings.quietHoursEnd}
                onTimeChange={(time) => handleTimeChange('end', time)}
              />
            </View>
          )}
        </View>

        {/* Marketing */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Marketing & Tips</Text>
          
          <SettingItem
            icon={Settings}
            title="Dating Tips"
            subtitle="Helpful tips to improve your experience"
            value={localSettings.tips}
            onToggle={(value) => handleToggle('tips', value)}
          />
          
          <SettingItem
            icon={Mail}
            title="Promotions"
            subtitle="Special offers and promotions"
            value={localSettings.promotions}
            onToggle={(value) => handleToggle('promotions', value)}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray200,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
  },
  placeholder: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  permissionBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.warning + '20',
    marginHorizontal: 20,
    marginTop: 20,
    padding: 16,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.warning,
  },
  permissionContent: {
    flex: 1,
    marginLeft: 12,
  },
  permissionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 4,
  },
  permissionText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
  },
  enableButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginLeft: 12,
  },
  enableButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  section: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    marginVertical: 10,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 12,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray100,
  },
  disabledItem: {
    opacity: 0.5,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  disabledIcon: {
    backgroundColor: theme.colors.gray200,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: theme.colors.text,
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
  },
  disabledText: {
    color: theme.colors.gray400,
  },
  timePickersContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
    gap: 12,
  },
  timePickerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: theme.colors.gray50,
    padding: 16,
    borderRadius: 12,
  },
  timePickerTitle: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: theme.colors.text,
  },
  timeDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  timeText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.primary,
  },
});
