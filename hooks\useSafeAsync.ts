import { useEffect, useRef, useCallback } from 'react';

/**
 * Custom hook to safely handle async operations and prevent state updates
 * on unmounted components
 */
export function useSafeAsync() {
  const isMountedRef = useRef(true);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const safeAsync = useCallback(async <T>(
    asyncFn: () => Promise<T>,
    onSuccess?: (result: T) => void,
    onError?: (error: Error) => void
  ): Promise<T | null> => {
    try {
      const result = await asyncFn();
      
      if (isMountedRef.current && onSuccess) {
        onSuccess(result);
      }
      
      return isMountedRef.current ? result : null;
    } catch (error) {
      if (isMountedRef.current && onError) {
        onError(error instanceof Error ? error : new Error('Unknown error'));
      }
      
      if (isMountedRef.current) {
        throw error;
      }
      
      return null;
    }
  }, []);

  const safeSetTimeout = useCallback((
    callback: () => void,
    delay: number
  ): NodeJS.Timeout => {
    return setTimeout(() => {
      if (isMountedRef.current) {
        callback();
      }
    }, delay);
  }, []);

  const safeSetState = useCallback(<T>(
    setState: (value: T | ((prev: T) => T)) => void,
    value: T | ((prev: T) => T)
  ) => {
    if (isMountedRef.current) {
      setState(value);
    }
  }, []);

  return {
    isMounted: isMountedRef.current,
    safeAsync,
    safeSetTimeout,
    safeSetState,
  };
}
