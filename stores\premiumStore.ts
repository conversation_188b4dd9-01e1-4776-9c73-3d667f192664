import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  PremiumSubscription, 
  SubscriptionPlan, 
  PremiumFeatureType, 
  FeatureAccess, 
  PaymentTransaction,
  PremiumUsage,
  DEFAULT_SUBSCRIPTION_PLANS,
  FREE_USER_LIMITS,
  SubscriptionStatus,
  BillingCycle
} from '@/types/premium';
import * as Haptics from 'expo-haptics';
import { Platform } from 'react-native';

interface PremiumState {
  // Subscription data
  subscription: PremiumSubscription | null;
  availablePlans: SubscriptionPlan[];
  selectedPlan: string | null;
  
  // Premium status
  isPremium: boolean;
  premiumFeatures: PremiumFeatureType[];
  usage: PremiumUsage | null;
  
  // Payment and transactions
  paymentMethods: any[];
  transactions: PaymentTransaction[];
  isProcessingPayment: boolean;
  
  // UI state
  isLoading: boolean;
  isLoadingPlans: boolean;
  isLoadingSubscription: boolean;
  error: string | null;
  
  // Actions
  loadSubscription: () => Promise<void>;
  loadPlans: () => Promise<void>;
  selectPlan: (planId: string) => void;
  subscribe: (planId: string) => Promise<boolean>;
  cancelSubscription: () => Promise<boolean>;
  restorePurchases: () => Promise<boolean>;
  
  // Feature access
  hasFeature: (feature: PremiumFeatureType) => boolean;
  checkFeatureAccess: (feature: PremiumFeatureType) => FeatureAccess;
  incrementUsage: (feature: PremiumFeatureType) => void;
  
  // Payment methods
  addPaymentMethod: (paymentMethod: any) => Promise<boolean>;
  removePaymentMethod: (methodId: string) => Promise<boolean>;
  
  // Utility
  clearError: () => void;
  reset: () => void;
}

// Initial state
const initialState = {
  subscription: null,
  availablePlans: DEFAULT_SUBSCRIPTION_PLANS,
  selectedPlan: '3_months', // Default to most popular
  isPremium: false,
  premiumFeatures: [],
  usage: null,
  paymentMethods: [],
  transactions: [],
  isProcessingPayment: false,
  isLoading: false,
  isLoadingPlans: false,
  isLoadingSubscription: false,
  error: null,
};

// Create default usage tracking
const createDefaultUsage = (userId: string): PremiumUsage => ({
  userId,
  period: new Date(),
  likesUsed: 0,
  superLikesUsed: 0,
  boostsUsed: 0,
  rewindsUsed: 0,
  features: {},
});

// Demo subscription for testing
const createDemoSubscription = (planId: string): PremiumSubscription => {
  const plan = DEFAULT_SUBSCRIPTION_PLANS.find(p => p.id === planId);
  if (!plan) throw new Error('Plan not found');
  
  const startDate = new Date();
  const endDate = new Date();
  
  // Calculate end date based on billing cycle
  switch (plan.billingCycle) {
    case '1_month':
      endDate.setMonth(endDate.getMonth() + 1);
      break;
    case '3_months':
      endDate.setMonth(endDate.getMonth() + 3);
      break;
    case '6_months':
      endDate.setMonth(endDate.getMonth() + 6);
      break;
    case '12_months':
      endDate.setFullYear(endDate.getFullYear() + 1);
      break;
  }
  
  return {
    id: `sub_${Date.now()}`,
    userId: 'demo_user',
    planId: plan.id,
    status: 'active' as SubscriptionStatus,
    startDate,
    endDate,
    autoRenew: true,
    paymentMethod: {
      id: 'pm_demo',
      type: 'card',
      last4: '4242',
      brand: 'visa',
      isDefault: true,
    },
    price: plan.price,
    currency: plan.currency,
    billingCycle: plan.billingCycle,
    createdAt: startDate,
    updatedAt: startDate,
  };
};

export const usePremiumStore = create<PremiumState>()(
  persist(
    (set, get) => ({
      ...initialState,

      loadSubscription: async () => {
        set({ isLoadingSubscription: true, error: null });
        
        try {
          // In production, this would fetch from API
          // For demo, check if user has active subscription in storage
          const state = get();
          
          if (state.subscription && state.subscription.status === 'active') {
            const now = new Date();
            const endDate = new Date(state.subscription.endDate);
            
            if (now > endDate) {
              // Subscription expired
              set({ 
                subscription: { ...state.subscription, status: 'expired' },
                isPremium: false,
                premiumFeatures: [],
                isLoadingSubscription: false 
              });
            } else {
              // Subscription still active
              const plan = DEFAULT_SUBSCRIPTION_PLANS.find(p => p.id === state.subscription?.planId);
              set({ 
                isPremium: true,
                premiumFeatures: plan?.features || [],
                isLoadingSubscription: false 
              });
            }
          } else {
            set({ 
              isPremium: false,
              premiumFeatures: [],
              isLoadingSubscription: false 
            });
          }
          
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to load subscription',
            isLoadingSubscription: false 
          });
        }
      },

      loadPlans: async () => {
        set({ isLoadingPlans: true, error: null });
        
        try {
          // In production, this would fetch from API
          // For demo, use default plans
          await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API call
          
          set({ 
            availablePlans: DEFAULT_SUBSCRIPTION_PLANS,
            isLoadingPlans: false 
          });
          
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to load plans',
            isLoadingPlans: false 
          });
        }
      },

      selectPlan: (planId: string) => {
        if (Platform.OS !== 'web') {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        }
        set({ selectedPlan: planId });
      },

      subscribe: async (planId: string) => {
        set({ isProcessingPayment: true, error: null });
        
        try {
          if (Platform.OS !== 'web') {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          }
          
          // In production, this would integrate with payment processor
          // For demo, simulate payment processing
          await new Promise(resolve => setTimeout(resolve, 2000));
          
          const subscription = createDemoSubscription(planId);
          const plan = DEFAULT_SUBSCRIPTION_PLANS.find(p => p.id === planId);
          const usage = createDefaultUsage('demo_user');
          
          set({ 
            subscription,
            isPremium: true,
            premiumFeatures: plan?.features || [],
            usage,
            isProcessingPayment: false 
          });
          
          if (Platform.OS !== 'web') {
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          }
          
          return true;
          
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Payment failed',
            isProcessingPayment: false 
          });
          
          if (Platform.OS !== 'web') {
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
          }
          
          return false;
        }
      },

      cancelSubscription: async () => {
        set({ isLoading: true, error: null });
        
        try {
          // In production, this would call API to cancel subscription
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          const state = get();
          if (state.subscription) {
            set({ 
              subscription: { 
                ...state.subscription, 
                status: 'cancelled',
                cancelledAt: new Date(),
                autoRenew: false 
              },
              isLoading: false 
            });
          }
          
          return true;
          
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to cancel subscription',
            isLoading: false 
          });
          return false;
        }
      },

      restorePurchases: async () => {
        set({ isLoading: true, error: null });
        
        try {
          // In production, this would restore purchases from app store
          await new Promise(resolve => setTimeout(resolve, 1500));
          
          // For demo, check if there's a stored subscription
          const state = get();
          if (state.subscription) {
            await get().loadSubscription();
          }
          
          set({ isLoading: false });
          return true;
          
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to restore purchases',
            isLoading: false 
          });
          return false;
        }
      },

      hasFeature: (feature: PremiumFeatureType) => {
        const state = get();
        return state.isPremium && state.premiumFeatures.includes(feature);
      },

      checkFeatureAccess: (feature: PremiumFeatureType): FeatureAccess => {
        const state = get();
        
        if (state.isPremium && state.premiumFeatures.includes(feature)) {
          return { canAccess: true };
        }
        
        // Check free user limits
        const usage = state.usage;
        const today = new Date().toDateString();
        
        switch (feature) {
          case 'unlimited_likes':
            const dailyLikes = usage?.likesUsed || 0;
            if (dailyLikes >= FREE_USER_LIMITS.daily_likes) {
              return {
                canAccess: false,
                reason: 'Daily like limit reached',
                upgradeRequired: true,
                featureLimit: {
                  current: dailyLikes,
                  limit: FREE_USER_LIMITS.daily_likes,
                  resetDate: new Date(new Date().setHours(24, 0, 0, 0)),
                },
              };
            }
            return { canAccess: true };
            
          case 'super_likes':
            const dailySuperLikes = usage?.superLikesUsed || 0;
            if (dailySuperLikes >= FREE_USER_LIMITS.super_likes) {
              return {
                canAccess: false,
                reason: 'Daily Super Like limit reached',
                upgradeRequired: true,
                featureLimit: {
                  current: dailySuperLikes,
                  limit: FREE_USER_LIMITS.super_likes,
                  resetDate: new Date(new Date().setHours(24, 0, 0, 0)),
                },
              };
            }
            return { canAccess: true };
            
          default:
            return {
              canAccess: false,
              reason: 'Premium feature requires subscription',
              upgradeRequired: true,
            };
        }
      },

      incrementUsage: (feature: PremiumFeatureType) => {
        const state = get();
        const usage = state.usage || createDefaultUsage('demo_user');
        
        switch (feature) {
          case 'unlimited_likes':
            usage.likesUsed += 1;
            break;
          case 'super_likes':
            usage.superLikesUsed += 1;
            break;
          case 'profile_boost':
            usage.boostsUsed += 1;
            break;
          case 'rewind_swipes':
            usage.rewindsUsed += 1;
            break;
        }
        
        // Update feature-specific usage
        if (!usage.features[feature]) {
          usage.features[feature] = { used: 0 };
        }
        usage.features[feature]!.used += 1;
        usage.features[feature]!.lastUsed = new Date();
        
        set({ usage });
      },

      addPaymentMethod: async (paymentMethod: any) => {
        set({ isLoading: true, error: null });
        
        try {
          // In production, this would add payment method via API
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          const state = get();
          set({ 
            paymentMethods: [...state.paymentMethods, paymentMethod],
            isLoading: false 
          });
          
          return true;
          
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to add payment method',
            isLoading: false 
          });
          return false;
        }
      },

      removePaymentMethod: async (methodId: string) => {
        set({ isLoading: true, error: null });
        
        try {
          // In production, this would remove payment method via API
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          const state = get();
          set({ 
            paymentMethods: state.paymentMethods.filter(pm => pm.id !== methodId),
            isLoading: false 
          });
          
          return true;
          
        } catch (error) {
          set({ 
            error: error instanceof Error ? error.message : 'Failed to remove payment method',
            isLoading: false 
          });
          return false;
        }
      },

      clearError: () => set({ error: null }),
      
      reset: () => set(initialState),
    }),
    {
      name: 'premium-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        subscription: state.subscription,
        isPremium: state.isPremium,
        premiumFeatures: state.premiumFeatures,
        usage: state.usage,
        selectedPlan: state.selectedPlan,
      }),
    }
  )
);
