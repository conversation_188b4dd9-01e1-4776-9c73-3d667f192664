import { Message, Conversation, CallSession } from '@/types/messaging';

export class WebSocketService {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private listeners: Map<string, Function[]> = new Map();

  constructor(private userId: string) {
    this.connect();
  }

  private connect() {
    try {
      // In production, use your WebSocket server URL
      this.ws = new WebSocket(`ws://localhost:8080?userId=${this.userId}`);
      
      this.ws.onopen = () => {
        console.log('WebSocket connected');
        this.reconnectAttempts = 0;
        this.emit('connected');
      };

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.handleMessage(data);
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      this.ws.onclose = () => {
        console.log('WebSocket disconnected');
        this.emit('disconnected');
        this.attemptReconnect();
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        this.emit('error', error);
      };
    } catch (error) {
      console.error('Failed to connect WebSocket:', error);
      this.attemptReconnect();
    }
  }

  private attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        console.log(`Reconnecting... Attempt ${this.reconnectAttempts}`);
        this.connect();
      }, this.reconnectDelay * this.reconnectAttempts);
    }
  }

  private handleMessage(data: any) {
    switch (data.type) {
      case 'message':
        this.emit('message', data.payload);
        break;
      case 'messageStatus':
        this.emit('messageStatus', data.payload);
        break;
      case 'typing':
        this.emit('typing', data.payload);
        break;
      case 'userStatus':
        this.emit('userStatus', data.payload);
        break;
      case 'callIncoming':
        this.emit('callIncoming', data.payload);
        break;
      case 'callAnswer':
        this.emit('callAnswer', data.payload);
        break;
      case 'callEnd':
        this.emit('callEnd', data.payload);
        break;
      case 'newLike':
        this.emit('newLike', data.payload);
        break;
      case 'newMatch':
        this.emit('newMatch', data.payload);
        break;
      case 'likeBack':
        this.emit('likeBack', data.payload);
        break;
      default:
        console.log('Unknown message type:', data.type);
    }
  }

  public sendMessage(message: Partial<Message>) {
    this.send({
      type: 'sendMessage',
      payload: message
    });
  }

  public markAsRead(messageId: string) {
    this.send({
      type: 'markAsRead',
      payload: { messageId }
    });
  }

  public startTyping(conversationId: string) {
    this.send({
      type: 'startTyping',
      payload: { conversationId }
    });
  }

  public stopTyping(conversationId: string) {
    this.send({
      type: 'stopTyping',
      payload: { conversationId }
    });
  }

  public initiateCall(receiverId: string, type: 'audio' | 'video') {
    this.send({
      type: 'initiateCall',
      payload: { receiverId, type }
    });
  }

  public answerCall(callId: string) {
    this.send({
      type: 'answerCall',
      payload: { callId }
    });
  }

  public endCall(callId: string) {
    this.send({
      type: 'endCall',
      payload: { callId }
    });
  }

  public sendLike(userId: string, type: 'like' | 'superlike') {
    this.send({
      type: 'sendLike',
      payload: { userId, type }
    });
  }

  public likeBack(userId: string) {
    this.send({
      type: 'likeBack',
      payload: { userId }
    });
  }

  public send(data: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(data));
    } else {
      console.warn('WebSocket not connected, message queued');
      // In production, implement message queuing
    }
  }

  public on(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  }

  public off(event: string, callback: Function) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      const index = eventListeners.indexOf(callback);
      if (index > -1) {
        eventListeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data?: any) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(callback => callback(data));
    }
  }

  public disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.listeners.clear();
  }
}