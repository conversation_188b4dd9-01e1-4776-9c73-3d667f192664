import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
  Alert,
  Platform,
  ActionSheetIOS,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useProfileStore } from '@/stores/profileStore';
import { theme } from '@/constants/theme';
import * as Haptics from 'expo-haptics';
import * as ImagePicker from 'expo-image-picker';
import {
  ArrowLeft,
  Plus,
  Camera,
  Image as ImageIcon,
  Star,
  Trash2,
  Move,
  Edit3,
} from 'lucide-react-native';

export default function PhotoManagementScreen() {
  const router = useRouter();
  const { 
    profile, 
    uploadPhoto, 
    deletePhoto, 
    setMainPhoto, 
    reorderPhotos,
    isUploadingPhoto 
  } = useProfileStore();

  const [isReordering, setIsReordering] = useState(false);

  const requestPermissions = async () => {
    const cameraPermission = await ImagePicker.requestCameraPermissionsAsync();
    const galleryPermission = await ImagePicker.requestMediaLibraryPermissionsAsync();
    
    return cameraPermission.status === 'granted' && galleryPermission.status === 'granted';
  };

  const showImagePicker = () => {
    if (Platform.OS === 'ios') {
      ActionSheetIOS.showActionSheetWithOptions(
        {
          options: ['Cancel', 'Take Photo', 'Choose from Gallery'],
          cancelButtonIndex: 0,
        },
        (buttonIndex) => {
          if (buttonIndex === 1) {
            takePhoto();
          } else if (buttonIndex === 2) {
            pickImage();
          }
        }
      );
    } else {
      Alert.alert(
        'Add Photo',
        'Choose an option',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Take Photo', onPress: takePhoto },
          { text: 'Choose from Gallery', onPress: pickImage },
        ]
      );
    }
  };

  const takePhoto = async () => {
    try {
      const hasPermission = await requestPermissions();
      if (!hasPermission) {
        Alert.alert('Permission Required', 'Camera and gallery permissions are required to take photos.');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [4, 5],
        quality: 0.9,
      });

      if (!result.canceled && result.assets[0]) {
        await handlePhotoUpload(result.assets[0].uri);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to take photo. Please try again.');
    }
  };

  const pickImage = async () => {
    try {
      const hasPermission = await requestPermissions();
      if (!hasPermission) {
        Alert.alert('Permission Required', 'Gallery permission is required to select photos.');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 5],
        quality: 0.9,
      });

      if (!result.canceled && result.assets[0]) {
        await handlePhotoUpload(result.assets[0].uri);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to select photo. Please try again.');
    }
  };

  const handlePhotoUpload = async (uri: string) => {
    if (!profile) return;

    if (profile.photos.length >= 9) {
      Alert.alert('Limit Reached', 'You can upload up to 9 photos.');
      return;
    }

    try {
      // Show loading state
      const loadingAlert = Alert.alert('Uploading', 'Please wait while we upload your photo...');

      // Simulate photo processing and upload
      await new Promise(resolve => setTimeout(resolve, 2000));

      await uploadPhoto({
        uri,
        type: 'image/jpeg',
        name: `photo-${Date.now()}.jpg`,
        isMain: profile.photos.length === 0,
        order: profile.photos.length,
      });

      if (Platform.OS !== 'web') {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      }

      Alert.alert('Success', 'Photo uploaded successfully!');
    } catch (error) {
      console.error('Photo upload error:', error);
      Alert.alert('Error', 'Failed to upload photo. Please check your internet connection and try again.');
    }
  };

  const handleDeletePhoto = (photoId: string) => {
    Alert.alert(
      'Delete Photo',
      'Are you sure you want to delete this photo?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deletePhoto(photoId);
              if (Platform.OS !== 'web') {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
              }
            } catch (error) {
              Alert.alert('Error', 'Failed to delete photo. Please try again.');
            }
          },
        },
      ]
    );
  };

  const handleSetMainPhoto = async (photoId: string) => {
    try {
      await setMainPhoto(photoId);
      if (Platform.OS !== 'web') {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      }
      Alert.alert('Success', 'Main photo updated!');
    } catch (error) {
      Alert.alert('Error', 'Failed to set main photo. Please try again.');
    }
  };

  const PhotoSlot = ({ photo, index }: { photo?: any; index: number }) => {
    const isEmpty = !photo;
    const isMain = photo?.isMain;

    return (
      <View style={styles.photoSlot}>
        {isEmpty ? (
          <TouchableOpacity 
            style={styles.emptySlot} 
            onPress={showImagePicker}
            disabled={isUploadingPhoto}
          >
            <Plus size={32} color={theme.colors.gray400} />
            <Text style={styles.emptySlotText}>Add Photo</Text>
          </TouchableOpacity>
        ) : (
          <View style={styles.photoContainer}>
            <Image source={{ uri: photo.url }} style={styles.photo} />
            
            {isMain && (
              <View style={styles.mainBadge}>
                <Star size={16} color="white" fill="white" />
                <Text style={styles.mainBadgeText}>Main</Text>
              </View>
            )}

            <View style={styles.photoActions}>
              {!isMain && (
                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={() => handleSetMainPhoto(photo.id)}
                >
                  <Star size={16} color="white" />
                </TouchableOpacity>
              )}
              
              <TouchableOpacity
                style={[styles.actionButton, styles.deleteButton]}
                onPress={() => handleDeletePhoto(photo.id)}
              >
                <Trash2 size={16} color="white" />
              </TouchableOpacity>
            </View>

            {index === 0 && (
              <View style={styles.orderBadge}>
                <Text style={styles.orderText}>1</Text>
              </View>
            )}
          </View>
        )}
      </View>
    );
  };

  if (!profile) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Failed to load profile</Text>
        </View>
      </SafeAreaView>
    );
  }

  const sortedPhotos = [...profile.photos].sort((a, b) => a.order - b.order);

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Manage Photos</Text>
        <TouchableOpacity 
          style={styles.reorderButton}
          onPress={() => setIsReordering(!isReordering)}
        >
          <Move size={20} color={theme.colors.primary} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Info Section */}
        <View style={styles.infoSection}>
          <Text style={styles.infoTitle}>Photo Guidelines</Text>
          <Text style={styles.infoText}>
            • Upload 2-9 high-quality photos{'\n'}
            • First photo will be your main profile photo{'\n'}
            • Show your face clearly in at least one photo{'\n'}
            • Avoid group photos as your main photo{'\n'}
            • Keep photos recent and authentic
          </Text>
        </View>

        {/* Photo Grid */}
        <View style={styles.photoGrid}>
          {Array.from({ length: 9 }, (_, index) => {
            const photo = sortedPhotos[index];
            return (
              <PhotoSlot 
                key={index} 
                photo={photo} 
                index={index}
              />
            );
          })}
        </View>

        {/* Upload Button */}
        {profile.photos.length < 9 && (
          <TouchableOpacity 
            style={[styles.uploadButton, isUploadingPhoto && styles.uploadButtonDisabled]}
            onPress={showImagePicker}
            disabled={isUploadingPhoto}
          >
            <Camera size={20} color="white" />
            <Text style={styles.uploadButtonText}>
              {isUploadingPhoto ? 'Uploading...' : 'Add More Photos'}
            </Text>
          </TouchableOpacity>
        )}

        {/* Photo Count */}
        <View style={styles.photoCount}>
          <Text style={styles.photoCountText}>
            {profile.photos.length} of 9 photos
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray200,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
  },
  reorderButton: {
    padding: 8,
  },
  scrollView: {
    flex: 1,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: theme.colors.error,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
  },
  infoSection: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    marginVertical: 10,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  infoTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 12,
  },
  infoText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
    lineHeight: 20,
  },
  photoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    gap: 10,
  },
  photoSlot: {
    width: '31%',
    aspectRatio: 4/5,
    marginBottom: 10,
  },
  emptySlot: {
    flex: 1,
    backgroundColor: theme.colors.gray100,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: theme.colors.gray300,
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptySlotText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: theme.colors.gray500,
    marginTop: 4,
  },
  photoContainer: {
    flex: 1,
    position: 'relative',
  },
  photo: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
  },
  mainBadge: {
    position: 'absolute',
    top: 8,
    left: 8,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  mainBadgeText: {
    fontSize: 10,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  photoActions: {
    position: 'absolute',
    top: 8,
    right: 8,
    gap: 4,
  },
  actionButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  deleteButton: {
    backgroundColor: theme.colors.error,
  },
  orderBadge: {
    position: 'absolute',
    bottom: 8,
    left: 8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  orderText: {
    fontSize: 12,
    fontFamily: 'Inter-Bold',
    color: 'white',
  },
  uploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.primary,
    marginHorizontal: 20,
    marginVertical: 20,
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
  },
  uploadButtonDisabled: {
    opacity: 0.6,
  },
  uploadButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  photoCount: {
    alignItems: 'center',
    paddingBottom: 20,
  },
  photoCountText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
  },
});
