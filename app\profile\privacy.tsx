import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Switch,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import * as Haptics from 'expo-haptics';
import {
  ArrowLeft,
  Eye,
  EyeOff,
  MapPin,
  MessageCircle,
  Shield,
  UserX,
  AlertTriangle,
  Globe,
  Lock,
  Users,
  Clock,
  Database,
  Share,
} from 'lucide-react-native';

import { theme } from '../../constants/theme';
import { useProfileStore } from '../../stores/profileStore';
import LoadingSkeleton from '../../components/LoadingSkeleton';

export default function PrivacySettingsScreen() {
  const router = useRouter();
  const { profile, settings, updateSettings, isLoading } = useProfileStore();
  
  const [localSettings, setLocalSettings] = useState({
    profileVisibility: 'public' as 'public' | 'private' | 'friends',
    showDistance: true,
    showAge: true,
    showLastSeen: false,
    allowMessagesFrom: 'matches' as 'everyone' | 'matches' | 'premium',
    showOnlineStatus: true,
    incognitoMode: false,
    dataSharing: false,
    analytics: true,
    locationServices: true,
    showProfileViews: false,
    allowScreenshots: true,
    readReceipts: true,
  });

  useEffect(() => {
    if (settings?.privacy && settings?.account) {
      setLocalSettings({
        profileVisibility: settings.privacy.profileVisibility,
        showDistance: settings.privacy.showDistance,
        showAge: settings.privacy.showAge,
        showLastSeen: settings.privacy.showLastSeen,
        allowMessagesFrom: settings.privacy.allowMessagesFrom,
        showOnlineStatus: settings.privacy.showOnlineStatus,
        incognitoMode: settings.privacy.incognitoMode,
        dataSharing: settings.account.dataSharing,
        analytics: settings.account.analytics,
        locationServices: settings.account.locationServices,
        showProfileViews: false, // Add to settings later
        allowScreenshots: true, // Add to settings later
        readReceipts: true, // Add to settings later
      });
    }
  }, [settings]);

  const handleBack = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    router.back();
  };

  const handleToggle = async (key: string, value: boolean) => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    setLocalSettings(prev => ({ ...prev, [key]: value }));

    try {
      let updatedSettings = { ...settings };
      
      // Update the appropriate settings section
      if (['profileVisibility', 'showDistance', 'showAge', 'showLastSeen', 'allowMessagesFrom', 'showOnlineStatus', 'incognitoMode'].includes(key)) {
        updatedSettings.privacy = {
          ...settings?.privacy,
          [key]: value,
        };
      } else if (['dataSharing', 'analytics', 'locationServices'].includes(key)) {
        updatedSettings.account = {
          ...settings?.account,
          [key]: value,
        };
      }
      
      await updateSettings(updatedSettings);
    } catch (error) {
      console.error('Failed to update setting:', error);
      // Revert on error
      setLocalSettings(prev => ({ ...prev, [key]: !value }));
      Alert.alert('Error', 'Failed to update setting. Please try again.');
    }
  };

  const handleVisibilityChange = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    const options = [
      { label: 'Public', value: 'public', description: 'Anyone can see your profile' },
      { label: 'Private', value: 'private', description: 'Only matches can see your profile' },
      { label: 'Friends Only', value: 'friends', description: 'Only friends can see your profile' },
    ];

    Alert.alert(
      'Profile Visibility',
      'Choose who can see your profile',
      [
        ...options.map(option => ({
          text: option.label,
          onPress: () => handleToggle('profileVisibility', option.value as any),
        })),
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const handleMessagesFromChange = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    const options = [
      { label: 'Everyone', value: 'everyone' },
      { label: 'Matches Only', value: 'matches' },
      { label: 'Premium Users', value: 'premium' },
    ];

    Alert.alert(
      'Message Permissions',
      'Choose who can send you messages',
      [
        ...options.map(option => ({
          text: option.label,
          onPress: () => handleToggle('allowMessagesFrom', option.value as any),
        })),
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const handleBlockedUsers = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    router.push('/profile/blocked-users');
  };

  const handleDataDownload = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    Alert.alert(
      'Download Your Data',
      'We will prepare a file with all your data and send it to your email address.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Request Download', onPress: () => {
          Alert.alert('Request Sent', 'You will receive an email with your data within 24 hours.');
        }},
      ]
    );
  };

  if (isLoading) {
    return (
      <LinearGradient colors={['#8B5CF6', '#EC4899']} style={styles.container}>
        <SafeAreaView style={styles.safeArea}>
          <LoadingSkeleton />
        </SafeAreaView>
      </LinearGradient>
    );
  }

  const SettingItem = ({ 
    icon: Icon, 
    title, 
    subtitle, 
    value, 
    onToggle, 
    type = 'switch',
    onPress,
    rightText,
  }: {
    icon: any;
    title: string;
    subtitle?: string;
    value?: boolean;
    onToggle?: (value: boolean) => void;
    type?: 'switch' | 'button';
    onPress?: () => void;
    rightText?: string;
  }) => (
    <TouchableOpacity 
      style={styles.settingItem} 
      onPress={type === 'button' ? onPress : undefined}
      disabled={type === 'switch'}
    >
      <View style={styles.settingLeft}>
        <View style={styles.settingIcon}>
          <Icon size={20} color={theme.colors.primary} />
        </View>
        <View style={styles.settingContent}>
          <Text style={styles.settingTitle}>{title}</Text>
          {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
        </View>
      </View>
      
      {type === 'switch' && onToggle && (
        <Switch
          value={value}
          onValueChange={onToggle}
          trackColor={{ false: theme.colors.gray300, true: theme.colors.primary + '40' }}
          thumbColor={value ? theme.colors.primary : theme.colors.gray400}
        />
      )}
      
      {type === 'button' && rightText && (
        <Text style={styles.rightText}>{rightText}</Text>
      )}
    </TouchableOpacity>
  );

  return (
    <LinearGradient colors={['#8B5CF6', '#EC4899']} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <ArrowLeft size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Privacy & Safety</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Profile Visibility */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Profile Visibility</Text>
            
            <SettingItem
              icon={Globe}
              title="Profile Visibility"
              subtitle={`Currently: ${localSettings.profileVisibility}`}
              type="button"
              onPress={handleVisibilityChange}
              rightText={localSettings.profileVisibility}
            />
            
            <SettingItem
              icon={Eye}
              title="Show Distance"
              subtitle="Let others see how far away you are"
              value={localSettings.showDistance}
              onToggle={(value) => handleToggle('showDistance', value)}
            />
            
            <SettingItem
              icon={Users}
              title="Show Age"
              subtitle="Display your age on your profile"
              value={localSettings.showAge}
              onToggle={(value) => handleToggle('showAge', value)}
            />
            
            <SettingItem
              icon={Clock}
              title="Show Last Seen"
              subtitle="Let others see when you were last active"
              value={localSettings.showLastSeen}
              onToggle={(value) => handleToggle('showLastSeen', value)}
            />
            
            <SettingItem
              icon={Eye}
              title="Show Online Status"
              subtitle="Let others see when you're online"
              value={localSettings.showOnlineStatus}
              onToggle={(value) => handleToggle('showOnlineStatus', value)}
            />
          </View>

          {/* Messaging Privacy */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Messaging</Text>
            
            <SettingItem
              icon={MessageCircle}
              title="Allow Messages From"
              subtitle={`Currently: ${localSettings.allowMessagesFrom}`}
              type="button"
              onPress={handleMessagesFromChange}
              rightText={localSettings.allowMessagesFrom}
            />
            
            <SettingItem
              icon={Eye}
              title="Read Receipts"
              subtitle="Let others know when you've read their messages"
              value={localSettings.readReceipts}
              onToggle={(value) => handleToggle('readReceipts', value)}
            />
          </View>

          {/* Advanced Privacy */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Advanced Privacy</Text>
            
            <SettingItem
              icon={EyeOff}
              title="Incognito Mode"
              subtitle="Browse profiles without being seen"
              value={localSettings.incognitoMode}
              onToggle={(value) => handleToggle('incognitoMode', value)}
            />
            
            <SettingItem
              icon={Shield}
              title="Allow Screenshots"
              subtitle="Allow others to screenshot your profile"
              value={localSettings.allowScreenshots}
              onToggle={(value) => handleToggle('allowScreenshots', value)}
            />
            
            <SettingItem
              icon={UserX}
              title="Blocked Users"
              subtitle="Manage blocked users"
              type="button"
              onPress={handleBlockedUsers}
            />
          </View>

          {/* Data Privacy */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Data Privacy</Text>
            
            <SettingItem
              icon={MapPin}
              title="Location Services"
              subtitle="Allow location access for better matches"
              value={localSettings.locationServices}
              onToggle={(value) => handleToggle('locationServices', value)}
            />
            
            <SettingItem
              icon={Share}
              title="Data Sharing"
              subtitle="Share anonymized data for app improvement"
              value={localSettings.dataSharing}
              onToggle={(value) => handleToggle('dataSharing', value)}
            />
            
            <SettingItem
              icon={Database}
              title="Analytics"
              subtitle="Help improve the app with usage analytics"
              value={localSettings.analytics}
              onToggle={(value) => handleToggle('analytics', value)}
            />
            
            <SettingItem
              icon={Database}
              title="Download Your Data"
              subtitle="Get a copy of all your data"
              type="button"
              onPress={handleDataDownload}
            />
          </View>
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  placeholder: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    marginVertical: 10,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 12,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray100,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: theme.colors.text,
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
  },
  rightText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: theme.colors.primary,
    textTransform: 'capitalize',
  },
});
