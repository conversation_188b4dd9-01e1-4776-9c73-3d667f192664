import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  SafeAreaView,
  Dimensions,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Phone, PhoneOff, Video, VideoOff, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Speaker, MoveVertical as MoreVertical, Monitor, Sword as Record, Users } from 'lucide-react-native';
import { CallSession, User } from '@/types/messaging';

interface CallScreenProps {
  callSession: CallSession;
  currentUser: User;
  localStream?: MediaStream;
  remoteStream?: MediaStream;
  onEndCall: () => void;
  onToggleVideo: () => void;
  onToggleAudio: () => void;
  onToggleSpeaker: () => void;
  onStartScreenShare: () => void;
  onStopScreenShare: () => void;
  onStartRecording: () => void;
  onStopRecording: () => void;
  isVideoEnabled: boolean;
  isAudioEnabled: boolean;
  isSpeakerEnabled: boolean;
  isScreenSharing: boolean;
  isRecording: boolean;
}

const { width, height } = Dimensions.get('window');

export default function CallScreen({
  callSession,
  currentUser,
  localStream,
  remoteStream,
  onEndCall,
  onToggleVideo,
  onToggleAudio,
  onToggleSpeaker,
  onStartScreenShare,
  onStopScreenShare,
  onStartRecording,
  onStopRecording,
  isVideoEnabled,
  isAudioEnabled,
  isSpeakerEnabled,
  isScreenSharing,
  isRecording,
}: CallScreenProps) {
  const [callDuration, setCallDuration] = useState(0);
  const [showControls, setShowControls] = useState(true);
  const localVideoRef = useRef<HTMLVideoElement>(null);
  const remoteVideoRef = useRef<HTMLVideoElement>(null);
  const controlsTimeoutRef = useRef<NodeJS.Timeout>();

  const otherUser = callSession.participants.find(p => p.id !== currentUser.id) || callSession.participants[0];

  useEffect(() => {
    if (callSession.status === 'connected' && callSession.startTime) {
      const interval = setInterval(() => {
        const now = new Date();
        const duration = Math.floor((now.getTime() - callSession.startTime!.getTime()) / 1000);
        setCallDuration(duration);
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [callSession.status, callSession.startTime]);

  useEffect(() => {
    if (localStream && localVideoRef.current) {
      localVideoRef.current.srcObject = localStream;
    }
  }, [localStream]);

  useEffect(() => {
    if (remoteStream && remoteVideoRef.current) {
      remoteVideoRef.current.srcObject = remoteStream;
    }
  }, [remoteStream]);

  useEffect(() => {
    if (showControls) {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
      controlsTimeoutRef.current = setTimeout(() => {
        setShowControls(false);
      }, 5000);
    }

    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
    };
  }, [showControls]);

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleScreenTap = () => {
    setShowControls(!showControls);
  };

  const handleScreenShare = () => {
    if (isScreenSharing) {
      onStopScreenShare();
    } else {
      onStartScreenShare();
    }
  };

  const handleRecording = () => {
    if (isRecording) {
      Alert.alert(
        'Stop Recording',
        'Are you sure you want to stop recording?',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Stop', style: 'destructive', onPress: onStopRecording },
        ]
      );
    } else {
      Alert.alert(
        'Start Recording',
        'This call will be recorded. All participants will be notified.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Start Recording', onPress: onStartRecording },
        ]
      );
    }
  };

  const renderVideoCall = () => (
    <TouchableOpacity style={styles.videoContainer} onPress={handleScreenTap} activeOpacity={1}>
      {/* Remote Video */}
      <View style={styles.remoteVideoContainer}>
        {remoteStream ? (
          <video
            ref={remoteVideoRef}
            style={styles.remoteVideo}
            autoPlay
            playsInline
          />
        ) : (
          <View style={styles.avatarContainer}>
            <Image source={{ uri: otherUser.avatar }} style={styles.largeAvatar} />
            <Text style={styles.userName}>{otherUser.name}</Text>
          </View>
        )}
      </View>

      {/* Local Video */}
      {isVideoEnabled && localStream && (
        <View style={styles.localVideoContainer}>
          <video
            ref={localVideoRef}
            style={styles.localVideo}
            autoPlay
            playsInline
            muted
          />
        </View>
      )}

      {/* Call Info Overlay */}
      {showControls && (
        <View style={styles.callInfoOverlay}>
          <Text style={styles.callStatus}>
            {callSession.status === 'connected' ? formatDuration(callDuration) : 'Connecting...'}
          </Text>
          {isRecording && (
            <View style={styles.recordingIndicator}>
              <View style={styles.recordingDot} />
              <Text style={styles.recordingText}>Recording</Text>
            </View>
          )}
        </View>
      )}
    </TouchableOpacity>
  );

  const renderAudioCall = () => (
    <TouchableOpacity style={styles.audioContainer} onPress={handleScreenTap} activeOpacity={1}>
      <LinearGradient colors={['#8B5CF6', '#EC4899']} style={styles.audioGradient}>
        <View style={styles.audioContent}>
          <Image source={{ uri: otherUser.avatar }} style={styles.largeAvatar} />
          <Text style={styles.userName}>{otherUser.name}</Text>
          <Text style={styles.callStatus}>
            {callSession.status === 'connected' ? formatDuration(callDuration) : 'Connecting...'}
          </Text>
          {isRecording && (
            <View style={styles.recordingIndicator}>
              <View style={styles.recordingDot} />
              <Text style={styles.recordingText}>Recording</Text>
            </View>
          )}
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {callSession.type === 'video' ? renderVideoCall() : renderAudioCall()}

      {/* Controls */}
      {showControls && (
        <View style={styles.controlsContainer}>
          <View style={styles.controls}>
            {/* Audio Toggle */}
            <TouchableOpacity
              style={[styles.controlButton, !isAudioEnabled && styles.controlButtonDisabled]}
              onPress={onToggleAudio}
            >
              {isAudioEnabled ? (
                <Mic size={24} color="white" />
              ) : (
                <MicOff size={24} color="white" />
              )}
            </TouchableOpacity>

            {/* Video Toggle (only for video calls) */}
            {callSession.type === 'video' && (
              <TouchableOpacity
                style={[styles.controlButton, !isVideoEnabled && styles.controlButtonDisabled]}
                onPress={onToggleVideo}
              >
                {isVideoEnabled ? (
                  <Video size={24} color="white" />
                ) : (
                  <VideoOff size={24} color="white" />
                )}
              </TouchableOpacity>
            )}

            {/* Speaker Toggle */}
            <TouchableOpacity
              style={[styles.controlButton, isSpeakerEnabled && styles.controlButtonActive]}
              onPress={onToggleSpeaker}
            >
              <Speaker size={24} color="white" />
            </TouchableOpacity>

            {/* Screen Share (only for video calls) */}
            {callSession.type === 'video' && (
              <TouchableOpacity
                style={[styles.controlButton, isScreenSharing && styles.controlButtonActive]}
                onPress={handleScreenShare}
              >
                <Monitor size={24} color="white" />
              </TouchableOpacity>
            )}

            {/* Recording */}
            <TouchableOpacity
              style={[styles.controlButton, isRecording && styles.controlButtonRecording]}
              onPress={handleRecording}
            >
              <Record size={24} color="white" />
            </TouchableOpacity>

            {/* End Call */}
            <TouchableOpacity style={styles.endCallButton} onPress={onEndCall}>
              <PhoneOff size={24} color="white" />
            </TouchableOpacity>
          </View>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  videoContainer: {
    flex: 1,
    position: 'relative',
  },
  remoteVideoContainer: {
    flex: 1,
    backgroundColor: '#1F2937',
  },
  remoteVideo: {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
  },
  localVideoContainer: {
    position: 'absolute',
    top: 60,
    right: 20,
    width: 120,
    height: 160,
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: 'white',
  },
  localVideo: {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
    transform: 'scaleX(-1)', // Mirror effect
  },
  audioContainer: {
    flex: 1,
  },
  audioGradient: {
    flex: 1,
  },
  audioContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  avatarContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1F2937',
  },
  largeAvatar: {
    width: 150,
    height: 150,
    borderRadius: 75,
    marginBottom: 24,
  },
  userName: {
    fontSize: 28,
    fontFamily: 'Poppins-Bold',
    color: 'white',
    marginBottom: 8,
    textAlign: 'center',
  },
  callStatus: {
    fontSize: 18,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  callInfoOverlay: {
    position: 'absolute',
    top: 60,
    left: 20,
    alignItems: 'flex-start',
  },
  recordingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(239, 68, 68, 0.9)',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginTop: 8,
  },
  recordingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'white',
    marginRight: 6,
  },
  recordingText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  controlsContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingBottom: 40,
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    gap: 20,
  },
  controlButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  controlButtonDisabled: {
    backgroundColor: 'rgba(239, 68, 68, 0.8)',
  },
  controlButtonActive: {
    backgroundColor: 'rgba(139, 92, 246, 0.8)',
  },
  controlButtonRecording: {
    backgroundColor: 'rgba(239, 68, 68, 0.8)',
  },
  endCallButton: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#EF4444',
    justifyContent: 'center',
    alignItems: 'center',
  },
});