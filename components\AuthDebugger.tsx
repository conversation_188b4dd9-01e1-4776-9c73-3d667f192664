import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { useAuth } from '@/contexts/AuthContext';

export default function AuthDebugger() {
  const { user, isLoading, signOut } = useAuth();

  const showAuthStatus = () => {
    Alert.alert(
      'Auth Status',
      `User: ${user ? `${user.firstName} ${user.lastName} (${user.email})` : 'Not logged in'}\nLoading: ${isLoading}`
    );
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      Alert.alert('Success', 'Signed out successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to sign out');
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Auth Debugger</Text>
      
      <TouchableOpacity style={styles.button} onPress={showAuthStatus}>
        <Text style={styles.buttonText}>Show Auth Status</Text>
      </TouchableOpacity>

      {user && (
        <TouchableOpacity style={[styles.button, styles.signOutButton]} onPress={handleSignOut}>
          <Text style={styles.buttonText}>Sign Out</Text>
        </TouchableOpacity>
      )}

      <View style={styles.status}>
        <Text style={styles.statusText}>
          Status: {isLoading ? 'Loading...' : user ? 'Authenticated' : 'Not Authenticated'}
        </Text>
        {user && (
          <Text style={styles.statusText}>
            User: {user.firstName} {user.lastName}
          </Text>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 50,
    right: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    padding: 10,
    borderRadius: 8,
    zIndex: 1000,
  },
  title: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  button: {
    backgroundColor: '#8B5CF6',
    padding: 8,
    borderRadius: 4,
    marginBottom: 4,
  },
  signOutButton: {
    backgroundColor: '#EF4444',
  },
  buttonText: {
    color: 'white',
    fontSize: 10,
    textAlign: 'center',
  },
  status: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.3)',
  },
  statusText: {
    color: 'white',
    fontSize: 10,
    marginBottom: 2,
  },
});
