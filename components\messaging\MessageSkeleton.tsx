import React, { useEffect } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import { theme } from '@/constants/theme';

interface MessageSkeletonProps {
  isOwn?: boolean;
  showAvatar?: boolean;
  messageCount?: number;
}

const { width } = Dimensions.get('window');
const MAX_BUBBLE_WIDTH = width * 0.75;

export default function MessageSkeleton({ 
  isOwn = false, 
  showAvatar = true,
  messageCount = 3 
}: MessageSkeletonProps) {
  const shimmerValue = useSharedValue(0);

  useEffect(() => {
    shimmerValue.value = withRepeat(
      withTiming(1, { duration: 1500 }),
      -1,
      true
    );
  }, []);

  const shimmerStyle = useAnimatedStyle(() => {
    const opacity = interpolate(
      shimmerValue.value,
      [0, 0.5, 1],
      [0.3, 0.7, 0.3]
    );
    return { opacity };
  });

  const renderMessageBubble = (index: number) => {
    const isLast = index === messageCount - 1;
    const bubbleWidth = Math.random() * (MAX_BUBBLE_WIDTH * 0.4) + (MAX_BUBBLE_WIDTH * 0.3);
    
    return (
      <View
        key={index}
        style={[
          styles.messageContainer,
          isOwn ? styles.ownMessageContainer : styles.otherMessageContainer,
        ]}
      >
        {!isOwn && showAvatar && isLast && (
          <Animated.View style={[styles.avatarSkeleton, shimmerStyle]} />
        )}
        {!isOwn && showAvatar && !isLast && <View style={styles.avatarSpacer} />}
        
        <Animated.View
          style={[
            styles.bubbleSkeleton,
            isOwn ? styles.ownBubbleSkeleton : styles.otherBubbleSkeleton,
            { width: bubbleWidth },
            shimmerStyle,
          ]}
        >
          <Animated.View style={[styles.textLineSkeleton, shimmerStyle]} />
          {Math.random() > 0.5 && (
            <Animated.View 
              style={[
                styles.textLineSkeleton, 
                styles.shortTextLine,
                shimmerStyle
              ]} 
            />
          )}
        </Animated.View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {Array.from({ length: messageCount }, (_, index) => renderMessageBubble(index))}
      
      {/* Typing indicator skeleton */}
      <View style={styles.typingContainer}>
        <Animated.View style={[styles.avatarSkeleton, styles.smallAvatar, shimmerStyle]} />
        <Animated.View style={[styles.typingBubble, shimmerStyle]}>
          <View style={styles.typingDots}>
            <Animated.View style={[styles.typingDot, shimmerStyle]} />
            <Animated.View style={[styles.typingDot, shimmerStyle]} />
            <Animated.View style={[styles.typingDot, shimmerStyle]} />
          </View>
        </Animated.View>
      </View>
    </View>
  );
}

export function ChatListSkeleton({ itemCount = 5 }: { itemCount?: number }) {
  const shimmerValue = useSharedValue(0);

  useEffect(() => {
    shimmerValue.value = withRepeat(
      withTiming(1, { duration: 1500 }),
      -1,
      true
    );
  }, []);

  const shimmerStyle = useAnimatedStyle(() => {
    const opacity = interpolate(
      shimmerValue.value,
      [0, 0.5, 1],
      [0.3, 0.7, 0.3]
    );
    return { opacity };
  });

  const renderChatItem = (index: number) => (
    <View key={index} style={styles.chatItemContainer}>
      <Animated.View style={[styles.avatarSkeleton, shimmerStyle]} />
      <View style={styles.chatItemContent}>
        <View style={styles.chatItemHeader}>
          <Animated.View style={[styles.nameSkeleton, shimmerStyle]} />
          <Animated.View style={[styles.timeSkeleton, shimmerStyle]} />
        </View>
        <Animated.View style={[styles.messageSkeleton, shimmerStyle]} />
      </View>
    </View>
  );

  return (
    <View style={styles.chatListContainer}>
      {Array.from({ length: itemCount }, (_, index) => renderChatItem(index))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  messageContainer: {
    flexDirection: 'row',
    marginBottom: 12,
    alignItems: 'flex-end',
  },
  ownMessageContainer: {
    justifyContent: 'flex-end',
  },
  otherMessageContainer: {
    justifyContent: 'flex-start',
  },
  avatarSkeleton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: theme.colors.gray200,
    marginRight: 8,
  },
  avatarSpacer: {
    width: 40,
  },
  bubbleSkeleton: {
    backgroundColor: theme.colors.gray200,
    borderRadius: 18,
    paddingHorizontal: 16,
    paddingVertical: 12,
    minHeight: 44,
    justifyContent: 'center',
  },
  ownBubbleSkeleton: {
    backgroundColor: theme.colors.primary + '40',
  },
  otherBubbleSkeleton: {
    backgroundColor: theme.colors.gray200,
  },
  textLineSkeleton: {
    height: 14,
    backgroundColor: theme.colors.gray300,
    borderRadius: 7,
    marginBottom: 4,
  },
  shortTextLine: {
    width: '60%',
    marginBottom: 0,
  },
  typingContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginTop: 8,
  },
  smallAvatar: {
    width: 24,
    height: 24,
    borderRadius: 12,
  },
  typingBubble: {
    backgroundColor: theme.colors.gray200,
    borderRadius: 18,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginLeft: 8,
  },
  typingDots: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typingDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: theme.colors.gray400,
    marginRight: 4,
  },
  // Chat List Skeleton Styles
  chatListContainer: {
    paddingHorizontal: 16,
  },
  chatItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray100,
  },
  chatItemContent: {
    flex: 1,
    marginLeft: 12,
  },
  chatItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  nameSkeleton: {
    width: 120,
    height: 16,
    backgroundColor: theme.colors.gray200,
    borderRadius: 8,
  },
  timeSkeleton: {
    width: 40,
    height: 12,
    backgroundColor: theme.colors.gray200,
    borderRadius: 6,
  },
  messageSkeleton: {
    width: '80%',
    height: 14,
    backgroundColor: theme.colors.gray200,
    borderRadius: 7,
  },
});
