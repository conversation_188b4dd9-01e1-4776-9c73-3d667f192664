{"name": "soulsync-dating-app", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"start": "cross-env EXPO_NO_TELEMETRY=1 npx expo start", "dev": "cross-env EXPO_NO_TELEMETRY=1 npx expo start", "build:web": "expo export --platform web", "lint": "expo lint", "type-check": "tsc --noEmit"}, "dependencies": {"@expo-google-fonts/inter": "^0.2.3", "@expo-google-fonts/poppins": "^0.2.3", "@expo/vector-icons": "^14.0.2", "@lucide/lab": "^0.1.2", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/native": "^7.1.14", "expo": "~52.0.30", "expo-av": "~14.0.7", "expo-blur": "~14.0.3", "expo-camera": "~15.0.16", "expo-constants": "~16.0.2", "expo-device": "~7.0.3", "expo-document-picker": "~12.0.2", "expo-font": "~12.0.10", "expo-haptics": "~13.0.1", "expo-image-picker": "~15.0.7", "expo-linear-gradient": "~13.0.2", "expo-linking": "~6.3.1", "expo-location": "~17.0.1", "expo-notifications": "^0.31.3", "expo-router": "~4.0.17", "expo-secure-store": "^13.0.2", "expo-sharing": "^13.1.5", "expo-splash-screen": "~0.29.13", "expo-status-bar": "~2.0.0", "expo-symbols": "~0.1.2", "expo-system-ui": "~4.0.4", "expo-web-browser": "~14.0.1", "lucide-react-native": "^0.475.0", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.76.5", "react-native-gesture-handler": "~2.20.2", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~3.32.0", "react-native-svg": "15.8.0", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.19.12", "react-native-webview": "13.12.2", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~18.2.79", "@types/react-native": "^0.73.0", "cross-env": "^7.0.3", "typescript": "~5.3.3"}}