import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import {
  BarChart3,
  Users,
  Shield,
  AlertTriangle,
  Heart,
  MessageCircle,
  TrendingUp,
  Settings,
} from 'lucide-react-native';
import { theme } from '@/constants/theme';

const { width } = Dimensions.get('window');
const CARD_WIDTH = (width - 48) / 2;

interface AdminCard {
  title: string;
  subtitle: string;
  icon: any;
  route: string;
  color: string;
  value?: string;
}

const ADMIN_CARDS: AdminCard[] = [
  {
    title: 'Analytics',
    subtitle: 'View app metrics',
    icon: BarChart3,
    route: '/admin/analytics',
    color: theme.colors.primary,
    value: '↗ 12%',
  },
  {
    title: 'User Management',
    subtitle: 'Manage users',
    icon: Users,
    route: '/admin/users',
    color: theme.colors.info,
    value: '1,234',
  },
  {
    title: 'Moderation',
    subtitle: 'Content moderation',
    icon: Shield,
    route: '/admin/moderation',
    color: theme.colors.warning,
    value: '23 pending',
  },
  {
    title: 'Reports',
    subtitle: 'User reports',
    icon: AlertTriangle,
    route: '/admin/reports',
    color: theme.colors.error,
    value: '5 new',
  },
];

const QUICK_STATS = [
  { label: 'Total Likes Today', value: '2,847', icon: Heart, color: theme.colors.like },
  { label: 'New Matches', value: '156', icon: MessageCircle, color: theme.colors.primary },
  { label: 'Active Users', value: '892', icon: Users, color: theme.colors.info },
  { label: 'Growth Rate', value: '+8.2%', icon: TrendingUp, color: theme.colors.success },
];

export default function AdminDashboard() {
  const router = useRouter();

  const handleCardPress = (route: string) => {
    router.push(route as any);
  };

  const renderAdminCard = (card: AdminCard) => (
    <TouchableOpacity
      key={card.title}
      style={styles.card}
      onPress={() => handleCardPress(card.route)}
    >
      <LinearGradient
        colors={[card.color, `${card.color}CC`]}
        style={styles.cardGradient}
      >
        <View style={styles.cardHeader}>
          <card.icon size={24} color="white" />
          {card.value && (
            <Text style={styles.cardValue}>{card.value}</Text>
          )}
        </View>
        <Text style={styles.cardTitle}>{card.title}</Text>
        <Text style={styles.cardSubtitle}>{card.subtitle}</Text>
      </LinearGradient>
    </TouchableOpacity>
  );

  const renderQuickStat = (stat: any, index: number) => (
    <View key={index} style={styles.statCard}>
      <View style={[styles.statIcon, { backgroundColor: `${stat.color}20` }]}>
        <stat.icon size={20} color={stat.color} />
      </View>
      <View style={styles.statContent}>
        <Text style={styles.statValue}>{stat.value}</Text>
        <Text style={styles.statLabel}>{stat.label}</Text>
      </View>
    </View>
  );

  return (
    <LinearGradient colors={['#8B5CF6', '#EC4899']} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.header}>
          <Text style={styles.title}>Admin Dashboard</Text>
          <TouchableOpacity style={styles.settingsButton}>
            <Settings size={24} color="white" />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Quick Stats */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Quick Stats</Text>
            <View style={styles.statsGrid}>
              {QUICK_STATS.map(renderQuickStat)}
            </View>
          </View>

          {/* Admin Cards */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Management</Text>
            <View style={styles.cardsGrid}>
              {ADMIN_CARDS.map(renderAdminCard)}
            </View>
          </View>

          {/* Recent Activity */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Recent Activity</Text>
            <View style={styles.activityCard}>
              <Text style={styles.activityText}>
                • 23 new user registrations in the last hour{'\n'}
                • 156 matches created today{'\n'}
                • 5 reports require attention{'\n'}
                • Server uptime: 99.9%
              </Text>
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Poppins-Bold',
    color: 'white',
  },
  settingsButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
    marginBottom: 16,
  },
  statsGrid: {
    gap: 12,
  },
  statCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    ...theme.shadows.sm,
  },
  statIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  statContent: {
    flex: 1,
  },
  statValue: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: 'white',
  },
  statLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  cardsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 16,
  },
  card: {
    width: CARD_WIDTH,
    height: 120,
    borderRadius: 16,
    overflow: 'hidden',
    ...theme.shadows.md,
  },
  cardGradient: {
    flex: 1,
    padding: 16,
    justifyContent: 'space-between',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  cardValue: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  cardTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  cardSubtitle: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
  },
  activityCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    ...theme.shadows.sm,
  },
  activityText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.9)',
    lineHeight: 20,
  },
});
