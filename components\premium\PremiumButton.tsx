import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Crown } from 'lucide-react-native';
import { theme } from '@/constants/theme';
import * as Haptics from 'expo-haptics';

interface PremiumButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  icon?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export default function PremiumButton({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  icon = true,
  style,
  textStyle,
}: PremiumButtonProps) {
  const handlePress = () => {
    if (disabled || loading) return;
    
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    onPress();
  };

  const getButtonStyle = () => {
    const baseStyle = [styles.button, styles[`${size}Button`]];
    
    if (variant === 'outline') {
      baseStyle.push(styles.outlineButton);
    }
    
    if (disabled) {
      baseStyle.push(styles.disabledButton);
    }
    
    return [...baseStyle, style];
  };

  const getTextStyle = () => {
    const baseStyle = [styles.text, styles[`${size}Text`]];
    
    if (variant === 'outline') {
      baseStyle.push(styles.outlineText);
    }
    
    if (disabled) {
      baseStyle.push(styles.disabledText);
    }
    
    return [...baseStyle, textStyle];
  };

  const renderContent = () => (
    <>
      {icon && <Crown size={size === 'small' ? 16 : size === 'large' ? 24 : 20} color="white" />}
      <Text style={getTextStyle()}>{loading ? 'Processing...' : title}</Text>
    </>
  );

  if (variant === 'primary') {
    return (
      <TouchableOpacity
        style={getButtonStyle()}
        onPress={handlePress}
        activeOpacity={0.8}
        disabled={disabled || loading}
      >
        <LinearGradient
          colors={disabled ? ['#9CA3AF', '#6B7280'] : ['#8B5CF6', '#EC4899']}
          style={styles.gradient}
        >
          {renderContent()}
        </LinearGradient>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      style={getButtonStyle()}
      onPress={handlePress}
      activeOpacity={0.8}
      disabled={disabled || loading}
    >
      {renderContent()}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  button: {
    borderRadius: theme.borderRadius.xl,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  smallButton: {
    minHeight: 40,
  },
  mediumButton: {
    minHeight: 56,
  },
  largeButton: {
    minHeight: 64,
  },
  outlineButton: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: theme.colors.primary,
  },
  disabledButton: {
    opacity: 0.6,
  },
  gradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: theme.spacing.xl,
    paddingVertical: theme.spacing.lg,
    gap: theme.spacing.sm,
    minHeight: '100%',
  },
  text: {
    color: 'white',
    fontWeight: theme.fontWeight.bold,
    letterSpacing: 0.5,
  },
  smallText: {
    fontSize: theme.fontSize.sm,
  },
  mediumText: {
    fontSize: theme.fontSize.base,
  },
  largeText: {
    fontSize: theme.fontSize.lg,
  },
  outlineText: {
    color: theme.colors.primary,
  },
  disabledText: {
    opacity: 0.7,
  },
});
