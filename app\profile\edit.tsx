import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  TextInput,
  Alert,
  Platform,
  KeyboardAvoidingView,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useProfileStore } from '@/stores/profileStore';
import { theme } from '@/constants/theme';
import * as Haptics from 'expo-haptics';
import {
  ArrowLeft,
  Save,
  User,
  MapPin,
  Briefcase,
  GraduationCap,
  Calendar,
  Heart,
  Plus,
  X,
} from 'lucide-react-native';

export default function EditProfileScreen() {
  const router = useRouter();
  const { profile, updateProfile, isUpdating, error } = useProfileStore();

  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    bio: '',
    occupation: '',
    education: '',
    city: '',
    state: '',
    interests: [] as string[],
    relationshipGoals: 'serious' as 'casual' | 'serious' | 'friendship' | 'unsure',
  });

  const [newInterest, setNewInterest] = useState('');
  const [bioCharCount, setBioCharCount] = useState(0);

  useEffect(() => {
    if (profile) {
      setFormData({
        firstName: profile.firstName,
        lastName: profile.lastName,
        bio: profile.bio,
        occupation: profile.occupation,
        education: profile.education,
        city: profile.location.city,
        state: profile.location.state,
        interests: [...profile.interests],
        relationshipGoals: profile.relationshipGoals,
      });
      setBioCharCount(profile.bio.length);
    }
  }, [profile]);

  const handleSave = async () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }

    if (!formData.firstName.trim() || !formData.lastName.trim()) {
      Alert.alert('Error', 'First name and last name are required');
      return;
    }

    if (formData.bio.length < 10) {
      Alert.alert('Error', 'Bio must be at least 10 characters long');
      return;
    }

    if (formData.interests.length < 3) {
      Alert.alert('Error', 'Please add at least 3 interests');
      return;
    }

    try {
      await updateProfile({
        firstName: formData.firstName.trim(),
        lastName: formData.lastName.trim(),
        name: `${formData.firstName.trim()} ${formData.lastName.trim()}`,
        bio: formData.bio.trim(),
        occupation: formData.occupation.trim(),
        education: formData.education.trim(),
        location: {
          ...profile?.location,
          city: formData.city.trim(),
          state: formData.state.trim(),
        },
        interests: formData.interests,
        relationshipGoals: formData.relationshipGoals,
      });

      Alert.alert('Success', 'Profile updated successfully!', [
        { text: 'OK', onPress: () => router.back() }
      ]);
    } catch (error) {
      Alert.alert('Error', 'Failed to update profile. Please try again.');
    }
  };

  const handleBioChange = (text: string) => {
    if (text.length <= 500) {
      setFormData(prev => ({ ...prev, bio: text }));
      setBioCharCount(text.length);
    }
  };

  const addInterest = () => {
    if (newInterest.trim() && !formData.interests.includes(newInterest.trim())) {
      if (formData.interests.length < 10) {
        setFormData(prev => ({
          ...prev,
          interests: [...prev.interests, newInterest.trim()]
        }));
        setNewInterest('');
        
        if (Platform.OS !== 'web') {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        }
      } else {
        Alert.alert('Limit Reached', 'You can add up to 10 interests');
      }
    }
  };

  const removeInterest = (interest: string) => {
    setFormData(prev => ({
      ...prev,
      interests: prev.interests.filter(i => i !== interest)
    }));
    
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  const relationshipGoalOptions = [
    { value: 'casual', label: 'Casual Dating' },
    { value: 'serious', label: 'Serious Relationship' },
    { value: 'friendship', label: 'Friendship' },
    { value: 'unsure', label: 'Not Sure Yet' },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView 
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <ArrowLeft size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Edit Profile</Text>
          <TouchableOpacity 
            style={[styles.saveButton, isUpdating && styles.saveButtonDisabled]} 
            onPress={handleSave}
            disabled={isUpdating}
          >
            <Save size={20} color="white" />
            <Text style={styles.saveButtonText}>
              {isUpdating ? 'Saving...' : 'Save'}
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Basic Info Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Basic Information</Text>
            
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>First Name</Text>
              <TextInput
                style={styles.textInput}
                value={formData.firstName}
                onChangeText={(text) => setFormData(prev => ({ ...prev, firstName: text }))}
                placeholder="Enter your first name"
                placeholderTextColor={theme.colors.gray400}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Last Name</Text>
              <TextInput
                style={styles.textInput}
                value={formData.lastName}
                onChangeText={(text) => setFormData(prev => ({ ...prev, lastName: text }))}
                placeholder="Enter your last name"
                placeholderTextColor={theme.colors.gray400}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Bio</Text>
              <TextInput
                style={[styles.textInput, styles.bioInput]}
                value={formData.bio}
                onChangeText={handleBioChange}
                placeholder="Tell others about yourself..."
                placeholderTextColor={theme.colors.gray400}
                multiline
                numberOfLines={4}
                textAlignVertical="top"
              />
              <Text style={styles.charCount}>{bioCharCount}/500</Text>
            </View>
          </View>

          {/* Professional Info Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Professional Information</Text>
            
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Occupation</Text>
              <TextInput
                style={styles.textInput}
                value={formData.occupation}
                onChangeText={(text) => setFormData(prev => ({ ...prev, occupation: text }))}
                placeholder="What do you do for work?"
                placeholderTextColor={theme.colors.gray400}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Education</Text>
              <TextInput
                style={styles.textInput}
                value={formData.education}
                onChangeText={(text) => setFormData(prev => ({ ...prev, education: text }))}
                placeholder="Where did you study?"
                placeholderTextColor={theme.colors.gray400}
              />
            </View>
          </View>

          {/* Location Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Location</Text>
            
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>City</Text>
              <TextInput
                style={styles.textInput}
                value={formData.city}
                onChangeText={(text) => setFormData(prev => ({ ...prev, city: text }))}
                placeholder="Enter your city"
                placeholderTextColor={theme.colors.gray400}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>State</Text>
              <TextInput
                style={styles.textInput}
                value={formData.state}
                onChangeText={(text) => setFormData(prev => ({ ...prev, state: text }))}
                placeholder="Enter your state"
                placeholderTextColor={theme.colors.gray400}
              />
            </View>
          </View>

          {/* Interests Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Interests ({formData.interests.length}/10)</Text>
            
            <View style={styles.interestsContainer}>
              {formData.interests.map((interest, index) => (
                <View key={index} style={styles.interestTag}>
                  <Text style={styles.interestText}>{interest}</Text>
                  <TouchableOpacity onPress={() => removeInterest(interest)}>
                    <X size={16} color={theme.colors.primary} />
                  </TouchableOpacity>
                </View>
              ))}
            </View>

            <View style={styles.addInterestContainer}>
              <TextInput
                style={[styles.textInput, styles.addInterestInput]}
                value={newInterest}
                onChangeText={setNewInterest}
                placeholder="Add an interest..."
                placeholderTextColor={theme.colors.gray400}
                onSubmitEditing={addInterest}
                returnKeyType="done"
              />
              <TouchableOpacity style={styles.addInterestButton} onPress={addInterest}>
                <Plus size={20} color="white" />
              </TouchableOpacity>
            </View>
          </View>

          {/* Relationship Goals Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Looking For</Text>
            
            <View style={styles.goalOptionsContainer}>
              {relationshipGoalOptions.map((option) => (
                <TouchableOpacity
                  key={option.value}
                  style={[
                    styles.goalOption,
                    formData.relationshipGoals === option.value && styles.goalOptionSelected
                  ]}
                  onPress={() => {
                    setFormData(prev => ({ ...prev, relationshipGoals: option.value as any }));
                    if (Platform.OS !== 'web') {
                      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                    }
                  }}
                >
                  <Text style={[
                    styles.goalOptionText,
                    formData.relationshipGoals === option.value && styles.goalOptionTextSelected
                  ]}>
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  keyboardView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray200,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 6,
  },
  saveButtonDisabled: {
    opacity: 0.6,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    marginVertical: 10,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: theme.colors.gray700,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: theme.colors.gray300,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text,
    backgroundColor: theme.colors.gray50,
  },
  bioInput: {
    height: 100,
    textAlignVertical: 'top',
  },
  charCount: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray500,
    textAlign: 'right',
    marginTop: 4,
  },
  interestsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  interestTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.primary + '20',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 6,
  },
  interestText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: theme.colors.primary,
  },
  addInterestContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  addInterestInput: {
    flex: 1,
  },
  addInterestButton: {
    backgroundColor: theme.colors.primary,
    width: 44,
    height: 44,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  goalOptionsContainer: {
    gap: 8,
  },
  goalOption: {
    borderWidth: 1,
    borderColor: theme.colors.gray300,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: theme.colors.gray50,
  },
  goalOptionSelected: {
    borderColor: theme.colors.primary,
    backgroundColor: theme.colors.primary + '20',
  },
  goalOptionText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray700,
    textAlign: 'center',
  },
  goalOptionTextSelected: {
    color: theme.colors.primary,
    fontFamily: 'Inter-SemiBold',
  },
});
