import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Platform,
} from 'react-native';
import { theme } from '../../constants/theme';
import { triggerHaptic } from '../../utils/haptics';

interface TimePickerProps {
  initialTime?: string; // Format: "HH:MM"
  onTimeChange?: (time: string) => void;
  format24Hour?: boolean;
  title?: string;
  disabled?: boolean;
}

export default function TimePicker({
  initialTime = '12:00',
  onTimeChange,
  format24Hour = false,
  title = 'Select Time',
  disabled = false,
}: TimePickerProps) {
  const [selectedTime, setSelectedTime] = useState(initialTime);

  const handleHaptic = () => {
    triggerHaptic.light();
  };

  const parseTime = (timeString: string) => {
    const [hours, minutes] = timeString.split(':').map(Number);
    return { hours, minutes };
  };

  const formatTime = (hours: number, minutes: number) => {
    const h = hours.toString().padStart(2, '0');
    const m = minutes.toString().padStart(2, '0');
    return `${h}:${m}`;
  };

  const formatDisplayTime = (hours: number, minutes: number) => {
    if (format24Hour) {
      return formatTime(hours, minutes);
    } else {
      const period = hours >= 12 ? 'PM' : 'AM';
      const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
      return `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`;
    }
  };

  const updateTime = (hours: number, minutes: number) => {
    const newTime = formatTime(hours, minutes);
    setSelectedTime(newTime);
    onTimeChange?.(newTime);
    handleHaptic();
  };

  const generateHours = () => {
    const hours = [];
    const maxHours = format24Hour ? 23 : 12;
    const startHour = format24Hour ? 0 : 1;
    
    for (let i = startHour; i <= maxHours; i++) {
      hours.push(i);
    }
    return hours;
  };

  const generateMinutes = () => {
    const minutes = [];
    for (let i = 0; i < 60; i += 5) {
      minutes.push(i);
    }
    return minutes;
  };

  const generatePeriods = () => {
    return format24Hour ? [] : ['AM', 'PM'];
  };

  const { hours: currentHours, minutes: currentMinutes } = parseTime(selectedTime);
  const currentPeriod = format24Hour ? '' : (currentHours >= 12 ? 'PM' : 'AM');
  const displayHours = format24Hour ? currentHours : (currentHours === 0 ? 12 : currentHours > 12 ? currentHours - 12 : currentHours);

  const hours = generateHours();
  const minutes = generateMinutes();
  const periods = generatePeriods();

  const handleHourChange = (hour: number) => {
    let newHours = hour;
    if (!format24Hour) {
      if (currentPeriod === 'PM' && hour !== 12) {
        newHours = hour + 12;
      } else if (currentPeriod === 'AM' && hour === 12) {
        newHours = 0;
      }
    }
    updateTime(newHours, currentMinutes);
  };

  const handleMinuteChange = (minute: number) => {
    updateTime(currentHours, minute);
  };

  const handlePeriodChange = (period: string) => {
    let newHours = currentHours;
    if (period === 'PM' && currentHours < 12) {
      newHours = currentHours + 12;
    } else if (period === 'AM' && currentHours >= 12) {
      newHours = currentHours - 12;
    }
    updateTime(newHours, currentMinutes);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>{title}</Text>
      
      <View style={styles.currentTime}>
        <Text style={styles.currentTimeText}>
          {formatDisplayTime(currentHours, currentMinutes)}
        </Text>
      </View>

      <View style={styles.pickersContainer}>
        {/* Hours */}
        <View style={styles.pickerSection}>
          <Text style={styles.pickerTitle}>Hour</Text>
          <ScrollView 
            style={styles.picker}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.pickerContent}
          >
            {hours.map((hour) => (
              <TouchableOpacity
                key={hour}
                style={[
                  styles.pickerItem,
                  displayHours === hour && styles.pickerItemSelected,
                ]}
                onPress={() => handleHourChange(hour)}
                disabled={disabled}
              >
                <Text
                  style={[
                    styles.pickerItemText,
                    displayHours === hour && styles.pickerItemTextSelected,
                  ]}
                >
                  {hour.toString().padStart(2, '0')}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Minutes */}
        <View style={styles.pickerSection}>
          <Text style={styles.pickerTitle}>Minute</Text>
          <ScrollView 
            style={styles.picker}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.pickerContent}
          >
            {minutes.map((minute) => (
              <TouchableOpacity
                key={minute}
                style={[
                  styles.pickerItem,
                  currentMinutes === minute && styles.pickerItemSelected,
                ]}
                onPress={() => handleMinuteChange(minute)}
                disabled={disabled}
              >
                <Text
                  style={[
                    styles.pickerItemText,
                    currentMinutes === minute && styles.pickerItemTextSelected,
                  ]}
                >
                  {minute.toString().padStart(2, '0')}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Period (AM/PM) */}
        {!format24Hour && (
          <View style={styles.pickerSection}>
            <Text style={styles.pickerTitle}>Period</Text>
            <ScrollView 
              style={styles.picker}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.pickerContent}
            >
              {periods.map((period) => (
                <TouchableOpacity
                  key={period}
                  style={[
                    styles.pickerItem,
                    currentPeriod === period && styles.pickerItemSelected,
                  ]}
                  onPress={() => handlePeriodChange(period)}
                  disabled={disabled}
                >
                  <Text
                    style={[
                      styles.pickerItemText,
                      currentPeriod === period && styles.pickerItemTextSelected,
                    ]}
                  >
                    {period}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: 16,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 16,
    textAlign: 'center',
  },
  currentTime: {
    alignItems: 'center',
    marginBottom: 24,
    paddingVertical: 16,
    paddingHorizontal: 24,
    backgroundColor: `${theme.colors.primary}10`,
    borderRadius: 12,
  },
  currentTimeText: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: theme.colors.primary,
  },
  pickersContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  pickerSection: {
    flex: 1,
    marginHorizontal: 8,
  },
  pickerTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: 8,
  },
  picker: {
    height: 200,
    backgroundColor: theme.colors.gray50,
    borderRadius: 12,
  },
  pickerContent: {
    paddingVertical: 8,
  },
  pickerItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginVertical: 2,
    marginHorizontal: 8,
    borderRadius: 8,
    alignItems: 'center',
  },
  pickerItemSelected: {
    backgroundColor: theme.colors.primary,
  },
  pickerItemText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: theme.colors.text,
  },
  pickerItemTextSelected: {
    color: 'white',
  },
});
