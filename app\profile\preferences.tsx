import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Switch,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import * as Haptics from 'expo-haptics';
import {
  ArrowLeft,
  Globe,
  Palette,
  Type,
  Clock,
  DollarSign,
  Calendar,
  Play,
  Accessibility,
  Eye,
  Volume2,
  Smartphone,
  Settings,
} from 'lucide-react-native';

import { theme } from '../../constants/theme';
import { useProfileStore } from '../../stores/profileStore';
import LoadingSkeleton from '../../components/LoadingSkeleton';

export default function AppPreferencesScreen() {
  const router = useRouter();
  const { profile, settings, updateSettings, isLoading } = useProfileStore();
  
  const [localSettings, setLocalSettings] = useState({
    language: 'en',
    theme: 'system' as 'light' | 'dark' | 'system',
    units: 'metric' as 'metric' | 'imperial',
    currency: 'USD',
    dateFormat: 'MM/DD/YYYY' as 'MM/DD/YYYY' | 'DD/MM/YYYY' | 'YYYY-MM-DD',
    timeFormat: '12h' as '12h' | '24h',
    autoPlayVideos: true,
    reducedMotion: false,
    highContrast: false,
    fontSize: 'medium' as 'small' | 'medium' | 'large',
  });

  const languages = [
    { code: 'en', name: 'English' },
    { code: 'es', name: 'Español' },
    { code: 'fr', name: 'Français' },
    { code: 'de', name: 'Deutsch' },
    { code: 'it', name: 'Italiano' },
    { code: 'pt', name: 'Português' },
    { code: 'ru', name: 'Русский' },
    { code: 'ja', name: '日本語' },
    { code: 'ko', name: '한국어' },
    { code: 'zh', name: '中文' },
  ];

  const currencies = [
    { code: 'USD', name: 'US Dollar', symbol: '$' },
    { code: 'EUR', name: 'Euro', symbol: '€' },
    { code: 'GBP', name: 'British Pound', symbol: '£' },
    { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
    { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$' },
    { code: 'AUD', name: 'Australian Dollar', symbol: 'A$' },
  ];

  useEffect(() => {
    if (settings?.app) {
      setLocalSettings({
        language: settings.app.language,
        theme: settings.app.theme,
        units: settings.app.units,
        currency: settings.app.currency,
        dateFormat: settings.app.dateFormat,
        timeFormat: settings.app.timeFormat,
        autoPlayVideos: settings.app.autoPlayVideos,
        reducedMotion: settings.app.reducedMotion,
        highContrast: settings.app.highContrast,
        fontSize: settings.app.fontSize,
      });
    }
  }, [settings]);

  const handleBack = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    router.back();
  };

  const handleToggle = async (key: string, value: boolean) => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    setLocalSettings(prev => ({ ...prev, [key]: value }));

    try {
      const updatedSettings = {
        ...settings,
        app: {
          ...settings?.app,
          [key]: value,
        },
      };
      await updateSettings(updatedSettings);
    } catch (error) {
      console.error('Failed to update setting:', error);
      setLocalSettings(prev => ({ ...prev, [key]: !value }));
      Alert.alert('Error', 'Failed to update setting. Please try again.');
    }
  };

  const handleSelectOption = async (key: string, value: string) => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    setLocalSettings(prev => ({ ...prev, [key]: value }));

    try {
      const updatedSettings = {
        ...settings,
        app: {
          ...settings?.app,
          [key]: value,
        },
      };
      await updateSettings(updatedSettings);
    } catch (error) {
      console.error('Failed to update setting:', error);
      Alert.alert('Error', 'Failed to update setting. Please try again.');
    }
  };

  const handleLanguageChange = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    Alert.alert(
      'Select Language',
      'Choose your preferred language',
      [
        ...languages.map(lang => ({
          text: lang.name,
          onPress: () => handleSelectOption('language', lang.code),
        })),
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const handleThemeChange = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    const themes = [
      { label: 'Light', value: 'light' },
      { label: 'Dark', value: 'dark' },
      { label: 'System', value: 'system' },
    ];

    Alert.alert(
      'Select Theme',
      'Choose your preferred theme',
      [
        ...themes.map(theme => ({
          text: theme.label,
          onPress: () => handleSelectOption('theme', theme.value),
        })),
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const handleUnitsChange = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    Alert.alert(
      'Units of Measurement',
      'Choose your preferred units',
      [
        { text: 'Metric (km, kg, °C)', onPress: () => handleSelectOption('units', 'metric') },
        { text: 'Imperial (mi, lbs, °F)', onPress: () => handleSelectOption('units', 'imperial') },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const handleCurrencyChange = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    Alert.alert(
      'Select Currency',
      'Choose your preferred currency',
      [
        ...currencies.map(currency => ({
          text: `${currency.name} (${currency.symbol})`,
          onPress: () => handleSelectOption('currency', currency.code),
        })),
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const handleDateFormatChange = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    const formats = [
      { label: 'MM/DD/YYYY', value: 'MM/DD/YYYY' },
      { label: 'DD/MM/YYYY', value: 'DD/MM/YYYY' },
      { label: 'YYYY-MM-DD', value: 'YYYY-MM-DD' },
    ];

    Alert.alert(
      'Date Format',
      'Choose your preferred date format',
      [
        ...formats.map(format => ({
          text: format.label,
          onPress: () => handleSelectOption('dateFormat', format.value),
        })),
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const handleTimeFormatChange = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    Alert.alert(
      'Time Format',
      'Choose your preferred time format',
      [
        { text: '12-hour (AM/PM)', onPress: () => handleSelectOption('timeFormat', '12h') },
        { text: '24-hour', onPress: () => handleSelectOption('timeFormat', '24h') },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const handleFontSizeChange = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    Alert.alert(
      'Font Size',
      'Choose your preferred font size',
      [
        { text: 'Small', onPress: () => handleSelectOption('fontSize', 'small') },
        { text: 'Medium', onPress: () => handleSelectOption('fontSize', 'medium') },
        { text: 'Large', onPress: () => handleSelectOption('fontSize', 'large') },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  if (isLoading) {
    return (
      <LinearGradient colors={['#8B5CF6', '#EC4899']} style={styles.container}>
        <SafeAreaView style={styles.safeArea}>
          <LoadingSkeleton />
        </SafeAreaView>
      </LinearGradient>
    );
  }

  const SettingItem = ({ 
    icon: Icon, 
    title, 
    subtitle, 
    value, 
    onToggle, 
    type = 'switch',
    onPress,
    rightText,
  }: {
    icon: any;
    title: string;
    subtitle?: string;
    value?: boolean;
    onToggle?: (value: boolean) => void;
    type?: 'switch' | 'button';
    onPress?: () => void;
    rightText?: string;
  }) => (
    <TouchableOpacity 
      style={styles.settingItem} 
      onPress={type === 'button' ? onPress : undefined}
      disabled={type === 'switch'}
    >
      <View style={styles.settingLeft}>
        <View style={styles.settingIcon}>
          <Icon size={20} color={theme.colors.primary} />
        </View>
        <View style={styles.settingContent}>
          <Text style={styles.settingTitle}>{title}</Text>
          {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
        </View>
      </View>
      
      {type === 'switch' && onToggle && (
        <Switch
          value={value}
          onValueChange={onToggle}
          trackColor={{ false: theme.colors.gray300, true: theme.colors.primary + '40' }}
          thumbColor={value ? theme.colors.primary : theme.colors.gray400}
        />
      )}
      
      {type === 'button' && rightText && (
        <Text style={styles.rightText}>{rightText}</Text>
      )}
    </TouchableOpacity>
  );

  const getCurrentLanguageName = () => {
    return languages.find(lang => lang.code === localSettings.language)?.name || 'English';
  };

  const getCurrentCurrencyName = () => {
    const currency = currencies.find(curr => curr.code === localSettings.currency);
    return currency ? `${currency.symbol} ${currency.code}` : '$ USD';
  };

  return (
    <LinearGradient colors={['#8B5CF6', '#EC4899']} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <ArrowLeft size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>App Preferences</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Language & Region */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Language & Region</Text>
            
            <SettingItem
              icon={Globe}
              title="Language"
              subtitle="App display language"
              type="button"
              onPress={handleLanguageChange}
              rightText={getCurrentLanguageName()}
            />
            
            <SettingItem
              icon={Settings}
              title="Units"
              subtitle="Distance, weight, and temperature"
              type="button"
              onPress={handleUnitsChange}
              rightText={localSettings.units}
            />
            
            <SettingItem
              icon={DollarSign}
              title="Currency"
              subtitle="Pricing display currency"
              type="button"
              onPress={handleCurrencyChange}
              rightText={getCurrentCurrencyName()}
            />
            
            <SettingItem
              icon={Calendar}
              title="Date Format"
              subtitle="How dates are displayed"
              type="button"
              onPress={handleDateFormatChange}
              rightText={localSettings.dateFormat}
            />
            
            <SettingItem
              icon={Clock}
              title="Time Format"
              subtitle="12-hour or 24-hour clock"
              type="button"
              onPress={handleTimeFormatChange}
              rightText={localSettings.timeFormat}
            />
          </View>

          {/* Appearance */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Appearance</Text>
            
            <SettingItem
              icon={Palette}
              title="Theme"
              subtitle="Light, dark, or system"
              type="button"
              onPress={handleThemeChange}
              rightText={localSettings.theme}
            />
            
            <SettingItem
              icon={Type}
              title="Font Size"
              subtitle="Text size throughout the app"
              type="button"
              onPress={handleFontSizeChange}
              rightText={localSettings.fontSize}
            />
            
            <SettingItem
              icon={Eye}
              title="High Contrast"
              subtitle="Increase contrast for better visibility"
              value={localSettings.highContrast}
              onToggle={(value) => handleToggle('highContrast', value)}
            />
            
            <SettingItem
              icon={Smartphone}
              title="Reduced Motion"
              subtitle="Minimize animations and transitions"
              value={localSettings.reducedMotion}
              onToggle={(value) => handleToggle('reducedMotion', value)}
            />
          </View>

          {/* Media */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Media</Text>
            
            <SettingItem
              icon={Play}
              title="Auto-play Videos"
              subtitle="Automatically play videos in feed"
              value={localSettings.autoPlayVideos}
              onToggle={(value) => handleToggle('autoPlayVideos', value)}
            />
          </View>
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  placeholder: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    marginVertical: 10,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 12,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray100,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: theme.colors.text,
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
  },
  rightText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: theme.colors.primary,
    textTransform: 'capitalize',
  },
});
