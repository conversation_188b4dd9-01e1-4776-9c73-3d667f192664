import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import * as Haptics from 'expo-haptics';
import {
  ArrowLeft,
  Play,
  CheckCircle,
  XCircle,
  Clock,
  Settings,
  Download,
  Upload,
  RotateCcw,
  Database,
  Sync,
  Search,
  Filter,
} from 'lucide-react-native';

import { theme } from '../../constants/theme';
import { useProfileStore } from '../../stores/profileStore';
import { settingsSyncService } from '../../services/settingsSync';

export default function SettingsTestSuite() {
  const router = useRouter();
  const { 
    settings, 
    updateSettings, 
    resetSettings,
    exportSettings,
    importSettings,
    createSettingsBackup,
    restoreSettingsBackup,
    getSettingsHistory,
    isUpdating,
    error 
  } = useProfileStore();
  
  const [testResults, setTestResults] = useState<Record<string, 'pending' | 'success' | 'error'>>({});
  const [isRunning, setIsRunning] = useState(false);

  const handleBack = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    router.back();
  };

  const runTest = async (testName: string, testFunction: () => Promise<void>) => {
    setTestResults(prev => ({ ...prev, [testName]: 'pending' }));
    
    try {
      await testFunction();
      setTestResults(prev => ({ ...prev, [testName]: 'success' }));
    } catch (error) {
      console.error(`Test ${testName} failed:`, error);
      setTestResults(prev => ({ ...prev, [testName]: 'error' }));
    }
  };

  const testSettingsUpdate = async () => {
    if (!settings) throw new Error('Settings not available');
    
    const originalValue = settings.notifications.pushNotifications;
    const testValue = !originalValue;
    
    await updateSettings({
      notifications: {
        ...settings.notifications,
        pushNotifications: testValue,
      },
    });
    
    // Verify update
    if (settings.notifications.pushNotifications !== testValue) {
      throw new Error('Settings update failed');
    }
    
    // Restore original
    await updateSettings({
      notifications: {
        ...settings.notifications,
        pushNotifications: originalValue,
      },
    });
  };

  const testSettingsExport = async () => {
    const exportedData = await exportSettings();
    
    if (!exportedData || typeof exportedData !== 'string') {
      throw new Error('Export failed - no data returned');
    }
    
    // Verify it's valid JSON
    const parsed = JSON.parse(exportedData);
    if (!parsed.settings) {
      throw new Error('Export failed - invalid format');
    }
  };

  const testSettingsImport = async () => {
    // First export current settings
    const exportedData = await exportSettings();
    
    // Modify a setting
    const parsed = JSON.parse(exportedData);
    parsed.settings.notifications.pushNotifications = !parsed.settings.notifications.pushNotifications;
    
    // Import modified settings
    await importSettings(JSON.stringify(parsed));
    
    // Verify import worked
    if (!settings) throw new Error('Settings not available after import');
  };

  const testSettingsBackup = async () => {
    await createSettingsBackup();
    
    // Modify settings
    await updateSettings({
      notifications: {
        ...settings?.notifications,
        pushNotifications: !settings?.notifications.pushNotifications,
      },
    });
    
    // Restore backup
    await restoreSettingsBackup();
    
    // Verify restoration (this is a basic test)
    if (!settings) throw new Error('Settings not available after restore');
  };

  const testSettingsReset = async () => {
    const originalSettings = { ...settings };
    
    await resetSettings();
    
    // Verify reset occurred (settings should be different)
    if (!settings) throw new Error('Settings not available after reset');
    
    // Note: In a real test, we'd verify specific default values
  };

  const testSettingsSync = async () => {
    // Test the sync service directly
    const testSettings = {
      ...settings,
      notifications: {
        ...settings?.notifications,
        pushNotifications: !settings?.notifications.pushNotifications,
      },
    };
    
    await settingsSyncService.updateSettings(testSettings, 'test');
    
    const syncedSettings = await settingsSyncService.getSettings();
    if (!syncedSettings) {
      throw new Error('Settings sync failed');
    }
  };

  const testSettingsHistory = async () => {
    const history = await getSettingsHistory();
    
    if (!Array.isArray(history)) {
      throw new Error('Settings history not available');
    }
    
    // History should have at least one entry after previous tests
    if (history.length === 0) {
      console.warn('Settings history is empty - this may be expected for new installations');
    }
  };

  const testSearchAndFilter = async () => {
    // Test search functionality (this would test the search component)
    const searchTerms = ['notification', 'privacy', 'age', 'distance'];
    
    for (const term of searchTerms) {
      // In a real implementation, this would test the search functionality
      console.log(`Testing search for: ${term}`);
    }
    
    // Test filter functionality
    const categories = ['Dating Preferences', 'Privacy & Safety', 'Notifications'];
    
    for (const category of categories) {
      // In a real implementation, this would test the filter functionality
      console.log(`Testing filter for: ${category}`);
    }
  };

  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults({});
    
    const tests = [
      { name: 'Settings Update', test: testSettingsUpdate },
      { name: 'Settings Export', test: testSettingsExport },
      { name: 'Settings Import', test: testSettingsImport },
      { name: 'Settings Backup', test: testSettingsBackup },
      { name: 'Settings Reset', test: testSettingsReset },
      { name: 'Settings Sync', test: testSettingsSync },
      { name: 'Settings History', test: testSettingsHistory },
      { name: 'Search & Filter', test: testSearchAndFilter },
    ];
    
    for (const { name, test } of tests) {
      await runTest(name, test);
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    setIsRunning(false);
    
    const results = Object.values(testResults);
    const successCount = results.filter(r => r === 'success').length;
    const totalCount = results.length;
    
    Alert.alert(
      'Test Results',
      `${successCount}/${totalCount} tests passed`,
      [{ text: 'OK' }]
    );
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle size={20} color={theme.colors.success} />;
      case 'error':
        return <XCircle size={20} color={theme.colors.error} />;
      case 'pending':
        return <Clock size={20} color={theme.colors.warning} />;
      default:
        return <Clock size={20} color={theme.colors.gray400} />;
    }
  };

  const TestItem = ({ 
    icon: Icon, 
    title, 
    description, 
    testName, 
    onPress 
  }: {
    icon: any;
    title: string;
    description: string;
    testName: string;
    onPress: () => void;
  }) => (
    <TouchableOpacity style={styles.testItem} onPress={onPress}>
      <View style={styles.testLeft}>
        <View style={styles.testIcon}>
          <Icon size={20} color={theme.colors.primary} />
        </View>
        <View style={styles.testContent}>
          <Text style={styles.testTitle}>{title}</Text>
          <Text style={styles.testDescription}>{description}</Text>
        </View>
      </View>
      {getStatusIcon(testResults[testName])}
    </TouchableOpacity>
  );

  return (
    <LinearGradient colors={['#8B5CF6', '#EC4899']} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <ArrowLeft size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Settings Test Suite</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Run All Tests */}
          <View style={styles.section}>
            <TouchableOpacity 
              style={[styles.runAllButton, isRunning && styles.runAllButtonDisabled]} 
              onPress={runAllTests}
              disabled={isRunning}
            >
              <Play size={20} color="white" />
              <Text style={styles.runAllButtonText}>
                {isRunning ? 'Running Tests...' : 'Run All Tests'}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Individual Tests */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Core Functionality Tests</Text>
            
            <TestItem
              icon={Settings}
              title="Settings Update"
              description="Test settings modification and persistence"
              testName="Settings Update"
              onPress={() => runTest('Settings Update', testSettingsUpdate)}
            />
            
            <TestItem
              icon={Download}
              title="Settings Export"
              description="Test settings export functionality"
              testName="Settings Export"
              onPress={() => runTest('Settings Export', testSettingsExport)}
            />
            
            <TestItem
              icon={Upload}
              title="Settings Import"
              description="Test settings import functionality"
              testName="Settings Import"
              onPress={() => runTest('Settings Import', testSettingsImport)}
            />
            
            <TestItem
              icon={Database}
              title="Settings Backup"
              description="Test backup and restore functionality"
              testName="Settings Backup"
              onPress={() => runTest('Settings Backup', testSettingsBackup)}
            />
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Advanced Features Tests</Text>
            
            <TestItem
              icon={RotateCcw}
              title="Settings Reset"
              description="Test reset to default values"
              testName="Settings Reset"
              onPress={() => runTest('Settings Reset', testSettingsReset)}
            />
            
            <TestItem
              icon={Sync}
              title="Settings Sync"
              description="Test real-time synchronization"
              testName="Settings Sync"
              onPress={() => runTest('Settings Sync', testSettingsSync)}
            />
            
            <TestItem
              icon={Database}
              title="Settings History"
              description="Test change history tracking"
              testName="Settings History"
              onPress={() => runTest('Settings History', testSettingsHistory)}
            />
            
            <TestItem
              icon={Search}
              title="Search & Filter"
              description="Test search and filter functionality"
              testName="Search & Filter"
              onPress={() => runTest('Search & Filter', testSearchAndFilter)}
            />
          </View>

          {/* Current State */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Current State</Text>
            <View style={styles.stateContainer}>
              <Text style={styles.stateText}>Settings Loaded: {settings ? '✅' : '❌'}</Text>
              <Text style={styles.stateText}>Updating: {isUpdating ? '⏳' : '✅'}</Text>
              <Text style={styles.stateText}>Error: {error ? '❌' : '✅'}</Text>
              {error && (
                <Text style={styles.errorText}>Error: {error}</Text>
              )}
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  placeholder: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    marginVertical: 10,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 12,
  },
  runAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.primary,
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    margin: 20,
  },
  runAllButtonDisabled: {
    backgroundColor: theme.colors.gray400,
  },
  runAllButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
    marginLeft: 8,
  },
  testItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray100,
  },
  testLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  testIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  testContent: {
    flex: 1,
  },
  testTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 2,
  },
  testDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
  },
  stateContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  stateText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text,
    marginBottom: 4,
  },
  errorText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: theme.colors.error,
    marginTop: 8,
  },
});
