import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Alert,
  Platform,
} from 'react-native';
import { Camera, Image as ImageIcon, FileText, X } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';

// Platform-specific import for document picker
let DocumentPicker: any = null;
if (Platform.OS !== 'web') {
  DocumentPicker = require('expo-document-picker');
}

interface FileAttachmentModalProps {
  visible: boolean;
  onClose: () => void;
  onFileSelect: (file: any, type: 'image' | 'file') => void;
}

export default function FileAttachmentModal({
  visible,
  onClose,
  onFileSelect
}: FileAttachmentModalProps) {
  const handleCameraPress = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission needed', 'Camera permission is required to take photos');
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled) {
      onFileSelect(result.assets[0], 'image');
    }
  };

  const handleGalleryPress = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled) {
      onFileSelect(result.assets[0], 'image');
    }
  };

  const handleDocumentPress = async () => {
    if (Platform.OS === 'web') {
      Alert.alert('Not supported', 'Document selection is not available on web platform');
      return;
    }

    if (!DocumentPicker) {
      Alert.alert('Error', 'Document picker is not available');
      return;
    }

    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        copyToCacheDirectory: true,
      });

      if (!result.canceled) {
        onFileSelect(result.assets[0], 'file');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to select document');
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.modal}>
          <View style={styles.header}>
            <Text style={styles.title}>Send attachment</Text>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <X size={24} color="#6B7280" />
            </TouchableOpacity>
          </View>

          <View style={styles.options}>
            <TouchableOpacity style={styles.option} onPress={handleCameraPress}>
              <View style={[styles.optionIcon, { backgroundColor: '#EF4444' }]}>
                <Camera size={24} color="white" />
              </View>
              <Text style={styles.optionText}>Camera</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.option} onPress={handleGalleryPress}>
              <View style={[styles.optionIcon, { backgroundColor: '#8B5CF6' }]}>
                <ImageIcon size={24} color="white" />
              </View>
              <Text style={styles.optionText}>Gallery</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={[
                styles.option, 
                Platform.OS === 'web' && styles.disabledOption
              ]} 
              onPress={handleDocumentPress}
              disabled={Platform.OS === 'web'}
            >
              <View style={[
                styles.optionIcon, 
                { backgroundColor: Platform.OS === 'web' ? '#9CA3AF' : '#10B981' }
              ]}>
                <FileText size={24} color="white" />
              </View>
              <Text style={[
                styles.optionText,
                Platform.OS === 'web' && styles.disabledText
              ]}>
                Document
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modal: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 34,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
  },
  closeButton: {
    padding: 4,
  },
  options: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    paddingTop: 24,
  },
  option: {
    alignItems: 'center',
    gap: 12,
  },
  disabledOption: {
    opacity: 0.5,
  },
  optionIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  optionText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  disabledText: {
    color: '#9CA3AF',
  },
});