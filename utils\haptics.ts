import { Platform } from 'react-native';
import * as Haptics from 'expo-haptics';

/**
 * Safe haptic feedback utility that works across all platforms
 * Web doesn't support haptics, so we gracefully handle it
 */
export const triggerHaptic = {
  light: () => {
    if (Platform.OS !== 'web') {
      try {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      } catch (error) {
        // Silently fail on platforms that don't support haptics
        console.log('Haptics not supported:', error);
      }
    }
  },
  
  medium: () => {
    if (Platform.OS !== 'web') {
      try {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      } catch (error) {
        console.log('Haptics not supported:', error);
      }
    }
  },
  
  heavy: () => {
    if (Platform.OS !== 'web') {
      try {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
      } catch (error) {
        console.log('Haptics not supported:', error);
      }
    }
  },
  
  selection: () => {
    if (Platform.OS !== 'web') {
      try {
        Haptics.selectionAsync();
      } catch (error) {
        console.log('Haptics not supported:', error);
      }
    }
  },
  
  notification: (type: 'success' | 'warning' | 'error' = 'success') => {
    if (Platform.OS !== 'web') {
      try {
        const notificationType = type === 'success' 
          ? Haptics.NotificationFeedbackType.Success
          : type === 'warning'
          ? Haptics.NotificationFeedbackType.Warning
          : Haptics.NotificationFeedbackType.Error;
        
        Haptics.notificationAsync(notificationType);
      } catch (error) {
        console.log('Haptics not supported:', error);
      }
    }
  }
};

export default triggerHaptic;
