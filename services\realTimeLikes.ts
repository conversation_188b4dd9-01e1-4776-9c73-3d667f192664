import { WebSocketService } from './websocket';
import { useLikesStore } from '@/stores/likesStore';
import { LikeProfile, Match } from '@/types/messaging';
import * as Haptics from 'expo-haptics';
import { Platform } from 'react-native';

export class RealTimeLikesManager {
  private wsService: WebSocketService | null = null;
  private notificationService: any = null;
  private isInitialized = false;

  constructor(userId: string) {
    this.initialize(userId);
  }

  private async initialize(userId: string) {
    try {
      // Initialize WebSocket service
      this.wsService = new WebSocketService(userId);
      
      // Setup event listeners
      this.setupEventListeners();
      
      this.isInitialized = true;
      console.log('RealTimeLikesManager initialized successfully');
    } catch (error) {
      console.error('Failed to initialize RealTimeLikesManager:', error);
    }
  }

  private setupEventListeners() {
    if (!this.wsService) return;

    // Listen for new likes received
    this.wsService.on('newLike', this.handleNewLike.bind(this));
    
    // Listen for new matches
    this.wsService.on('newMatch', this.handleNewMatch.bind(this));
    
    // Listen for like back responses
    this.wsService.on('likeBack', this.handleLikeBack.bind(this));
    
    // Connection status
    this.wsService.on('connected', this.handleConnected.bind(this));
    this.wsService.on('disconnected', this.handleDisconnected.bind(this));
    this.wsService.on('error', this.handleError.bind(this));
  }

  private handleNewLike(likeData: LikeProfile) {
    console.log('New like received:', likeData);
    
    // Add to likes store
    const { addNewLike } = useLikesStore.getState();
    addNewLike(likeData);
    
    // Trigger haptic feedback
    this.triggerHapticFeedback('light');
    
    // Send notification
    this.sendNotification({
      type: 'like',
      title: 'New Like! 💕',
      body: `${likeData.name} liked you!`,
      userId: likeData.id,
    });
  }

  private handleNewMatch(matchData: Match) {
    console.log('New match created:', matchData);
    
    // Add to matches store
    const { addNewMatch } = useLikesStore.getState();
    addNewMatch(matchData);
    
    // Trigger success haptic feedback
    this.triggerHapticFeedback('success');
    
    // Send match notification
    this.sendNotification({
      type: 'match',
      title: "It's a Match! 🎉",
      body: 'You have a new match! Start chatting now.',
      userId: matchData.id,
    });
  }

  private handleLikeBack(data: { isMatch: boolean; match?: Match; userId: string }) {
    console.log('Like back response:', data);
    
    if (data.isMatch && data.match) {
      this.handleNewMatch(data.match);
    }
    
    // Trigger haptic feedback
    this.triggerHapticFeedback(data.isMatch ? 'success' : 'medium');
  }

  private handleConnected() {
    console.log('RealTimeLikesManager: WebSocket connected');
    // Optionally sync any pending likes or matches
  }

  private handleDisconnected() {
    console.log('RealTimeLikesManager: WebSocket disconnected');
    // Handle offline state
  }

  private handleError(error: any) {
    console.error('RealTimeLikesManager: WebSocket error:', error);
    // Handle connection errors
  }

  private triggerHapticFeedback(type: 'light' | 'medium' | 'heavy' | 'success') {
    if (Platform.OS === 'web') {
      console.log(`🔊 Haptic feedback: ${type} (web - no haptic)`);
      return;
    }

    try {
      switch (type) {
        case 'light':
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          break;
        case 'medium':
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          break;
        case 'heavy':
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
          break;
        case 'success':
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          break;
      }
    } catch (error) {
      console.warn('Haptic feedback failed:', error);
    }
  }

  private sendNotification(data: {
    type: 'like' | 'match';
    title: string;
    body: string;
    userId: string;
  }) {
    // This would integrate with your notification service
    console.log('Sending notification:', data);
    
    // For now, we'll just log it
    // In a real app, you would use expo-notifications or similar
  }

  // Public methods for sending likes
  public sendLike(userId: string, type: 'like' | 'superlike') {
    if (!this.wsService || !this.isInitialized) {
      console.warn('RealTimeLikesManager not initialized');
      return;
    }

    this.wsService.sendLike(userId, type);
  }

  public sendLikeBack(userId: string) {
    if (!this.wsService || !this.isInitialized) {
      console.warn('RealTimeLikesManager not initialized');
      return;
    }

    this.wsService.likeBack(userId);
  }

  public disconnect() {
    if (this.wsService) {
      this.wsService.disconnect();
      this.wsService = null;
    }
    this.isInitialized = false;
  }

  public isConnected(): boolean {
    return this.isInitialized && this.wsService !== null;
  }
}

// Singleton instance
let realTimeLikesManager: RealTimeLikesManager | null = null;

export const getRealTimeLikesManager = (userId?: string): RealTimeLikesManager | null => {
  if (!realTimeLikesManager && userId) {
    realTimeLikesManager = new RealTimeLikesManager(userId);
  }
  return realTimeLikesManager;
};

export const disconnectRealTimeLikes = () => {
  if (realTimeLikesManager) {
    realTimeLikesManager.disconnect();
    realTimeLikesManager = null;
  }
};
