import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  ScrollView,
  Alert,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolateColor,
} from 'react-native-reanimated';
import { Languages, Settings, Globe, Check, X } from 'lucide-react-native';
import { useTranslation } from '@/hooks/useTranslation';
import { theme } from '@/constants/theme';
import * as Haptics from 'expo-haptics';
import { Platform } from 'react-native';

interface TranslationButtonProps {
  onTranslationToggle?: (enabled: boolean) => void;
}

export default function TranslationButton({ onTranslationToggle }: TranslationButtonProps) {
  const [showSettings, setShowSettings] = useState(false);
  const {
    settings,
    toggleAutoTranslate,
    setTargetLanguage,
    toggleShowOriginal,
    getSupportedLanguages,
    error,
    clearError,
  } = useTranslation();

  const scale = useSharedValue(1);
  const rotation = useSharedValue(0);
  const colorProgress = useSharedValue(settings.autoTranslate ? 1 : 0);

  const animatedButtonStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: scale.value },
        { rotate: `${rotation.value}deg` },
      ],
      backgroundColor: interpolateColor(
        colorProgress.value,
        [0, 1],
        [theme.colors.gray300, theme.colors.primary]
      ),
    };
  });

  const animatedIconStyle = useAnimatedStyle(() => {
    return {
      transform: [{ rotate: `${rotation.value}deg` }],
    };
  });

  const handlePress = async () => {
    // Haptic feedback
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }

    // Animation
    scale.value = withSpring(0.9, { duration: 100 }, () => {
      scale.value = withSpring(1);
    });
    
    rotation.value = withTiming(rotation.value + 180, { duration: 300 });

    try {
      await toggleAutoTranslate();
      colorProgress.value = withTiming(settings.autoTranslate ? 0 : 1, { duration: 300 });
      
      if (onTranslationToggle) {
        onTranslationToggle(!settings.autoTranslate);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to toggle translation');
    }
  };

  const handleSettingsPress = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    setShowSettings(true);
  };

  const handleLanguageSelect = async (languageCode: string) => {
    try {
      await setTargetLanguage(languageCode);
      setShowSettings(false);
      
      if (Platform.OS !== 'web') {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to change language');
    }
  };

  const supportedLanguages = getSupportedLanguages();

  return (
    <View style={styles.container}>
      {/* Translation Toggle Button */}
      <Animated.View style={[styles.button, animatedButtonStyle]}>
        <TouchableOpacity
          onPress={handlePress}
          style={styles.buttonTouchable}
          activeOpacity={0.8}
        >
          <Animated.View style={animatedIconStyle}>
            <Languages 
              size={20} 
              color={settings.autoTranslate ? 'white' : theme.colors.gray600} 
            />
          </Animated.View>
        </TouchableOpacity>
      </Animated.View>

      {/* Settings Button */}
      <TouchableOpacity
        onPress={handleSettingsPress}
        style={styles.settingsButton}
        activeOpacity={0.7}
      >
        <Settings size={16} color={theme.colors.gray500} />
      </TouchableOpacity>

      {/* Settings Modal */}
      <Modal
        visible={showSettings}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowSettings(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Translation Settings</Text>
            <TouchableOpacity
              onPress={() => setShowSettings(false)}
              style={styles.closeButton}
            >
              <X size={24} color={theme.colors.gray600} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
            {/* Auto Translate Toggle */}
            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <Text style={styles.settingTitle}>Auto Translate</Text>
                <Text style={styles.settingDescription}>
                  Automatically translate incoming messages
                </Text>
              </View>
              <TouchableOpacity
                onPress={toggleAutoTranslate}
                style={[
                  styles.toggle,
                  settings.autoTranslate && styles.toggleActive,
                ]}
              >
                <View
                  style={[
                    styles.toggleThumb,
                    settings.autoTranslate && styles.toggleThumbActive,
                  ]}
                />
              </TouchableOpacity>
            </View>

            {/* Show Original Toggle */}
            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <Text style={styles.settingTitle}>Show Original</Text>
                <Text style={styles.settingDescription}>
                  Display original text alongside translation
                </Text>
              </View>
              <TouchableOpacity
                onPress={toggleShowOriginal}
                style={[
                  styles.toggle,
                  settings.showOriginal && styles.toggleActive,
                ]}
              >
                <View
                  style={[
                    styles.toggleThumb,
                    settings.showOriginal && styles.toggleThumbActive,
                  ]}
                />
              </TouchableOpacity>
            </View>

            {/* Target Language Selection */}
            <View style={styles.languageSection}>
              <Text style={styles.sectionTitle}>Target Language</Text>
              <Text style={styles.sectionDescription}>
                Choose your preferred language for translations
              </Text>

              <View style={styles.languageGrid}>
                {Object.entries(supportedLanguages).map(([code, name]) => (
                  <TouchableOpacity
                    key={code}
                    onPress={() => handleLanguageSelect(code)}
                    style={[
                      styles.languageItem,
                      settings.targetLanguage === code && styles.languageItemActive,
                    ]}
                  >
                    <Globe 
                      size={16} 
                      color={
                        settings.targetLanguage === code 
                          ? 'white' 
                          : theme.colors.gray600
                      } 
                    />
                    <Text
                      style={[
                        styles.languageText,
                        settings.targetLanguage === code && styles.languageTextActive,
                      ]}
                    >
                      {name}
                    </Text>
                    {settings.targetLanguage === code && (
                      <Check size={16} color="white" />
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Error Display */}
            {error && (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>{error}</Text>
                <TouchableOpacity onPress={clearError} style={styles.errorDismiss}>
                  <Text style={styles.errorDismissText}>Dismiss</Text>
                </TouchableOpacity>
              </View>
            )}
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  button: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  buttonTouchable: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
  },
  settingsButton: {
    padding: 8,
    borderRadius: 16,
    backgroundColor: theme.colors.gray100,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray200,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  closeButton: {
    padding: 4,
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray100,
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  toggle: {
    width: 50,
    height: 30,
    borderRadius: 15,
    backgroundColor: theme.colors.gray300,
    justifyContent: 'center',
    paddingHorizontal: 2,
  },
  toggleActive: {
    backgroundColor: theme.colors.primary,
  },
  toggleThumb: {
    width: 26,
    height: 26,
    borderRadius: 13,
    backgroundColor: 'white',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  toggleThumbActive: {
    transform: [{ translateX: 20 }],
  },
  languageSection: {
    marginTop: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: 16,
  },
  languageGrid: {
    gap: 8,
  },
  languageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    backgroundColor: theme.colors.gray100,
    gap: 8,
  },
  languageItemActive: {
    backgroundColor: theme.colors.primary,
  },
  languageText: {
    flex: 1,
    fontSize: 16,
    color: theme.colors.text,
  },
  languageTextActive: {
    color: 'white',
  },
  errorContainer: {
    marginTop: 16,
    padding: 12,
    backgroundColor: theme.colors.error + '20',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.error,
  },
  errorText: {
    fontSize: 14,
    color: theme.colors.error,
    marginBottom: 8,
  },
  errorDismiss: {
    alignSelf: 'flex-start',
  },
  errorDismissText: {
    fontSize: 14,
    color: theme.colors.error,
    fontWeight: '600',
  },
});
