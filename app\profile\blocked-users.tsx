import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Platform,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import * as Haptics from 'expo-haptics';
import {
  ArrowLeft,
  UserX,
  User,
  MoreVertical,
  AlertTriangle,
  Shield,
} from 'lucide-react-native';

import { theme } from '../../constants/theme';
import { useProfileStore } from '../../stores/profileStore';
import LoadingSkeleton from '../../components/LoadingSkeleton';

export default function BlockedUsersScreen() {
  const router = useRouter();
  const { profile, isLoading } = useProfileStore();
  
  // Mock blocked users data
  const [blockedUsers] = useState([
    {
      id: '1',
      name: '<PERSON>',
      photo: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      blockedAt: new Date('2024-01-15'),
      reason: 'Inappropriate behavior',
    },
    {
      id: '2',
      name: 'Mike <PERSON>',
      photo: undefined,
      blockedAt: new Date('2024-01-10'),
      reason: 'Spam messages',
    },
  ]);

  const handleBack = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    router.back();
  };

  const handleUnblockUser = (userId: string, userName: string) => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    Alert.alert(
      'Unblock User',
      `Are you sure you want to unblock ${userName}? They will be able to see your profile and send you messages again.`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Unblock', 
          style: 'destructive',
          onPress: () => {
            // In production, this would call an API to unblock the user
            Alert.alert('User Unblocked', `${userName} has been unblocked.`);
          }
        },
      ]
    );
  };

  const handleUserOptions = (userId: string, userName: string) => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    Alert.alert(
      userName,
      'Choose an action',
      [
        { text: 'Unblock', onPress: () => handleUnblockUser(userId, userName) },
        { text: 'Report User', onPress: () => handleReportUser(userId, userName) },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const handleReportUser = (userId: string, userName: string) => {
    Alert.alert(
      'Report User',
      `Report ${userName} for additional violations?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Report', 
          onPress: () => {
            Alert.alert('Report Submitted', 'Thank you for your report. We will review it shortly.');
          }
        },
      ]
    );
  };

  const handleBlockNewUser = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    Alert.alert(
      'Block User',
      'To block a user, go to their profile and tap the menu button, then select "Block User".',
      [{ text: 'OK' }]
    );
  };

  if (isLoading) {
    return (
      <LinearGradient colors={['#8B5CF6', '#EC4899']} style={styles.container}>
        <SafeAreaView style={styles.safeArea}>
          <LoadingSkeleton />
        </SafeAreaView>
      </LinearGradient>
    );
  }

  const BlockedUserItem = ({ user }: { user: any }) => (
    <View style={styles.userItem}>
      <View style={styles.userLeft}>
        <View style={styles.userImageContainer}>
          {user.photo ? (
            <Image source={{ uri: user.photo }} style={styles.userImage} />
          ) : (
            <View style={styles.placeholderImage}>
              <User size={24} color={theme.colors.gray400} />
            </View>
          )}
        </View>
        <View style={styles.userInfo}>
          <Text style={styles.userName}>{user.name}</Text>
          <Text style={styles.blockDate}>
            Blocked {user.blockedAt.toLocaleDateString()}
          </Text>
          {user.reason && (
            <Text style={styles.blockReason}>Reason: {user.reason}</Text>
          )}
        </View>
      </View>
      <TouchableOpacity 
        style={styles.optionsButton}
        onPress={() => handleUserOptions(user.id, user.name)}
      >
        <MoreVertical size={20} color={theme.colors.gray400} />
      </TouchableOpacity>
    </View>
  );

  return (
    <LinearGradient colors={['#8B5CF6', '#EC4899']} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <ArrowLeft size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Blocked Users</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          {/* Info Section */}
          <View style={styles.section}>
            <View style={styles.infoContainer}>
              <Shield size={24} color={theme.colors.primary} />
              <Text style={styles.infoTitle}>Blocked Users</Text>
              <Text style={styles.infoText}>
                Users you've blocked cannot see your profile, send you messages, or interact with you in any way.
              </Text>
            </View>
          </View>

          {/* Blocked Users List */}
          {blockedUsers.length > 0 ? (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>
                {blockedUsers.length} Blocked User{blockedUsers.length !== 1 ? 's' : ''}
              </Text>
              {blockedUsers.map((user) => (
                <BlockedUserItem key={user.id} user={user} />
              ))}
            </View>
          ) : (
            <View style={styles.section}>
              <View style={styles.emptyContainer}>
                <UserX size={48} color={theme.colors.gray400} />
                <Text style={styles.emptyTitle}>No Blocked Users</Text>
                <Text style={styles.emptyText}>
                  You haven't blocked anyone yet. When you block someone, they'll appear here.
                </Text>
              </View>
            </View>
          )}

          {/* Actions */}
          <View style={styles.section}>
            <TouchableOpacity style={styles.actionButton} onPress={handleBlockNewUser}>
              <UserX size={20} color={theme.colors.primary} />
              <Text style={styles.actionButtonText}>How to Block Someone</Text>
            </TouchableOpacity>
          </View>

          {/* Safety Tips */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Safety Tips</Text>
            <View style={styles.tipContainer}>
              <AlertTriangle size={16} color={theme.colors.warning} />
              <Text style={styles.tipText}>
                If someone is harassing you or making you feel unsafe, block them immediately and report their behavior.
              </Text>
            </View>
            <View style={styles.tipContainer}>
              <Shield size={16} color={theme.colors.success} />
              <Text style={styles.tipText}>
                Blocking is private - the other person won't be notified that you've blocked them.
              </Text>
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
  },
  placeholder: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    marginVertical: 10,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 12,
  },
  infoContainer: {
    alignItems: 'center',
    paddingVertical: 30,
    paddingHorizontal: 20,
  },
  infoTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginTop: 12,
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
    textAlign: 'center',
    lineHeight: 20,
  },
  userItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray100,
  },
  userLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  userImageContainer: {
    marginRight: 12,
  },
  userImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  placeholderImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: theme.colors.gray200,
    justifyContent: 'center',
    alignItems: 'center',
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginBottom: 2,
  },
  blockDate: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
    marginBottom: 2,
  },
  blockReason: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray500,
  },
  optionsButton: {
    padding: 8,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  emptyTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
    textAlign: 'center',
    lineHeight: 20,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  actionButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: theme.colors.primary,
    marginLeft: 12,
  },
  tipContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  tipText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text,
    marginLeft: 12,
    flex: 1,
    lineHeight: 20,
  },
});
