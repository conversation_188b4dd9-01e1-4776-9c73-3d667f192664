import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Platform,
} from 'react-native';
import {
  Sync,
  CheckCircle,
  AlertTriangle,
  Clock,
  Wifi,
  WifiOff,
  Database,
  RefreshCw,
} from 'lucide-react-native';
import { triggerHaptic } from '../../utils/haptics';

import { theme } from '../../constants/theme';
import { useProfileStore } from '../../stores/profileStore';
import { settingsSyncService } from '../../services/settingsSync';

interface SettingsSyncManagerProps {
  onSyncStatusChange?: (status: 'synced' | 'syncing' | 'error' | 'offline') => void;
}

export default function SettingsSyncManager({ onSyncStatusChange }: SettingsSyncManagerProps) {
  const { settings, updateSettings } = useProfileStore();
  const [syncStatus, setSyncStatus] = useState<'synced' | 'syncing' | 'error' | 'offline'>('synced');
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);
  const [isOnline, setIsOnline] = useState(true);
  const [pendingChanges, setPendingChanges] = useState(0);

  useEffect(() => {
    // Initialize sync service listener
    const unsubscribe = settingsSyncService.addListener({
      id: 'settings-sync-manager',
      callback: (updatedSettings) => {
        // Handle settings updates from sync service
        console.log('Settings updated via sync:', updatedSettings);
        setLastSyncTime(new Date());
        setSyncStatus('synced');
        setPendingChanges(0);
      },
    });

    // Simulate network status monitoring
    // In production, use NetInfo or similar
    const networkInterval = setInterval(() => {
      // Simulate occasional network issues
      const online = Math.random() > 0.1; // 90% uptime
      setIsOnline(online);
      setSyncStatus(online ? 'synced' : 'offline');
    }, 10000);

    return () => {
      unsubscribe();
      clearInterval(networkInterval);
    };
  }, []);

  useEffect(() => {
    onSyncStatusChange?.(syncStatus);
  }, [syncStatus, onSyncStatusChange]);

  const handleManualSync = async () => {
    triggerHaptic.medium();

    setSyncStatus('syncing');

    try {
      if (settings) {
        await settingsSyncService.updateSettings(settings, 'manual-sync');
        setLastSyncTime(new Date());
        setSyncStatus('synced');
        setPendingChanges(0);
        
        Alert.alert('Sync Complete', 'Your settings have been synchronized successfully.');
      }
    } catch (error) {
      setSyncStatus('error');
      Alert.alert('Sync Failed', 'Failed to synchronize settings. Please try again.');
    }
  };

  const handleViewHistory = async () => {
    triggerHaptic.light();

    try {
      const history = await settingsSyncService.getSettingsHistory();
      
      if (history.length === 0) {
        Alert.alert('Settings History', 'No settings changes recorded yet.');
        return;
      }

      const recentChanges = history.slice(0, 5);
      const historyText = recentChanges
        .map((entry, index) => 
          `${index + 1}. ${entry.source} - ${new Date(entry.timestamp).toLocaleString()}`
        )
        .join('\n');

      Alert.alert(
        'Recent Settings Changes',
        historyText,
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to load settings history.');
    }
  };

  const handleCreateBackup = async () => {
    triggerHaptic.medium();

    try {
      await settingsSyncService.createBackup();
      Alert.alert('Backup Created', 'Your settings have been backed up successfully.');
    } catch (error) {
      Alert.alert('Backup Failed', 'Failed to create settings backup.');
    }
  };

  const handleRestoreBackup = () => {
    triggerHaptic.heavy();

    Alert.alert(
      'Restore Settings',
      'Are you sure you want to restore your settings from the last backup? This will overwrite your current settings.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Restore', 
          style: 'destructive',
          onPress: async () => {
            try {
              await settingsSyncService.restoreFromBackup();
              Alert.alert('Settings Restored', 'Your settings have been restored from backup.');
            } catch (error) {
              Alert.alert('Restore Failed', 'Failed to restore settings from backup.');
            }
          }
        },
      ]
    );
  };

  const getSyncStatusIcon = () => {
    switch (syncStatus) {
      case 'synced':
        return <CheckCircle size={20} color={theme.colors.success} />;
      case 'syncing':
        return <RefreshCw size={20} color={theme.colors.primary} />;
      case 'error':
        return <AlertTriangle size={20} color={theme.colors.error} />;
      case 'offline':
        return <WifiOff size={20} color={theme.colors.gray400} />;
      default:
        return <Clock size={20} color={theme.colors.gray400} />;
    }
  };

  const getSyncStatusText = () => {
    switch (syncStatus) {
      case 'synced':
        return 'All settings synchronized';
      case 'syncing':
        return 'Synchronizing settings...';
      case 'error':
        return 'Sync failed - tap to retry';
      case 'offline':
        return 'Offline - will sync when online';
      default:
        return 'Unknown status';
    }
  };

  const getSyncStatusColor = () => {
    switch (syncStatus) {
      case 'synced':
        return theme.colors.success;
      case 'syncing':
        return theme.colors.primary;
      case 'error':
        return theme.colors.error;
      case 'offline':
        return theme.colors.gray400;
      default:
        return theme.colors.gray400;
    }
  };

  return (
    <View style={styles.container}>
      {/* Sync Status */}
      <TouchableOpacity 
        style={styles.syncStatus} 
        onPress={syncStatus === 'error' ? handleManualSync : undefined}
      >
        <View style={styles.syncStatusLeft}>
          {getSyncStatusIcon()}
          <View style={styles.syncStatusContent}>
            <Text style={[styles.syncStatusText, { color: getSyncStatusColor() }]}>
              {getSyncStatusText()}
            </Text>
            {lastSyncTime && (
              <Text style={styles.lastSyncText}>
                Last sync: {lastSyncTime.toLocaleTimeString()}
              </Text>
            )}
          </View>
        </View>
        
        <View style={styles.syncStatusRight}>
          {isOnline ? (
            <Wifi size={16} color={theme.colors.success} />
          ) : (
            <WifiOff size={16} color={theme.colors.error} />
          )}
          {pendingChanges > 0 && (
            <View style={styles.pendingBadge}>
              <Text style={styles.pendingBadgeText}>{pendingChanges}</Text>
            </View>
          )}
        </View>
      </TouchableOpacity>

      {/* Sync Actions */}
      <View style={styles.syncActions}>
        <TouchableOpacity
          style={[styles.syncButton, syncStatus === 'syncing' && styles.syncButtonDisabled]}
          onPress={handleManualSync}
          disabled={syncStatus === 'syncing'}
        >
          <Sync size={16} color="white" />
          <Text style={styles.syncButtonText}>Sync Now</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.syncButton} onPress={handleViewHistory}>
          <Database size={16} color="white" />
          <Text style={styles.syncButtonText}>History</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.syncButton} onPress={handleCreateBackup}>
          <Database size={16} color="white" />
          <Text style={styles.syncButtonText}>Backup</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.syncButton} onPress={handleRestoreBackup}>
          <RefreshCw size={16} color="white" />
          <Text style={styles.syncButtonText}>Restore</Text>
        </TouchableOpacity>
      </View>

      {/* Sync Info */}
      <View style={styles.syncInfo}>
        <Text style={styles.syncInfoText}>
          Settings are automatically synchronized across all your devices. 
          Manual sync ensures immediate updates.
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: 'white',
    borderRadius: 12,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  syncStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: theme.colors.gray50,
    borderRadius: 8,
    marginBottom: 16,
  },
  syncStatusLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  syncStatusContent: {
    marginLeft: 12,
    flex: 1,
  },
  syncStatusText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 2,
  },
  lastSyncText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
  },
  syncStatusRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  pendingBadge: {
    backgroundColor: theme.colors.error,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  pendingBadgeText: {
    fontSize: 12,
    fontFamily: 'Inter-Bold',
    color: 'white',
  },
  syncActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  syncButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
    flex: 1,
    marginHorizontal: 2,
    justifyContent: 'center',
  },
  syncButtonDisabled: {
    backgroundColor: theme.colors.gray400,
  },
  syncButtonText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
    marginLeft: 4,
  },
  syncInfo: {
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: theme.colors.gray100,
  },
  syncInfoText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: theme.colors.gray600,
    textAlign: 'center',
    lineHeight: 16,
  },
});
