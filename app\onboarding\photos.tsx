import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Image,
  Alert,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { ArrowRight, ArrowLeft, Plus, Camera } from 'lucide-react-native';
import * as ImagePicker from 'expo-image-picker';

const { width } = Dimensions.get('window');
const PHOTO_SIZE = (width - 72) / 2;

export default function OnboardingPhotos() {
  const router = useRouter();
  const [photos, setPhotos] = useState<string[]>([]);

  const pickImage = async () => {
    if (photos.length >= 6) {
      Alert.alert('Maximum Photos', 'You can upload up to 6 photos');
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      setPhotos(prev => [...prev, result.assets[0].uri]);
    }
  };

  const removePhoto = (index: number) => {
    setPhotos(prev => prev.filter((_, i) => i !== index));
  };

  const handleContinue = () => {
    if (photos.length < 2) {
      Alert.alert('More Photos Needed', 'Please add at least 2 photos to continue');
      return;
    }
    router.push('/onboarding/interests');
  };

  return (
    <LinearGradient colors={['#8B5CF6', '#EC4899']} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <ArrowLeft size={24} color="white" />
            </TouchableOpacity>
            <Text style={styles.title}>Add your photos</Text>
            <Text style={styles.subtitle}>
              Show your best self! Add 2-6 photos that represent who you are
            </Text>
            <View style={styles.progressBar}>
              <View style={[styles.progressStep, styles.activeStep]} />
              <View style={[styles.progressStep, styles.activeStep]} />
              <View style={styles.progressStep} />
              <View style={styles.progressStep} />
              <View style={styles.progressStep} />
            </View>
          </View>

          <View style={styles.photosContainer}>
            <View style={styles.photosGrid}>
              {Array.from({ length: 6 }, (_, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.photoSlot,
                    photos[index] && styles.photoSlotFilled,
                  ]}
                  onPress={() => {
                    if (photos[index]) {
                      removePhoto(index);
                    } else {
                      pickImage();
                    }
                  }}
                >
                  {photos[index] ? (
                    <Image source={{ uri: photos[index] }} style={styles.photo} />
                  ) : (
                    <View style={styles.emptyPhotoSlot}>
                      {index === 0 ? (
                        <>
                          <Camera size={24} color="white" />
                          <Text style={styles.emptyPhotoText}>
                            {index === 0 ? 'Main Photo' : 'Add Photo'}
                          </Text>
                        </>
                      ) : (
                        <>
                          <Plus size={24} color="rgba(255, 255, 255, 0.7)" />
                          <Text style={styles.emptyPhotoTextSecondary}>Add Photo</Text>
                        </>
                      )}
                    </View>
                  )}
                  {index === 0 && photos[index] && (
                    <View style={styles.mainPhotoBadge}>
                      <Text style={styles.mainPhotoBadgeText}>MAIN</Text>
                    </View>
                  )}
                </TouchableOpacity>
              ))}
            </View>

            <View style={styles.tips}>
              <Text style={styles.tipsTitle}>Photo Tips</Text>
              <View style={styles.tipsList}>
                <Text style={styles.tipItem}>• Use recent photos that show your face clearly</Text>
                <Text style={styles.tipItem}>• Smile and look confident</Text>
                <Text style={styles.tipItem}>• Include photos of your hobbies and interests</Text>
                <Text style={styles.tipItem}>• Avoid group photos or sunglasses</Text>
              </View>
            </View>
          </View>
        </ScrollView>

        <View style={styles.footer}>
          <TouchableOpacity
            style={[styles.continueButton, photos.length < 2 && styles.continueButtonDisabled]}
            onPress={handleContinue}
            disabled={photos.length < 2}
          >
            <Text style={styles.continueButtonText}>
              Continue ({photos.length}/6 photos)
            </Text>
            <ArrowRight size={20} color="#8B5CF6" />
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 24,
  },
  header: {
    paddingTop: 20,
    marginBottom: 32,
  },
  backButton: {
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Poppins-Bold',
    color: 'white',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.8)',
    lineHeight: 24,
    marginBottom: 24,
  },
  progressBar: {
    flexDirection: 'row',
    gap: 8,
  },
  progressStep: {
    flex: 1,
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
  },
  activeStep: {
    backgroundColor: 'white',
  },
  photosContainer: {
    flex: 1,
    paddingBottom: 20,
  },
  photosGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 32,
  },
  photoSlot: {
    width: PHOTO_SIZE,
    height: PHOTO_SIZE,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    borderStyle: 'dashed',
    position: 'relative',
  },
  photoSlotFilled: {
    borderStyle: 'solid',
    borderColor: 'white',
  },
  photo: {
    width: '100%',
    height: '100%',
    borderRadius: 10,
  },
  emptyPhotoSlot: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
  },
  emptyPhotoText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
    textAlign: 'center',
  },
  emptyPhotoTextSecondary: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
  },
  mainPhotoBadge: {
    position: 'absolute',
    top: 8,
    left: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  mainPhotoBadgeText: {
    fontSize: 10,
    fontFamily: 'Inter-Bold',
    color: 'white',
  },
  tips: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
  },
  tipsTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: 'white',
    marginBottom: 12,
  },
  tipsList: {
    gap: 8,
  },
  tipItem: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: 'rgba(255, 255, 255, 0.9)',
    lineHeight: 20,
  },
  footer: {
    paddingHorizontal: 24,
    paddingBottom: 32,
  },
  continueButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
    borderRadius: 12,
    paddingVertical: 16,
    gap: 8,
  },
  continueButtonDisabled: {
    opacity: 0.5,
  },
  continueButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#8B5CF6',
  },
});