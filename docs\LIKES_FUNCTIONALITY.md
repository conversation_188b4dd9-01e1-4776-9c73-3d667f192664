# Likes Functionality Documentation

## Overview

The Likes functionality is a comprehensive system that handles all aspects of user interactions in the dating app, including likes, super likes, matches, and real-time updates. This system is built with Material Design/iOS styling, smooth animations, haptic feedback, and robust offline support.

## Architecture

### Core Components

1. **Likes Store** (`stores/likesStore.ts`)
   - Zustand-based state management
   - Persistent storage with AsyncStorage
   - Offline support with pending actions
   - Real-time updates integration

2. **Services Layer**
   - `likesService.ts` - Core API interactions
   - `realTimeLikes.ts` - WebSocket real-time updates
   - `likesCache.ts` - Offline caching and sync
   - `likesNotifications.ts` - Push notifications

3. **UI Components**
   - `AnimatedLikeCard.tsx` - Individual like cards with animations
   - `AnimatedMatchCard.tsx` - Match display cards
   - `OptimizedLikesList.tsx` - Performance-optimized list
   - `LikesErrorBoundary.tsx` - Error handling

4. **Hooks**
   - `useRealTimeLikes.ts` - Real-time functionality management
   - `useNotificationPreferences.ts` - Notification settings

## Features

### ✅ Implemented Features

1. **Core Functionality**
   - Send likes and super likes
   - Receive and display incoming likes
   - Like back functionality
   - Pass/reject users
   - Match creation and management

2. **UI/UX**
   - Material Design/iOS styling
   - Smooth react-native-reanimated animations
   - Haptic feedback integration
   - Skeleton loading screens
   - Floating action buttons
   - Swipe gestures on discovery screen

3. **Real-time Updates**
   - WebSocket integration for live updates
   - Instant notification of new likes
   - Real-time match notifications
   - Connection status monitoring

4. **Offline Support**
   - Local caching of likes data
   - Pending actions queue for offline operations
   - Automatic sync when back online
   - Optimistic updates for better UX

5. **Performance**
   - Optimized FlatList rendering
   - Image caching
   - Memoized components
   - Efficient state management

6. **Error Handling**
   - Comprehensive error boundaries
   - Retry mechanisms
   - Graceful degradation
   - User-friendly error messages

7. **Admin Panel**
   - Comprehensive analytics dashboard
   - Likes monitoring and metrics
   - Top performing profiles
   - Real-time statistics

8. **Testing**
   - Unit test suite
   - Integration tests
   - Debug components for development

## Usage

### Basic Usage

```typescript
import { useLikesStore } from '@/stores/likesStore';
import { useRealTimeLikes } from '@/hooks/useRealTimeLikes';

function LikesScreen() {
  const { 
    receivedLikes, 
    matches, 
    sendLike, 
    likeBack 
  } = useLikesStore();
  
  // Initialize real-time functionality
  useRealTimeLikes();

  const handleLike = async (userId: string) => {
    const isMatch = await sendLike(userId, 'like');
    if (isMatch) {
      // Handle match UI
    }
  };

  return (
    // Your UI components
  );
}
```

### Real-time Integration

```typescript
import { getRealTimeLikesManager } from '@/services/realTimeLikes';

// Initialize real-time manager
const manager = getRealTimeLikesManager(currentUserId);

// Send real-time like
manager.sendLike(targetUserId, 'like');

// Listen for incoming likes (handled automatically by the store)
```

### Offline Support

```typescript
import { likesCacheService } from '@/services/likesCache';

// Cache data
await likesCacheService.cacheLikesData({
  receivedLikes,
  sentLikes,
  matches
});

// Add pending action when offline
await likesCacheService.addPendingAction({
  type: 'like',
  userId: targetUserId
});

// Sync when back online
await likesCacheService.syncPendingActions(syncHandler);
```

## Configuration

### Environment Variables

```env
# WebSocket configuration
WEBSOCKET_URL=ws://your-websocket-server.com
WEBSOCKET_RECONNECT_ATTEMPTS=5

# Notification configuration
EXPO_PROJECT_ID=your-expo-project-id

# Cache configuration
CACHE_EXPIRY_HOURS=24
MAX_PENDING_ACTIONS=100
```

### Notification Setup

1. Configure Expo push notifications
2. Set up notification channels (Android)
3. Request permissions on app start
4. Handle notification responses

## API Integration

### Endpoints

- `POST /api/likes/send` - Send like/super like
- `POST /api/likes/like-back` - Like back a user
- `POST /api/likes/pass` - Pass on a user
- `GET /api/likes/received` - Get received likes
- `GET /api/likes/matches` - Get matches

### WebSocket Events

- `newLike` - Incoming like received
- `newMatch` - New match created
- `likeBack` - Like back response

## Performance Optimizations

1. **List Virtualization**
   - FlatList with optimized rendering
   - Memoized components
   - Efficient key extraction

2. **Image Optimization**
   - Lazy loading
   - Caching strategies
   - Optimized image sizes

3. **State Management**
   - Selective re-renders
   - Normalized data structures
   - Efficient updates

## Testing

### Running Tests

```bash
# Run likes test suite
npm run test:likes

# Run integration tests
npm run test:integration

# Run all tests
npm test
```

### Debug Components

- `LikesServiceTest` - Test core service functionality
- `LikesTestSuite` - Comprehensive test suite
- `LikesIntegrationTest` - End-to-end integration tests

## Troubleshooting

### Common Issues

1. **Real-time not working**
   - Check WebSocket connection
   - Verify user authentication
   - Check network connectivity

2. **Notifications not appearing**
   - Verify permissions granted
   - Check notification settings
   - Ensure proper initialization

3. **Offline sync issues**
   - Check pending actions queue
   - Verify network status detection
   - Review sync error logs

### Debug Tools

Use the debug components in development:

```typescript
import LikesServiceTest from '@/components/debug/LikesServiceTest';
import LikesIntegrationTest from '@/components/debug/LikesIntegrationTest';

// Add to your debug screen
<LikesServiceTest />
<LikesIntegrationTest />
```

## Future Enhancements

### Planned Features

1. **Advanced Analytics**
   - User behavior tracking
   - A/B testing framework
   - Conversion funnel analysis

2. **AI/ML Integration**
   - Smart matching algorithms
   - Personalized recommendations
   - Spam detection

3. **Enhanced Real-time**
   - Typing indicators
   - Read receipts
   - Presence status

4. **Performance**
   - Background sync
   - Predictive caching
   - Image preloading

## Contributing

When contributing to the likes functionality:

1. Follow the established patterns
2. Add comprehensive tests
3. Update documentation
4. Consider performance implications
5. Test offline scenarios
6. Verify real-time functionality

## Support

For issues or questions:

1. Check the troubleshooting section
2. Run the debug test suites
3. Review error logs
4. Contact the development team
