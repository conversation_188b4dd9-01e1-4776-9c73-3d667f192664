import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import Skeleton from './Skeleton';

const { width } = Dimensions.get('window');
const CARD_WIDTH = (width - 48) / 2;

interface LikesSkeletonProps {
  count?: number;
}

export function LikesGridSkeleton({ count = 6 }: LikesSkeletonProps) {
  return (
    <View style={styles.grid}>
      {Array.from({ length: count }).map((_, index) => (
        <LikeCardSkeleton key={index} />
      ))}
    </View>
  );
}

export function LikeCardSkeleton() {
  return (
    <View style={styles.card}>
      <Skeleton 
        variant="rectangular" 
        width={CARD_WIDTH} 
        height={CARD_WIDTH * 1.3} 
        borderRadius={16}
      />
      <View style={styles.cardContent}>
        <Skeleton variant="text" width="70%" height={16} />
        <View style={styles.buttonSkeleton}>
          <Skeleton variant="rectangular" width={80} height={32} borderRadius={16} />
        </View>
      </View>
    </View>
  );
}

export function MatchesListSkeleton({ count = 5 }: LikesSkeletonProps) {
  return (
    <View style={styles.list}>
      {Array.from({ length: count }).map((_, index) => (
        <MatchCardSkeleton key={index} />
      ))}
    </View>
  );
}

export function MatchCardSkeleton() {
  return (
    <View style={styles.matchCard}>
      <Skeleton variant="circular" height={60} />
      <View style={styles.matchContent}>
        <View style={styles.matchHeader}>
          <Skeleton variant="text" width="40%" height={16} />
          <Skeleton variant="text" width="20%" height={12} />
        </View>
        <Skeleton variant="text" width="80%" height={14} />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
  },
  card: {
    width: CARD_WIDTH,
    marginBottom: 16,
  },
  cardContent: {
    position: 'absolute',
    bottom: 12,
    left: 12,
    right: 12,
  },
  buttonSkeleton: {
    marginTop: 8,
  },
  list: {
    paddingHorizontal: 16,
  },
  matchCard: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginBottom: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
  },
  matchContent: {
    flex: 1,
    marginLeft: 16,
  },
  matchHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
});
